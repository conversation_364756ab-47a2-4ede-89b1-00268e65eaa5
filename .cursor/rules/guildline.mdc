---
alwaysApply: true
---

# 开发协作规范 (Development Guidelines)

本文档旨在提供一套统一的开发规范，以确保代码质量、提高协作效率并保持项目的一致性。

---

## Git 工作流规范

### 1. 分支管理

所有开发工作都应在独立的分支中进行。分支命名应遵循以下约定：

-   **`feature/<description>`**: 用于开发新功能。
    -   *示例*: `feature/user-authentication`
-   **`fix/<description>`**: 用于修复 Bug。
    -   *示例*: `fix/login-button-bug`
-   **`chore/<description>`**: 用于不涉及功能或修复的杂项任务（如构建脚本、依赖更新）。
    -   *示例*: `chore/update-pnpm-dependencies`
-   **`docs/<description>`**: 用于编写或更新文档。
    -   *示例*: `docs/add-api-guidelines`

### 2. 提交信息规范 (Commit Messages)

我们采用 [Conventional Commits](https://www.conventionalcommits.org/) 规范来编写提交信息。格式为：

`type(scope): subject`

-   **`type`**: 提交类型 (如 `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`)。
-   **`scope`**: (可选) 本次提交影响的范围 (如 `api`, `ui`, `auth`)。
-   **`subject`**: 简明扼要的提交描述。

*示例*:
```
feat(api): add endpoint for user profile
fix(ui): correct button alignment on login page
docs(readme): update setup instructions
```

### 3. Pull Request (PR) 规范

-   PR 标题应清晰、有意义。
-   PR 描述中应说明“做了什么”以及“为什么这么做”。
-   在请求审查之前，确保所有自动化测试和 Lint 检查都已通过。

---

## 代码风格指南

### 前端 (`njs-investor-insight`)

-   **语言**: TypeScript
-   **格式化**: 使用项目预设的 Prettier 规则。
-   **命名**:
    -   组件文件和组件名: `PascalCase` (例如: `UserProfile.tsx`)
    -   非组件的 TS/JS 文件和变量: `camelCase` (例如: `apiClient.ts`, `const userName`)

### 后端 (`py-investor-insight`)

-   **语言**: Python
-   **格式化**: 使用 Black。
-   **Linter**: 使用 Ruff。
-   **命名**:
    -   文件名、变量名、函数名: `snake_case` (例如: `financial_data_service.py`, `def get_user_data()`)
    -   类名: `PascalCase` (例如: `class DataProvider:`)

---

## API 设计规范

-   **端点命名 (Endpoint Naming)**:
    -   路径使用 `kebab-case`。
    -   使用名词而非动词。
    -   *示例*: `/api/v1/user-profiles`, `/api/v1/market-data`
-   **数据格式 (Data Format)**:
    -   所有 API 请求和响应体均使用 JSON 格式。
    -   JSON 字段名统一使用 `snake_case`，以匹配 Python 后端的习惯。
    -   *示例*: `{"user_id": 123, "full_name": "John Doe"}`
