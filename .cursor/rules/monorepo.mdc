---
description: 
globs: 
alwaysApply: true
---

## 文档编写规范

为了保持项目整洁，所有 `.md` 格式的文档文件（除了项目根目录的 `README.md` 以及每个 `app` 下的 `README.md`）都应该统一存放在根目录下的 `docs` 目录中。

---

## 前端开发流程 (`njs-investor-insight`)

前端是一个独立的 Next.js 应用，其依赖由根目录的 `pnpm` 工作区管理。

### 首次设置

1.  **在项目根目录安装所有依赖**:
    `pnpm` 会自动处理 `apps` 目录下所有应用的依赖项。
    ```bash
    # 确保在项目根目录 yai-investor-insight/
    pnpm install
    ```

### 日常启动

1.  **进入应用目录**:
    ```bash
    cd apps/njs-investor-insight
    ```

2.  **启动开发服务器**:
    ```bash
    pnpm dev
    ```
    服务将默认运行在 `http://localhost:3000` (如果端口被占用，会自动使用下一个可用端口，如 `3001`)。

---

## 后端开发流程 (`py-investor-insight`)

后端是一个独立的 Python 应用，使用 FastAPI 框架。

### 首次设置

1.  **进入应用目录**:
    ```bash
    cd apps/py-investor-insight
    ```

2.  **创建并激活虚拟环境**:
    ```bash
    # 创建虚拟环境
    python3 -m venv .venv
    # 激活虚拟环境 (macOS/Linux)
    source .venv/bin/activate
    # 如果使用 Windows, 运行:
    # .venv\Scripts\activate
    ```

3.  **安装依赖**:
    激活虚拟环境后，使用 pip 安装所有必需的包。
    ```bash
    pip install -r requirements.txt
    ```
4.  **配置环境变量**:
    从 `.env.example` 复制一份并重命名为 `.env`，然后根据需要修改其中的配置。
    ```bash
    cp .env.example .env
    ```

### 日常启动

1.  **进入应用目录并激活虚拟环境**:
    ```bash
    cd apps/py-investor-insight
    source .venv/bin/activate
    ```

2.  **启动开发服务器**:
    ```bash
    python main.py
    ```
    服务将默认运行在 `http://localhost:8000`。
