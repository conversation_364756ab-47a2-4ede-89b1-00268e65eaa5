{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@yai-investor-insight/api-client": ["libs/api-client/src/index.ts"], "@yai-investor-insight/shared-types": ["libs/shared-types/src/index.ts"], "@yai-investor-insight/demo-feature-fe": ["libs/demo-feature-fe/src/index.ts"], "@yai-investor-insight/shared-fe-kit": ["libs/shared-fe-kit/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}