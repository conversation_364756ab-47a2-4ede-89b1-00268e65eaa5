# 全栈调试指南

## 🎯 问题解决

**之前的问题：** 只能看到 Next.js 调用栈，看不到 Python 的调试信息

**原因：** 调试配置只包含了 Node.js 调试器，缺少 Python 调试配置

## 🔧 新的调试配置

### 1. 🔥 Full Stack Debug（推荐）
**用途：** 同时启动并调试 Python API 和 Next.js Web App

**使用方法：**
1. 在 VS Code 中按 `F5` 或点击调试面板
2. 选择 "🔥 Full Stack Debug"
3. 会同时启动两个调试会话：
   - 🐍 Python API Server (端口 8000)
   - ⚡ Next.js Web App (端口 3000)

**调试体验：**
- ✅ 可以在 Python 代码中设置断点
- ✅ 可以在 TypeScript/JavaScript 代码中设置断点
- ✅ 支持变量检查、调用栈查看
- ✅ 支持热重载

### 2. 单独调试配置

#### 🐍 Python API Server
- **类型：** `debugpy` (Python 调试器)
- **启动方式：** uvicorn main:app
- **端口：** 8000
- **入口：** `apps/api-server/main.py` (FastAPI app 对象)
- **环境：** 自动加载 `.venv` 虚拟环境
- **日志级别：** DEBUG
- **热重载：** --reload 参数启用

#### ⚡ Next.js Web App
- **端口：** 3000
- **入口：** `apps/web-app`
- **调试端口：** 9229

### 3. 🔗 Attach 模式（高级用法）

如果服务已经在运行，可以使用 Attach 模式：

#### 🔗 Attach to All Services
- 附加到已运行的 Python 和 Next.js 服务
- 适用于 Docker 环境或外部启动的服务

## 🚀 使用步骤

### 方式一：一键启动（推荐）
```bash
# 1. 确保依赖已安装
pnpm install

# 2. 在 VS Code 中按 F5
# 3. 选择 "🔥 Full Stack Debug"
```

### 方式二：手动启动
```bash
# 1. 启动 Python API
cd apps/api-server
source .venv/bin/activate
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# 2. 启动 Next.js（新终端）
cd apps/web-app
pnpm dev

# 3. 在 VS Code 中选择 "🔗 Attach to All Services"
```

## 🔍 调试技巧

### Python 调试
```python
# 在代码中设置断点
import pdb; pdb.set_trace()  # 传统方式

# 或者直接在 VS Code 中点击行号设置断点
```

### Next.js 调试
```typescript
// 在代码中设置断点
debugger;  // 浏览器会在此处暂停

// 或者在 VS Code 中设置断点
```

### 全栈调试流程
1. **前端发起请求** → 在 Next.js 中设置断点
2. **API 接收请求** → 在 Python FastAPI 中设置断点
3. **数据处理** → 在业务逻辑中设置断点
4. **返回响应** → 在响应处理中设置断点

## 🛠️ 故障排除

### 常见问题

#### 1. Python 调试器无法启动
```bash
# 检查虚拟环境
ls apps/api-server/.venv/bin/python

# 重新创建虚拟环境
cd apps/api-server
rm -rf .venv
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

#### 2. Next.js 断点无法生效
- 参考详细的 [Next.js 调试故障排除指南](./nextjs-debugging-troubleshooting.md)
- 检查 source maps 配置
- 确认调试端口 (9229) 未被占用

```bash
# 检查端口占用
lsof -i :3000
lsof -i :9229

# 重启 Next.js
cd apps/web-app
pnpm dev
```

#### 3. 断点不生效
- 确保源码映射正确
- 检查 `justMyCode: false` 配置
- 重启调试会话

### 环境要求
- ✅ Python 3.8+
- ✅ Node.js 18+
- ✅ pnpm 8+
- ✅ VS Code Python 扩展
- ✅ VS Code TypeScript 扩展

## 📝 配置文件说明

### `.vscode/launch.json`
包含所有调试配置，支持：
- 单独调试 Python/Next.js
- 复合调试（同时调试）
- Attach 模式

### 环境变量
```bash
# Python 环境
PYTHONPATH=apps/api-server/src
LOG_LEVEL=DEBUG
ENVIRONMENT=development

# Next.js 环境
NODE_OPTIONS=--inspect
```

## 🎉 现在你可以：

1. **同时调试前后端** - 设置断点，查看完整调用链
2. **变量检查** - 实时查看 Python 和 JavaScript 变量
3. **调用栈追踪** - 从前端请求到后端处理的完整流程
4. **热重载** - 代码修改后自动重启，保持调试会话

享受全栈调试的强大功能！🚀