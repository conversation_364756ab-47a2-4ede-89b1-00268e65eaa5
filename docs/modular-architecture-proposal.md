# 面向未来的插件式架构方案

## 1. 架构演进：从分层到插件式

### 1.1. 现有分层架构 (COLA) 的优势与局限

项目初期，我们采用了业界成熟的**分层架构模式（如 COLA - Clean Object-oriented and Layered Architecture）**。该架构以领域模型为核心，将系统清晰地划分为**领域层 (Domain)**、**应用层 (Application)** 和**基础设施层 (Infrastructure)**。这为我们带来了诸多好处：

*   **关注点分离**：各层职责单一，逻辑清晰。
*   **领域驱动**：确保了业务逻辑的核心地位，不易被技术细节侵蚀。
*   **可测试性**：核心业务逻辑独立于外部依赖，易于进行单元测试。

然而，随着业务功能的不断丰富，一个单一、庞大的分层架构开始暴露其局限性：

*   **模块边界模糊**：所有功能的代码（例如“用户认证”和“投资组合管理”）被分散在同一个 `domain`、`application` 和 `infrastructure` 文件夹结构中。这使得单个功能的边界变得模糊，难以理解和维护。
*   **层内耦合加剧**：在 `domain` 层内部，不同业务模块的领域服务可能会无意中产生耦合，逐渐形成一个难以拆分的“大泥球”。
*   **团队协作困难**：不同功能团队需要频繁修改相同的代码目录（例如 `application/services`），导致协作成本和代码冲突风险增高。
*   **认知负荷过高**：新成员需要加载整个项目的领域知识才能开始有效工作，而非仅仅聚焦于其负责的模块。

### 1.2. 引入插件式架构：宏观解耦，微观自治

为了解决上述挑战，我们决定在宏观层面引入**插件式架构（也称“模块化单体”）**。此举并非要推翻原有的分层思想，而是对其进行**“容器化”管理**。

核心理念是：**在宏观上，我们将整个系统按业务能力垂直切分为独立的“功能插件”（如 `portfolio-management-bs`）；在微观上，每个插件内部依然可以是一个完整、独立的 COLA 式分层架构。**

这种演进带来了决定性的优势：

*   **清晰的业务边界**：每个功能插件都是一个高内聚的单元，包含了实现该业务所需的所有前后端逻辑。
*   **强制解耦**：插件之间只能通过定义好的服务接口（或 API）通信，杜绝了内部实现的意外耦合。
*   **高度自治**：单个插件的内部架构（无论是 COLA 还是其他）可以独立演进，只要其对外接口保持稳定。
*   **提升团队效率**：团队可以端到端地拥有一个或多个插件，实现真正的并行开发。

本方案的后续章节将详细阐述如何实现这一先进的插件式架构。

## 2. 核心价值与目标

本方案旨在将项目重构为一种**模块化的插件式架构**，以实现以下核心价值：

*   **高度可扩展性 (Scalability)**: 新功能可以作为独立的“插件”库被轻松添加，而无需大规模改动现有代码，使系统能够平滑地演进。
*   **明确的责任边界 (Clear Ownership)**: 每个功能模块（例如 `portfolio-management-fe` 和 `portfolio-management-bs`）都成为一个独立的单元。这使得团队可以端到端地负责一个功能，从前端到后端，提升开发效率和代码质量。
*   **提升可维护性 (Maintainability)**: 功能间的耦合度被降至最低。修改一个功能模块的内部实现，不会轻易影响到其他模块，从而显著降低了回归风险。
*   **加速并行开发 (Parallel Development)**: 不同的团队或开发者可以同时在各自的功能模块上工作，互不干扰，大幅缩短开发周期。
*   **技术栈优势最大化 (Leveraging Tech Stack)**: 此架构完全契合我们现有的 **Nx Monorepo** 工具链，能够充分利用其依赖图分析、缓存、分布式任务执行等高级功能。

## 3. 整体项目结构

项目将遵循 Monorepo 的最佳实践，将代码分为**应用（Apps）**、**库（Libs）**和**支持性文件（Docs, Tools）**三大部分。

```
yai-investor-insight/
├── apps/
│   ├── api-server/        # 主后端应用 (FastAPI)，负责集成所有-bs库
│   └── web-app/           # 主前端应用 (Next.js)，负责集成所有-fe库
│
├── libs/
│   ├── company-financials-bs/ # 功能: 公司财报 - 后端服务
│   ├── company-financials-fe/ # 功能: 公司财报 - 前端界面
│   │
│   ├── market-data-bs/      # 功能: 市场行情 - 后端服务
│   ├── market-data-fe/      # 功能: 市场行情 - 前端界面
│   │
│   ├── news-analysis-bs/    # 功能: 新闻分析 - 后端服务
│   ├── news-analysis-fe/    # 功能: 新闻分析 - 前端界面
│   │
│   ├── portfolio-management-bs/ # 功能: 投资组合管理 - 后端服务
│   │   ├── src/
│   │   │   ├── portfolio_management_bs/
│   │   │   │   ├── adapter/       # (微观COLA) 适配层
│   │   │   │   ├── application/   # (微观COLA) 应用层
│   │   │   │   ├── domain/        # (微观COLA) 领域层
│   │   │   │   └── infrastructure/# (微观COLA) 基础设施层
│   │   │   └── __init__.py
│   │   └── pyproject.toml
│   │
│   ├── portfolio-management-fe/ # 功能: 投资组合管理 - 前端界面
│   │
│   ├── user-auth-bs/        # 功能: 用户认证 - 后端服务
│   ├── user-auth-fe/        # 功能: 用户认证 - 前端界面
│   │
│   ├── shared-bs-core/      # 共享: 核心后端工具库
│   ├── shared-fe-kit/       # 共享: 通用前端UI组件库
│   └── shared-types/        # 共享: 前后端通用的TypeScript类型
│
├── docs/
│   └── modular-architecture-proposal.md # (本文档)
│
├── tools/
│   └── scripts/             # 工作区脚本
│
├── package.json
├── nx.json
└── pnpm-workspace.yaml
```

## 4. 架构设计：库（Libs）

库（`libs/`）是本架构的核心。我们将采用**扁平化的库结构**并结合 **Nx 标签（Tags）**进行逻辑分组。

### 4.1. 命名规范

库的目录名遵循 `<scope>-<type>` 格式。

*   **`scope`**: 库所属的功能领域。我们以 5 个核心模块为例：
    *   `user-auth` (用户认证)
    *   `market-data` (市场行情)
    *   `portfolio-management` (投资组合管理)
    *   `news-analysis` (新闻分析)
    *   `company-financials` (公司财报)
    *   `shared` (共享)
*   **`type`**: 库的技术类型。
    *   `bs`: **Backend Service**，用于 Python 后端逻辑。
    *   `fe`: **Frontend**，用于 TypeScript/React 前端组件和页面。

### 4.2. 使用 Nx 标签进行逻辑分组

为了在逻辑上保持功能的内聚性，我们将为每个库强制添加标签：

*   `scope:<scope-name>`: 例如 `scope:portfolio-management`, `scope:shared`。
*   `type:<type-name>`: 例如 `type:bs`, `type:fe`。

这使我们能够通过 `nx graph --include=scope:portfolio-management` 等命令，可视化任何一个功能维度的依赖关系。

## 5. 共享代码策略

*   **`libs/shared-types`**: 定义跨前后端共享的 TypeScript 接口和类型。
*   **`libs/shared-fe-kit`**: 存放全局通用的无状态 UI 组件（如 `Button`, `Card`）。
*   **`libs/shared-bs-core`**: 提供所有 `-bs` 库依赖的核心服务，如数据库配置、基类、通用异常和依赖注入容器。

## 6. 模块间通信机制

### 6.1. 后端模块间通信 (Python)

我们将采用**依赖注入（DI）**的方式进行服务调用，以实现高性能和低耦合。

```python
# 在 libs/portfolio-management-bs/src/.../use_case.py

from market_data_bs.services import MarketDataService # 仅用于类型提示

class PortfolioValuationUseCase:
    # 通过构造函数注入市场行情服务
    def __init__(self, market_data_service: MarketDataService):
         self._market_data_service = market_data_service

    def execute(self, portfolio_id: int):
        # 调用行情服务获取最新价格
        latest_prices = self._market_data_service.get_latest_prices(...)
        # ...后续计算
```

### 6.2. 前后端通信

所有 API 路由都将按功能进行命名隔离，例如：
*   `/api/v1/portfolio-management/portfolios` -> 由 `portfolio-management-bs` 库处理。
*   `/api/v1/market-data/quotes/{symbol}` -> 由 `market-data-bs` 库处理。

## 7. 应用集成方案

主应用 (`apps/*`) 作为“集成层”，负责组装所有功能模块。

### 7.1. 后端集成 (`api-server`)

利用 FastAPI 的 `APIRouter`。每个 `-bs` 库都提供一个 `router` 实例，主应用 `main.py` 在启动时动态地 `include` 所有模块的路由。

```python
# apps/api-server/main.py
from fastapi import FastAPI
from user_auth_bs import router as user_auth_router
from portfolio_management_bs import router as portfolio_router
# ...

app = FastAPI()

# 挂载所有模块的 API 路由
app.include_router(user_auth_router, prefix="/api/v1/auth", tags=["Auth"])
app.include_router(portfolio_router, prefix="/api/v1/portfolio-management", tags=["Portfolio"])
# ...
```

### 7.2. 前端集成 (`web-app`)

利用 Next.js 的 App Router 和 Nx 的路径别名。
*   **页面路由**: `libs/portfolio-management-fe/src/app/portfolios/page.tsx` 会被 Next.js 自动识别为 `/portfolios` 页面。
*   **组件使用**: 可以通过 `@<workspace>/shared-fe-kit` 或 `@<workspace>/market-data-fe` 等别名在任何地方导入组件。

## 8. 总结

本方案通过**插件式宏观架构**与**COLA式微观架构**相结合，利用**扁平化库结构**、**Nx标签**、**共享代码**及**依赖注入**等策略，构建了一个职责清晰、高度解耦、易于扩展的先进软件系统。它不仅解决了当前项目面临的挑战，更为未来的快速发展奠定了坚实的基础。 