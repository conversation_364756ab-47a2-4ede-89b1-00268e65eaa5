# K8s环境中loglayer-support v0.7.9问题排查与解决方案

## 问题描述

在将使用loglayer-support v0.7.9的Next.js应用部署到K8s环境后，浏览器控制台出现错误，而本地.env.local环境运行正常。

## 问题分析

### 1. 环境差异对比

| 环境 | 配置方式 | 工作状态 |
|------|----------|----------|
| 本地开发 | `.env.local` 文件 | ✅ 正常 |
| K8s生产 | ConfigMap环境变量 | ❌ 报错 |

### 2. 根本原因

1. **环境变量读取时序问题**: K8s环境中环境变量的注入时机与本地不同
2. **React渲染问题**: 异步日志器初始化可能导致React组件渲染错误
3. **网络连接问题**: K8s环境中SLS连接可能存在网络限制
4. **类型兼容性问题**: loglayer-support在不同环境中的API调用差异

## 解决方案

### 1. 增强环境变量读取机制

```typescript
// 安全的环境变量读取函数
const getEnvVar = (key: string, defaultValue: string = ''): string => {
  try {
    if (typeof window !== 'undefined') {
      // 浏览器环境 - 多种方式尝试获取环境变量
      return (window as any).__NEXT_DATA__?.env?.[key] || 
             (window as any).__ENV__?.[key] ||
             process.env[key] || 
             defaultValue;
    }
    // 服务端环境
    return process.env[key] || defaultValue;
  } catch (error) {
    console.warn(`获取环境变量 ${key} 失败:`, error);
    return defaultValue;
  }
};
```

### 2. K8s环境检测

```typescript
// 检查是否在K8s环境中
const isK8sEnvironment = (): boolean => {
  return !!(
    getEnvVar('KUBERNETES_SERVICE_HOST') ||
    getEnvVar('KUBERNETES_PORT') ||
    getEnvVar('KUBERNETES_DEPLOYMENT') ||
    getEnvVar('HOSTNAME', '').includes('pod') ||
    getEnvVar('NODE_NAME')
  );
};
```

### 3. 分环境日志配置

```typescript
const createLogConfig = (): YAILogLayerConfig => {
  const isK8s = isK8sEnvironment();
  const nodeEnv = getEnvVar('NODE_ENV', 'development');
  const slsEnabled = getEnvVar('NEXT_PUBLIC_SLS_ENABLED') === 'true';
  const slsConfigComplete = checkSlsConfig();
  
  return {
    // ... 其他配置
    features: {
      enableBatch: true,
      batchSize: isK8s ? 20 : 10, // K8s环境中增加批量大小
      flushInterval: isK8s ? 10000 : 5000, // K8s环境中增加刷新间隔
      enableRetry: true,
      maxRetries: isK8s ? 1 : 2, // K8s环境中减少重试次数
      enableConsole: nodeEnv === 'development' || !slsConfigComplete,
      enableSls: slsEnabled && slsConfigComplete
    }
  };
};
```

### 4. 健壮的日志器初始化

```typescript
const initLogger = async () => {
  // 防止重复初始化
  if (initPromise) {
    return initPromise;
  }
  
  if (!browserLogger) {
    initPromise = (async () => {
      try {
        const isK8s = isK8sEnvironment();
        
        // K8s环境中使用更保守的初始化策略
        if (isK8s) {
          // 在K8s环境中，先尝试只使用控制台输出
          browserLogger = await createBrowserLogger(logConfig.app.name, {
            browser: { outputs: [{ type: 'console' }] }
          } as any);
        } else {
          // 非K8s环境使用完整配置
          browserLogger = await createBrowserLogger(logConfig.app.name, {
            browser: { outputs }
          } as any);
        }
        
        return browserLogger;
      } catch (error) {
        // 创建fallback日志器
        browserLogger = {
          debug: (msg: string, data?: any) => console.debug(`[DEBUG] ${msg}`, data),
          info: (msg: string, data?: any) => console.info(`[INFO] ${msg}`, data),
          warn: (msg: string, data?: any) => console.warn(`[WARN] ${msg}`, data),
          error: (msg: string, data?: any) => console.error(`[ERROR] ${msg}`, data)
        };
        return browserLogger;
      }
    })();
  }
  
  return initPromise;
};
```

### 5. 更新K8s ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: yai-investor-insight-web-app-config
data:
  # 应用基础配置
  NEXT_PUBLIC_API_URL: "http://yai-investor-insight-api-server-service"
  NEXT_PUBLIC_APP_VERSION: "1.0.0"
  NEXT_PUBLIC_SERVICE_NAME: "yai-investor-insight-web"
  NODE_ENV: "production"
  
  # 日志配置 - loglayer-support v0.7.9
  NEXT_PUBLIC_SLS_ENABLED: "true"
  NEXT_PUBLIC_SLS_ENDPOINT: "https://cn-beijing.log.aliyuncs.com"
  NEXT_PUBLIC_SLS_ACCESS_KEY_ID: "LTAI5tPrkKMrPLW1XjbBxwhm"
  NEXT_PUBLIC_SLS_ACCESS_KEY_SECRET: "******************************"
  NEXT_PUBLIC_SLS_PROJECT: "yai-log-test"
  NEXT_PUBLIC_SLS_LOGSTORE: "app-log"
  NEXT_PUBLIC_SLS_REGION: "cn-beijing"
  NEXT_PUBLIC_SLS_TOPIC: "default"
  
  # K8s环境标识
  KUBERNETES_DEPLOYMENT: "true"
  DEPLOYMENT_ENVIRONMENT: "k8s"
```

## 调试工具

### 1. 调试页面

创建了专门的调试页面 `/debug-logging`，可以：
- 检查环境变量配置
- 测试日志器初始化状态
- 验证SLS连接
- 显示详细的错误信息

### 2. 使用方法

1. 部署更新后的应用到K8s
2. 访问 `https://your-domain/debug-logging`
3. 查看环境检测结果和错误信息
4. 点击"测试日志"按钮验证功能

## 预期效果

1. **K8s环境兼容**: 应用在K8s环境中正常启动，不再出现控制台错误
2. **优雅降级**: 即使SLS配置有问题，也会回退到控制台日志
3. **环境自适应**: 根据不同环境自动调整日志配置参数
4. **错误处理**: 提供详细的错误信息和调试工具

## 部署步骤

1. 更新日志器代码 (`apps/web-app/src/lib/logger.ts`)
2. 更新K8s ConfigMap (`deploy/web/config/yai-investor-insight-web-app-config.yaml`)
3. 重新构建和部署应用
4. 访问调试页面验证修复效果

## 监控建议

1. **日志监控**: 在SLS中设置告警，监控日志上报状态
2. **错误追踪**: 监控浏览器控制台错误，及时发现问题
3. **性能监控**: 关注日志系统对应用性能的影响

## 后续优化

1. **配置验证**: 在应用启动时验证所有必需的环境变量
2. **健康检查**: 添加日志系统的健康检查端点
3. **性能优化**: 根据实际使用情况调整批量大小和刷新间隔
