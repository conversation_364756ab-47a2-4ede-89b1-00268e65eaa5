# VSCode 分层调试配置架构

## 🎯 **设计理念**

采用分层的调试配置架构，将配置按照项目结构进行组织，实现更好的职责分离和维护性。

## 📁 **配置结构**

```
yai-investor-insight/
├── .vscode/                    # 项目级配置
│   ├── launch.json            # 全栈调试配置
│   └── tasks.json             # 项目级任务
├── apps/
│   ├── api-server/
│   │   └── .vscode/           # API Server 专用配置
│   │       ├── launch.json    # Python 调试配置
│   │       └── tasks.json     # Python 任务配置
│   └── web-app/
│       └── .vscode/           # Web App 专用配置
│           ├── launch.json    # Node.js 调试配置
│           └── tasks.json     # Web 任务配置
└── libs/                      # 共享库（可按需添加配置）
```

## 🔧 **配置层级说明**

### 1. 项目级配置 (Root/.vscode/)

**职责**: 全栈调试、项目级任务、跨服务协调

**包含内容**:
- 🚀 全栈调试配置
- 📦 项目级依赖管理
- 🏗️ 整体构建任务
- 🧪 全项目测试

### 2. API Server 配置 (apps/api-server/.vscode/)

**职责**: Python 应用专用调试和任务

**包含内容**:
- 🐍 Python 应用调试
- 🧪 单元测试运行
- 🔧 当前文件调试
- 📦 Python 依赖管理

### 3. Web App 配置 (apps/web-app/.vscode/)

**职责**: Next.js 应用专用调试和任务

**包含内容**:
- 🌐 Next.js 开发服务器调试
- 🔧 服务端渲染调试
- 🧪 Jest 测试调试
- 🏗️ 构建过程调试

## ✅ **优势对比**

### 之前的问题 (单一配置)
```
❌ 配置文件臃肿，难以维护
❌ 不同技术栈的配置混杂
❌ 团队协作时配置冲突
❌ 职责不清晰
❌ 扩展性差
```

### 现在的优势 (分层配置)
```
✅ 配置职责清晰，易于维护
✅ 技术栈隔离，专业化配置
✅ 团队成员可独立维护各自配置
✅ 支持独立开发和调试
✅ 良好的扩展性
```

## 🚀 **使用指南**

### 调试 API Server
1. 打开 `apps/api-server` 文件夹
2. 选择 `🐍 Debug API Server` 配置
3. 开始调试

### 调试 Web App
1. 打开 `apps/web-app` 文件夹
2. 选择 `🌐 Debug Next.js Dev` 配置
3. 开始调试

### 全栈调试
1. 在根目录选择 `🚀 Full Stack Debug` 配置
2. 同时启动所有服务

### 运行测试
- **API Server**: 使用 `🧪 Run Tests` 配置
- **Web App**: 使用 `🧪 Debug Jest Tests` 配置
- **全项目**: 使用根目录的 `test-all` 任务

## 📋 **最佳实践**

### 1. 配置命名规范
- 使用 emoji 前缀区分配置类型
- 描述性命名，便于识别

### 2. 环境变量管理
- 每个应用维护自己的环境变量
- 开发/测试/生产环境分离

### 3. 任务依赖管理
- 合理设置任务依赖关系
- 使用并行执行提高效率

### 4. 路径配置
- 使用相对路径，提高可移植性
- 避免硬编码绝对路径

## 🔄 **迁移指南**

### 从旧配置迁移
1. 备份现有配置
2. 按应用类型分离配置
3. 更新路径引用
4. 测试各个配置是否正常工作

### 添加新应用
1. 在应用目录下创建 `.vscode` 文件夹
2. 添加对应的 `launch.json` 和 `tasks.json`
3. 在根目录配置中添加全栈调试支持

## 🛠️ **故障排除**

### 常见问题
1. **配置不生效**: 确保在正确的工作区打开文件
2. **路径错误**: 检查 `cwd` 和相对路径设置
3. **任务失败**: 验证依赖是否正确安装

### 调试技巧
1. 使用 VSCode 的 "Output" 面板查看详细日志
2. 检查 "Problems" 面板的错误信息
3. 在终端中手动执行命令验证环境

## 📈 **扩展建议**

### 未来可以添加的配置
- 📊 性能分析配置
- 🐳 Docker 调试配置
- 🔍 代码覆盖率配置
- 🚀 部署相关任务

这种分层架构为项目的长期维护和团队协作提供了坚实的基础。