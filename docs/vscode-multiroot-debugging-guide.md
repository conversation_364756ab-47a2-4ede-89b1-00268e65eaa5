# VSCode 多根工作区调试指南

## 问题描述

在多根工作区配置下，API Server 调试启动失败的常见问题和解决方案。

## 主要问题

### 1. 工作区文件夹引用问题

**问题**: `${workspaceFolder:API Server}` 引用在某些情况下无法正确解析路径。

**原因**: 
- 多根工作区的文件夹名称必须完全匹配
- VSCode 在某些版本中对多根工作区的路径解析存在问题

**解决方案**: 提供两种调试配置
- `🐍 Debug API Server`: 使用标准路径 `${workspaceFolder}/apps/api-server`
- `🐍 Debug API Server (Multi-root)`: 使用多根工作区引用 `${workspaceFolder:API Server}`

### 2. Python 环境配置问题

**问题**: Python 解释器路径和 PYTHONPATH 配置不正确。

**解决方案**:
```json
{
  "python": "${workspaceFolder}/apps/api-server/.venv/bin/python",
  "env": {
    "PYTHONPATH": "${workspaceFolder}/apps/api-server",
    "LOG_LEVEL": "DEBUG",
    "ENVIRONMENT": "development"
  }
}
```

### 3. 任务依赖问题

**问题**: `preLaunchTask` 在多根工作区中执行失败。

**解决方案**: 创建对应的任务配置
- `install-python-deps`: 标准路径版本
- `install-python-deps-multiroot`: 多根工作区版本

## 使用建议

### 推荐调试流程

1. **首选方案**: 使用 `🐍 Debug API Server` 配置
   - 兼容性更好
   - 路径解析更稳定

2. **备选方案**: 使用 `🐍 Debug API Server (Multi-root)` 配置
   - 适用于严格的多根工作区环境

### 环境检查

在调试前，确保以下条件满足：

```bash
# 1. 检查虚拟环境是否存在
ls -la apps/api-server/.venv/

# 2. 检查 Python 解释器
apps/api-server/.venv/bin/python --version

# 3. 检查依赖是否安装
apps/api-server/.venv/bin/pip list
```

### 常见错误排查

#### 错误: "No module named 'src'"

**原因**: PYTHONPATH 配置不正确

**解决**: 确保 PYTHONPATH 指向 API Server 根目录
```json
"env": {
  "PYTHONPATH": "${workspaceFolder}/apps/api-server"
}
```

#### 错误: "Python interpreter not found"

**原因**: 虚拟环境路径不正确

**解决**: 
1. 重新创建虚拟环境
2. 更新调试配置中的 Python 路径

#### 错误: "Task 'install-python-deps' not found"

**原因**: 任务配置与调试配置不匹配

**解决**: 使用对应的任务名称
- 标准配置使用 `install-python-deps`
- 多根配置使用 `install-python-deps-multiroot`

## 最佳实践

1. **统一使用标准路径**: 优先使用 `${workspaceFolder}/apps/api-server` 而不是多根引用
2. **环境变量配置**: 始终设置 `LOG_LEVEL` 和 `ENVIRONMENT` 环境变量
3. **控制台配置**: 使用 `"console": "integratedTerminal"` 获得更好的调试体验
4. **调试选项**: 设置 `"justMyCode": false` 以便调试第三方库

## 配置文件说明

### launch.json
包含两个调试配置，支持不同的工作区模式。

### tasks.json  
包含对应的任务配置，确保依赖安装正确执行。

### workspace 文件
配置 Python 解释器路径和相关设置。

## 故障排除

如果调试仍然无法启动：

1. 重启 VSCode
2. 重新加载工作区
3. 检查 Python 扩展是否正常工作
4. 查看 VSCode 输出面板的错误信息
5. 尝试在终端中手动启动应用验证环境配置