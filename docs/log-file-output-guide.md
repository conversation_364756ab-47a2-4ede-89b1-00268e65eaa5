# 日志文件输出配置指南

## 📁 当前日志文件输出位置

### 🎯 绝对路径
```
/Users/<USER>/Documents/Y-AI/yai-investor-insight/apps/api-server/logs/investor-insight.log
```

### 🔍 相对路径（相对于 apps/api-server/）
```
logs/investor-insight.log
```

## ⚙️ 配置说明

### 环境变量配置
在 `apps/api-server/.env` 文件中：

```bash
# 日志配置
LOG_APP_NAME=investor-insight
LOG_LEVEL=INFO
LOG_CONSOLE_ENABLED=true
LOG_FILE_ENABLED=true                    # 启用文件日志
LOG_FILE_PATH=logs/investor-insight.log  # 文件路径
LOG_UVICORN_INTEGRATION_ENABLED=true
```

### 代码配置逻辑
在 `src/infrastructure/monitoring/logger.py` 中：

```python
# 根据环境设置文件日志
if settings.environment == "production":
    os.environ.setdefault("LOG_FILE_ENABLED", "true")
    os.environ.setdefault("LOG_FILE_PATH", f"logs/{app_name}.log")
```

## 📊 当前状态

### ✅ 已启用功能
- **控制台日志**: ✅ 启用 - 在终端显示彩色格式化日志
- **文件日志**: ✅ 启用 - 写入到 `logs/investor-insight.log`
- **Uvicorn 集成**: ✅ 启用 - 接管 FastAPI 访问日志
- **trace_id 支持**: ✅ 启用 - 全链路追踪

### 📄 日志文件格式示例
```
2025-07-22 20:22:56.027 | INFO    | test_file_logging:26 | [No-Trace-ID] | 🚀 开始文件日志测试
2025-07-22 20:22:56.027 | DEBUG   | test_file_logging:27 | [No-Trace-ID] | 这是调试信息 | test_type=file_logging
2025-07-22 20:22:56.027 | WARNING | test_file_logging:28 | [No-Trace-ID] | 这是警告信息 | warning_code=W001
2025-07-22 20:22:56.027 | ERROR   | test_file_logging:29 | [No-Trace-ID] | 这是错误信息 | error_code=E001 | details=测试错误
2025-07-22 20:22:56.027 | INFO    | test_file_logging:36 | [test-trace-123] | 带 trace_id 的日志 | user_id=user_123 | action=file_log_test | timestamp=1753186976.027488
```

## 🔧 不同环境的配置

### 开发环境 (Development)
```bash
LOG_FILE_ENABLED=true          # 可选，用于调试
LOG_FILE_PATH=logs/dev.log     # 开发环境日志文件
LOG_LEVEL=DEBUG                # 详细日志级别
```

### 生产环境 (Production)
```bash
LOG_FILE_ENABLED=true          # 必须启用
LOG_FILE_PATH=logs/prod.log    # 生产环境日志文件
LOG_LEVEL=INFO                 # 适中的日志级别
SLS_ENABLED=true               # 启用云日志服务
```

### 测试环境 (Testing)
```bash
LOG_FILE_ENABLED=false         # 通常关闭文件日志
LOG_LEVEL=ERROR                # 只记录错误
```

## 📂 目录结构

```
apps/api-server/
├── logs/                      # 日志文件目录
│   ├── investor-insight.log   # 主应用日志
│   ├── test.log              # 测试日志
│   └── ...                   # 其他日志文件
├── src/
├── tests/
└── ...
```

## 🛠️ 管理建议

### 1. 日志轮转
考虑配置日志轮转以避免文件过大：
```bash
# 可以使用 logrotate 或在代码中配置
LOG_FILE_MAX_SIZE=100MB
LOG_FILE_BACKUP_COUNT=5
```

### 2. 权限设置
确保日志目录有正确的写权限：
```bash
chmod 755 apps/api-server/logs/
chmod 644 apps/api-server/logs/*.log
```

### 3. 监控和清理
定期监控日志文件大小和清理旧日志：
```bash
# 查看日志文件大小
ls -lh apps/api-server/logs/

# 清理旧日志（谨慎操作）
find apps/api-server/logs/ -name "*.log" -mtime +30 -delete
```

## 🔍 查看日志的方法

### 实时查看
```bash
# 实时跟踪日志
tail -f apps/api-server/logs/investor-insight.log

# 带颜色的实时查看
tail -f apps/api-server/logs/investor-insight.log | grep --color=always "ERROR\|WARN\|INFO"
```

### 搜索日志
```bash
# 搜索特定 trace_id
grep "test-trace-123" apps/api-server/logs/investor-insight.log

# 搜索错误日志
grep "ERROR" apps/api-server/logs/investor-insight.log

# 搜索特定时间范围
grep "2025-07-22 20:" apps/api-server/logs/investor-insight.log
```

## 🚀 测试验证

运行测试脚本验证日志功能：
```bash
cd apps/api-server
python3 test_file_logging.py
```

这将验证：
- ✅ 文件日志是否正常写入
- ✅ trace_id 是否正确记录
- ✅ 不同日志级别是否正常工作
- ✅ 结构化数据是否正确格式化
