# Next.js 调试故障排除指南

## 常见问题及解决方案

### 1. 断点无法生效

#### 问题症状
- Python 断点能正常工作，但 Next.js 断点无法触发
- 调试器连接成功，但代码执行时不会停在断点处

#### 解决方案

**A. 检查 Source Maps 配置**
确保 `next.config.ts` 中启用了开发模式的 source maps：
```typescript
experimental: {
  serverSourceMaps: true,
},
webpack: (config, { isServer, dev }) => {
  if (dev) {
    config.devtool = isServer ? 'eval-source-map' : 'eval-cheap-module-source-map';
  }
  // ...
}
```

**B. 检查调试配置**
确保 `.vscode/launch.json` 中包含以下配置：
```json
{
  "name": "⚡ Next.js Web App",
  "type": "node",
  "request": "launch",
  "port": 9229,
  "sourceMaps": true,
  "resolveSourceMapLocations": [
    "${workspaceFolder}/apps/web-app/**",
    "!**/node_modules/**"
  ],
  "skipFiles": [
    "<node_internals>/**",
    "**/node_modules/**"
  ]
}
```

**C. 检查端口配置**
确保 `NODE_OPTIONS` 和调试端口一致：
- 环境变量：`NODE_OPTIONS: "--inspect=9229"`
- 调试配置：`"port": 9229`

### 2. 调试器连接问题

#### 问题症状
- 调试器无法连接到 Next.js 进程
- 显示连接超时或端口占用错误

#### 解决方案

**A. 检查端口占用**
```bash
# 检查 9229 端口是否被占用
lsof -i :9229

# 如果被占用，杀死进程
kill -9 <PID>
```

**B. 重启调试会话**
1. 停止当前调试会话 (Shift+F5)
2. 等待 2-3 秒
3. 重新启动调试 (F5)

### 3. Source Maps 不匹配

#### 问题症状
- 断点设置在错误的行
- 调试器显示的代码与实际代码不符

#### 解决方案

**A. 清理 Next.js 缓存**
```bash
cd apps/web-app
rm -rf .next
pnpm dev
```

**B. 重新生成 TypeScript 类型**
```bash
cd apps/web-app
pnpm build
```

### 4. 调试最佳实践

#### A. 断点设置技巧
1. **避免在导入语句上设置断点**
2. **在函数体内设置断点**，而不是函数声明行
3. **使用 `debugger;` 语句**作为备选方案

#### B. 调试模式选择
- **服务端代码**：使用 "⚡ Next.js Web App" 配置
- **客户端代码**：在浏览器开发者工具中调试
- **全栈调试**：使用 "🔥 Full Stack Debug" 配置

#### C. 常用调试命令
- `F5`：开始调试
- `F10`：单步跳过
- `F11`：单步进入
- `Shift+F11`：单步跳出
- `Shift+F5`：停止调试

### 5. 环境检查清单

在开始调试前，请确认：

- [ ] VS Code 已安装 Node.js 调试扩展
- [ ] Next.js 项目已正确启动 (`pnpm dev`)
- [ ] 端口 9229 未被其他进程占用
- [ ] `tsconfig.json` 中启用了 `sourceMap: true`
- [ ] `next.config.ts` 中配置了开发模式的 source maps
- [ ] 调试配置中的路径正确指向项目目录

### 6. 高级调试技巧

#### A. 条件断点
右键点击断点 → 编辑断点 → 添加条件表达式

#### B. 日志断点
右键点击断点 → 编辑断点 → 选择"日志消息"

#### C. 异常断点
调试面板 → 断点部分 → 启用"未捕获异常"

## 故障排除流程

1. **重启开发服务器**
   ```bash
   # 停止当前服务
   Ctrl+C
   
   # 清理缓存并重启
   rm -rf .next
   pnpm dev
   ```

2. **重启 VS Code**
   - 完全关闭 VS Code
   - 重新打开项目

3. **检查调试输出**
   - 查看 VS Code 调试控制台
   - 检查终端输出是否有错误信息

4. **验证配置**
   - 确认所有配置文件语法正确
   - 检查路径是否正确

如果问题仍然存在，请检查 VS Code 和 Node.js 版本兼容性。