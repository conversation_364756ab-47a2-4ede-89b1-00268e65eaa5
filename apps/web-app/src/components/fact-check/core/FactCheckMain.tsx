/**
 * 事实核查主界面组件
 * 根据ASCII线框图设计的主要界面
 */
'use client';

import React, { useEffect } from 'react';
import { useFactCheckStore } from '../../../store/factCheckStore';
import { useFactCheckAGUI } from '../../../hooks/useFactCheckAGUI';
import TextInputArea from '../input/TextInputArea';
import ProcessingStatus from '../processing/ProcessingStatus';
import ResultsDisplay from '../results/ResultsDisplay';
import CostTracker from '../processing/CostTracker';
import { Card, CardContent, CardHeader } from '../../ui/Card';

const FactCheckMain: React.FC = () => {
  const {
    current_task,
    is_processing,
    connection_status,
    daily_cost,
    startFactCheck: startFactCheckAction,
    handleTaskStarted,
    handleClaimExtracted,
    handleAgentProgress,
    handleVerificationComplete,
    handleDebateStarted,
    handleDebateMessage,
    handleDebateConcluded,
    handleTaskComplete,
    handleCostUpdate,
    handleError,
    setConnectionStatus
  } = useFactCheckStore();

  // 集成AGUI
  const {
    connectionStatus: aguiStatus,
    isRunning,
    startFactCheck,
    cancelFactCheck
  } = useFactCheckAGUI({
    onTaskStarted: handleTaskStarted,
    onClaimExtracted: handleClaimExtracted,
    onAgentProgress: handleAgentProgress,
    onVerificationComplete: handleVerificationComplete,
    onDebateStarted: handleDebateStarted,
    onDebateMessage: handleDebateMessage,
    onDebateConcluded: handleDebateConcluded,
    onTaskComplete: handleTaskComplete,
    onCostUpdate: handleCostUpdate,
    onError: handleError
  });

  // 同步连接状态
  useEffect(() => {
    setConnectionStatus(aguiStatus);
  }, [aguiStatus, setConnectionStatus]);

  // 开始事实核查的处理函数
  const handleStartFactCheck = async (text: string, options: any) => {
    try {
      // 立即设置处理状态为true，提供即时UI反馈
      startFactCheckAction(text, options);
      
      // 启动AGUI事实核查流程
      await startFactCheck(text, options);
    } catch (error) {
      console.error('启动事实核查失败:', error);
      handleError(`启动失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 取消事实核查的处理函数
  const handleCancelFactCheck = async () => {
    if (current_task) {
      try {
        await cancelFactCheck(current_task.id);
      } catch (error) {
        console.error('取消事实核查失败:', error);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="mx-auto max-w-7xl">
        {/* 头部 */}
        <div className="mb-6 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
              🔍 AI事实核查引擎
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              基于多Agent协作的投资信息事实核查系统
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            {/* 连接状态指示器 */}
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${
                connection_status === 'connected' ? 'bg-green-500' :
                connection_status === 'connecting' ? 'bg-yellow-500' :
                'bg-red-500'
              }`} />
              <span className="text-sm text-gray-600">
                {connection_status === 'connected' ? '已连接' :
                 connection_status === 'connecting' ? '连接中' : '连接错误'}
              </span>
            </div>

            {/* 成本追踪 */}
            <CostTracker dailyCost={daily_cost} taskCost={current_task?.total_cost || 0} />
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：输入区域 */}
          <div className="lg:col-span-1">
            <TextInputArea
              onStartFactCheck={handleStartFactCheck}
              isProcessing={is_processing}
              connectionStatus={connection_status}
            />
          </div>

          {/* 右侧：处理状态和结果 */}
          <div className="lg:col-span-2">
            {is_processing ? (
              current_task ? (
                <ProcessingStatus
                  task={current_task}
                  onCancel={handleCancelFactCheck}
                />
              ) : (
                // 初始加载状态：处理中但任务还未创建
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-medium text-gray-900">
                      🚀 启动事实核查...
                    </h3>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-12">
                      <div className="flex justify-center mb-4">
                        <svg
                          className="h-8 w-8 animate-spin text-blue-600"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          />
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          />
                        </svg>
                      </div>
                      <p className="text-gray-600 mb-2">
                        正在启动AI事实核查引擎...
                      </p>
                      <p className="text-sm text-gray-400">
                        连接后端服务、初始化Agent团队
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )
            ) : current_task?.status === 'completed' ? (
              <ResultsDisplay task={current_task} />
            ) : (
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-medium text-gray-900">
                    📊 处理结果
                  </h3>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🔍</div>
                    <p className="text-gray-500 mb-2">
                      请在左侧输入需要核查的文本
                    </p>
                    <p className="text-sm text-gray-400">
                      系统将自动提取声明、交叉验证数据源，并在发现冲突时组织AI辩论
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* 底部：最近记录（TODO: 实现历史记录功能） */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium text-gray-900">
                📊 最近核查记录
              </h3>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-gray-500">
                  历史记录功能开发中...
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default FactCheckMain;