/**
 * 文本输入区域组件
 * 基于ASCII线框图的主界面设计
 */
'use client';

import React, { useState, useCallback } from 'react';
import { useFactCheckStore, FactCheckOptions } from '../../../store/factCheckStore';
import { Card, CardContent, CardHeader } from '../../ui/Card';
import { Button } from '../../ui/Button';

interface TextInputAreaProps {
  onStartFactCheck: (text: string, options: FactCheckOptions) => Promise<void>;
  isProcessing: boolean;
  connectionStatus: 'connecting' | 'connected' | 'error';
}

const TextInputArea: React.FC<TextInputAreaProps> = ({
  onStartFactCheck,
  isProcessing,
  connectionStatus
}) => {
  const { input_text, options, setInputText, setOptions } = useFactCheckStore();
  const [estimatedCost, setEstimatedCost] = useState(0);

  // 计算预估成本（简单估算：每1000字符约$0.05）
  const calculateEstimatedCost = useCallback((text: string) => {
    const charCount = text.length;
    const estimated = Math.max(0.05, (charCount / 1000) * 0.15); // 最低$0.05
    setEstimatedCost(estimated);
  }, []);

  // 处理文本变化
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const text = e.target.value;
    setInputText(text);
    calculateEstimatedCost(text);
  };

  // 处理选项变化
  const handleOptionChange = (key: keyof FactCheckOptions, value: boolean) => {
    setOptions({ [key]: value });
  };

  // 开始核查
  const handleStart = async () => {
    if (!input_text.trim()) {
      alert('请输入需要核查的文本');
      return;
    }

    if (input_text.length < 10) {
      alert('文本长度至少需要10个字符');
      return;
    }

    await onStartFactCheck(input_text, options);
  };

  // 示例文本
  const loadExampleText = () => {
    const exampleText = `据报道，苹果公司在2024年第四季度的营收达到了946亿美元，同比增长6%。公司计划在2025年春季发布新款MacBook Pro，搭载M4 Pro芯片。分析师预计苹果股价将在未来6个月内上涨15%。`;
    setInputText(exampleText);
    calculateEstimatedCost(exampleText);
  };

  const isDisabled = isProcessing || connectionStatus !== 'connected';

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
          📝 请输入需要核查的投资相关文本
        </h3>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 文本输入区 */}
        <div className="relative">
          <textarea
            value={input_text}
            onChange={handleTextChange}
            placeholder="例如：据报道，苹果公司在2024年Q4的营收达到了946亿美元，同比增长6%。公司计划在2025年春季发布新款MacBook Pro，搭载M4 Pro芯片。分析师预计苹果股价将在未来6个月内上涨15%。"
            className="w-full h-32 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isDisabled}
          />
          <div className="absolute bottom-2 right-2 text-xs text-gray-500 bg-white/90 px-2 py-1 rounded">
            字数: {input_text.length} | 预估成本: ${estimatedCost.toFixed(2)}
          </div>
        </div>

        {/* 快速示例按钮 */}
        <div className="flex justify-end">
          <Button
            variant="ghost"
            size="sm"
            onClick={loadExampleText}
            disabled={isDisabled}
          >
            加载示例文本
          </Button>
        </div>

        {/* 核查选项 */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">📋 核查选项:</h4>
          <div className="grid grid-cols-1 gap-3">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={options.enableFinancialVerification}
                onChange={(e) => handleOptionChange('enableFinancialVerification', e.target.checked)}
                disabled={isDisabled}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">财务数据验证</span>
            </label>
            
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={options.enableCorporateVerification}
                onChange={(e) => handleOptionChange('enableCorporateVerification', e.target.checked)}
                disabled={isDisabled}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">公司声明核实</span>
            </label>
            
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={options.enableNewsVerification}
                onChange={(e) => handleOptionChange('enableNewsVerification', e.target.checked)}
                disabled={isDisabled}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">市场预测分析</span>
            </label>
            
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={options.deepResearchMode}
                onChange={(e) => handleOptionChange('deepResearchMode', e.target.checked)}
                disabled={isDisabled}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">深度调研模式</span>
            </label>
          </div>
        </div>

        {/* 开始按钮 */}
        <Button
          onClick={handleStart}
          disabled={isDisabled || !input_text.trim()}
          loading={isProcessing}
          className="w-full"
          size="lg"
        >
          {isProcessing ? '核查进行中...' : '🚀 开始核查'}
        </Button>

        {/* 状态提示 */}
        {connectionStatus !== 'connected' && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              {connectionStatus === 'connecting' ? '正在连接服务器...' : '连接服务器失败，请刷新页面重试'}
            </p>
          </div>
        )}

        {/* 提示信息 */}
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p className="text-sm text-blue-800">
            💡 提示: 系统将自动提取声明、交叉验证数据源，并在发现冲突时组织AI辩论
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default TextInputArea;