/**
 * 辩论查看器组件
 * 以模态框形式显示AI专家辩论过程
 */
'use client';

import React from 'react';
import { DebateRecord } from '../../store/factCheckStore';
import { Button } from '../../ui/Button';

interface DebateViewerProps {
  debate: DebateRecord;
  onClose: () => void;
}

const DebateViewer: React.FC<DebateViewerProps> = ({ debate, onClose }) => {
  // Agent角色显示名称映射
  const getAgentDisplayName = (role: string) => {
    const roleMap = {
      'financial_verifier': '💰 财务验证专家',
      'corporate_verifier': '🏢 公司声明验证员',
      'news_verifier': '📰 新闻验证专家',
      'debate_moderator': '⚖️ 辩论主持人',
      'orchestrator': '🎯 项目总监'
    };
    return roleMap[role] || `🤖 ${role}`;
  };

  // 获取支持度颜色
  const getSupportLevelColor = (level?: number) => {
    if (!level) return 'bg-gray-100';
    if (level >= 0.8) return 'bg-green-100 text-green-800';
    if (level >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  // 格式化时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                🎭 AI专家辩论
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                辩论主题: {debate.topic}
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                debate.status === 'concluded' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-blue-100 text-blue-800'
              }`}>
                {debate.status === 'concluded' ? '✅ 已结束' : '🔄 进行中'}
              </div>
              <Button variant="ghost" onClick={onClose}>
                ❌
              </Button>
            </div>
          </div>

          {/* 参与者列表 */}
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">参与专家:</h4>
            <div className="flex flex-wrap gap-2">
              {debate.participants.map((participant, idx) => (
                <span 
                  key={idx}
                  className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                >
                  {getAgentDisplayName(participant)}
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* 辩论消息列表 */}
        <div className="p-6 overflow-y-auto max-h-96">
          <div className="space-y-4">
            {debate.messages.map((message, idx) => (
              <div key={idx} className="flex gap-4">
                {/* Agent头像和名称 */}
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-lg">
                      {getAgentDisplayName(message.agent_role).split(' ')[0]}
                    </span>
                  </div>
                </div>

                {/* 消息内容 */}
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-gray-900">
                      {getAgentDisplayName(message.agent_role)}
                    </span>
                    {message.support_level !== undefined && (
                      <span className={`px-2 py-1 rounded-full text-xs ${getSupportLevelColor(message.support_level)}`}>
                        支持度: {Math.round(message.support_level * 100)}%
                      </span>
                    )}
                    <span className="text-xs text-gray-500">
                      {formatTime(message.timestamp)}
                    </span>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-3">
                    <p className="text-gray-800 text-sm leading-relaxed">
                      {message.message}
                    </p>

                    {/* 证据列表 */}
                    {message.evidence && message.evidence.length > 0 && (
                      <div className="mt-3">
                        <h5 className="text-xs font-medium text-gray-600 mb-1">
                          📋 证据支撑:
                        </h5>
                        <ul className="space-y-1">
                          {message.evidence.map((evidence, evidenceIdx) => (
                            <li key={evidenceIdx} className="text-xs text-gray-600 flex items-start gap-1">
                              <span className="text-blue-500 mt-0.5">•</span>
                              <span>{evidence}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 辩论结论 */}
        {debate.conclusion && (
          <div className="p-6 border-t border-gray-200 bg-gray-50">
            <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
              🎯 辩论结论
            </h4>
            <div className="bg-white rounded-lg p-4 border">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">综合评分:</span>
                <span className="text-lg font-bold text-blue-600">
                  {debate.confidence ? Math.round(debate.confidence * 100) : 0}/100
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                <div 
                  className={`h-2 rounded-full ${
                    (debate.confidence || 0) >= 0.7 ? 'bg-green-500' :
                    (debate.confidence || 0) >= 0.5 ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`}
                  style={{ width: `${(debate.confidence || 0) * 100}%` }}
                />
              </div>
              <p className="text-gray-800 text-sm leading-relaxed">
                {debate.conclusion}
              </p>
            </div>
          </div>
        )}

        {/* 底部操作按钮 */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              {debate.messages.length} 条辩论消息
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                📋 复制辩论记录
              </Button>
              <Button variant="outline" size="sm">
                📥 导出辩论
              </Button>
              <Button onClick={onClose}>
                关闭
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebateViewer;