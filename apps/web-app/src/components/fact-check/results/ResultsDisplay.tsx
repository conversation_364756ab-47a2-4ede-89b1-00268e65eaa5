/**
 * 结果显示组件
 * 展示事实核查的最终结果和详细信息
 */
'use client';

import React, { useState } from 'react';
import { FactCheckTask } from '../../store/factCheckStore';
import { Card, CardContent, CardHeader } from '../../ui/Card';
import { Button } from '../../ui/Button';
import ClaimVerificationCard from './ClaimVerificationCard';
import <PERSON><PERSON><PERSON>ie<PERSON> from './DebateViewer';

interface ResultsDisplayProps {
  task: FactCheckTask;
}

const ResultsDisplay: React.FC<ResultsDisplayProps> = ({ task }) => {
  const [selectedClaimId, setSelectedClaimId] = useState<string | null>(null);
  const [showDebateViewer, setShowDebateViewer] = useState(false);
  const [activeDebateId, setActiveDebateId] = useState<string | null>(null);

  // 计算摘要统计
  const getSummaryStats = () => {
    const total = task.claims.length;
    const verified = task.claims.filter(c => c.status === 'verified').length;
    const contradicted = task.claims.filter(c => c.status === 'contradicted').length;
    const conflicted = task.claims.filter(c => c.status === 'conflicted').length;
    const unverified = task.claims.filter(c => c.status === 'unverified').length;
    const errors = task.claims.filter(c => c.status === 'error').length;
    
    return { total, verified, contradicted, conflicted, unverified, errors };
  };

  // 计算处理时间
  const getProcessingTime = () => {
    if (!task.end_time) return '未知';
    const elapsed = Math.floor((task.end_time - task.start_time) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    return minutes > 0 ? `${minutes}分${seconds}秒` : `${seconds}秒`;
  };

  // 导出报告
  const handleExportReport = () => {
    const reportData = {
      task_id: task.id,
      timestamp: new Date().toISOString(),
      original_text: task.original_text,
      summary: getSummaryStats(),
      claims: task.claims,
      debates: task.debates,
      total_cost: task.total_cost,
      processing_time: getProcessingTime()
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `fact-check-report-${task.id}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 重新核查
  const handleRecheck = () => {
    // TODO: 实现重新核查功能
    alert('重新核查功能开发中...');
  };

  // 查看辩论
  const handleViewDebate = (debateId: string) => {
    setActiveDebateId(debateId);
    setShowDebateViewer(true);
  };

  const stats = getSummaryStats();
  const processingTime = getProcessingTime();

  return (
    <div className="space-y-6">
      {/* 摘要卡片 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                ✅ 事实核查完成
              </h3>
              <p className="text-sm text-gray-600">
                任务ID: {task.id}
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-green-600">
                完成
              </div>
              <div className="text-xs text-gray-500">
                ⏱️ 用时: {processingTime}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 核查摘要统计 */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 p-4 bg-gray-50 rounded-lg mb-4">
            <div className="text-center">
              <div className="text-xl font-bold text-gray-900">{stats.total}</div>
              <div className="text-xs text-gray-600">总声明</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-green-600">{stats.verified}</div>
              <div className="text-xs text-gray-600">✅ 已验证</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-red-600">{stats.contradicted}</div>
              <div className="text-xs text-gray-600">❌ 存在矛盾</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-yellow-600">{stats.conflicted}</div>
              <div className="text-xs text-gray-600">⚠️ 信息冲突</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-gray-600">{stats.unverified}</div>
              <div className="text-xs text-gray-600">❓ 未能验证</div>
            </div>
          </div>

          {/* 成本和时间信息 */}
          <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-4">
              <div>
                <span className="text-sm text-gray-600">总成本: </span>
                <span className="font-medium text-blue-900">${task.total_cost.toFixed(3)}</span>
              </div>
              <div>
                <span className="text-sm text-gray-600">处理时间: </span>
                <span className="font-medium text-blue-900">{processingTime}</span>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleExportReport}>
                📥 导出报告
              </Button>
              <Button variant="outline" size="sm" onClick={handleRecheck}>
                🔄 重新核查
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 详细结果 */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-medium text-gray-900">
            🔍 详细核查结果
          </h3>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {task.claims.map((claim, index) => (
              <ClaimVerificationCard
                key={claim.id}
                claim={claim}
                index={index + 1}
                isSelected={selectedClaimId === claim.id}
                onClick={() => setSelectedClaimId(
                  selectedClaimId === claim.id ? null : claim.id
                )}
                onViewDebate={
                  task.debates.find(d => d.claim_id === claim.id) ?
                  () => {
                    const debate = task.debates.find(d => d.claim_id === claim.id);
                    if (debate) handleViewDebate(debate.id);
                  } : undefined
                }
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 辩论查看器模态框 */}
      {showDebateViewer && activeDebateId && (
        <DebateViewer
          debate={task.debates.find(d => d.id === activeDebateId)!}
          onClose={() => {
            setShowDebateViewer(false);
            setActiveDebateId(null);
          }}
        />
      )}
    </div>
  );
};

export default ResultsDisplay;