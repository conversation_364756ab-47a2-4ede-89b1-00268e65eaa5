/**
 * 声明验证卡片组件
 * 显示单个声明的验证结果
 */
'use client';

import React from 'react';
import { Claim } from '../../store/factCheckStore';
import { Button } from '../../ui/Button';

interface ClaimVerificationCardProps {
  claim: Claim;
  index: number;
  isSelected: boolean;
  onClick: () => void;
  onViewDebate?: () => void;
}

const ClaimVerificationCard: React.FC<ClaimVerificationCardProps> = ({
  claim,
  index,
  isSelected,
  onClick,
  onViewDebate
}) => {
  // 获取状态颜色和图标
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'verified':
        return {
          icon: '✅',
          text: '已验证',
          color: 'text-green-800 bg-green-100 border-green-200',
          bgColor: 'bg-green-50'
        };
      case 'contradicted':
        return {
          icon: '❌',
          text: '存在矛盾',
          color: 'text-red-800 bg-red-100 border-red-200',
          bgColor: 'bg-red-50'
        };
      case 'conflicted':
        return {
          icon: '⚠️',
          text: '信息冲突',
          color: 'text-yellow-800 bg-yellow-100 border-yellow-200',
          bgColor: 'bg-yellow-50'
        };
      case 'unverified':
        return {
          icon: '❓',
          text: '未能验证',
          color: 'text-gray-800 bg-gray-100 border-gray-200',
          bgColor: 'bg-gray-50'
        };
      case 'error':
        return {
          icon: '💥',
          text: '验证错误',
          color: 'text-red-800 bg-red-100 border-red-200',
          bgColor: 'bg-red-50'
        };
      default:
        return {
          icon: '⏳',
          text: '处理中',
          color: 'text-blue-800 bg-blue-100 border-blue-200',
          bgColor: 'bg-blue-50'
        };
    }
  };

  // 获取声明类型显示
  const getTypeDisplay = (type: string) => {
    switch (type) {
      case 'financial_data':
        return { icon: '💰', text: '财务数据' };
      case 'corporate_statement':
        return { icon: '🏢', text: '公司声明' };
      case 'market_prediction':
        return { icon: '📈', text: '市场预测' };
      case 'news_fact':
        return { icon: '📰', text: '新闻事实' };
      default:
        return { icon: '📄', text: '一般声明' };
    }
  };

  const statusDisplay = getStatusDisplay(claim.status);
  const typeDisplay = getTypeDisplay(claim.type);

  return (
    <div 
      className={`border rounded-lg transition-all duration-200 cursor-pointer ${
        isSelected ? 'border-blue-500 shadow-md' : 'border-gray-200 hover:border-gray-300'
      } ${statusDisplay.bgColor}`}
      onClick={onClick}
    >
      <div className="p-4">
        {/* 头部：序号、类型、状态 */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <span className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
              {index}
            </span>
            <div className="flex items-center gap-1">
              <span>{typeDisplay.icon}</span>
              <span className="text-sm text-gray-600">{typeDisplay.text}</span>
            </div>
          </div>
          <div className={`px-3 py-1 rounded-full text-sm font-medium border ${statusDisplay.color}`}>
            <span className="mr-1">{statusDisplay.icon}</span>
            {statusDisplay.text}
          </div>
        </div>

        {/* 声明文本 */}
        <div className="mb-3">
          <p className="text-gray-900 font-medium">
            "{claim.text}"
          </p>
        </div>

        {/* 置信度 */}
        {claim.confidence > 0 && (
          <div className="mb-3">
            <div className="flex items-center justify-between text-sm mb-1">
              <span className="text-gray-600">置信度</span>
              <span className="font-medium">{Math.round(claim.confidence * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  claim.confidence >= 0.8 ? 'bg-green-500' :
                  claim.confidence >= 0.6 ? 'bg-yellow-500' :
                  'bg-red-500'
                }`}
                style={{ width: `${claim.confidence * 100}%` }}
              />
            </div>
          </div>
        )}

        {/* 展开的详细信息 */}
        {isSelected && (
          <div className="mt-4 pt-4 border-t border-gray-200 space-y-3">
            {/* 验证结果详情 */}
            {claim.verification_result && (
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2">验证详情:</h5>
                <div className="p-3 bg-white rounded-md border">
                  {typeof claim.verification_result === 'object' ? (
                    <div className="space-y-2 text-sm">
                      {claim.verification_result.status && (
                        <div>
                          <span className="font-medium">状态: </span>
                          <span>{claim.verification_result.status}</span>
                        </div>
                      )}
                      {claim.verification_result.official_value && (
                        <div>
                          <span className="font-medium">官方数据: </span>
                          <span>{claim.verification_result.official_value}</span>
                        </div>
                      )}
                      {claim.verification_result.claimed_value && (
                        <div>
                          <span className="font-medium">声明数据: </span>
                          <span>{claim.verification_result.claimed_value}</span>
                        </div>
                      )}
                      {claim.verification_result.verification_details && (
                        <div>
                          <span className="font-medium">详细说明: </span>
                          <span>{claim.verification_result.verification_details}</span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-600">
                      {String(claim.verification_result)}
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* 数据来源 */}
            {claim.sources && claim.sources.length > 0 && (
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2">数据来源:</h5>
                <div className="space-y-2">
                  {claim.sources.map((source, idx) => (
                    <div key={idx} className="flex items-center justify-between p-2 bg-white rounded border">
                      <div>
                        <span className="text-sm font-medium">{source.name}</span>
                        {source.url && (
                          <a 
                            href={source.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="ml-2 text-xs text-blue-600 hover:text-blue-800"
                          >
                            查看来源
                          </a>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <span
                            key={i}
                            className={`text-xs ${
                              i < Math.round(source.reliability_score * 5) 
                                ? 'text-yellow-400' 
                                : 'text-gray-300'
                            }`}
                          >
                            ⭐
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex gap-2 pt-2">
              {onViewDebate && (
                <Button variant="outline" size="sm" onClick={onViewDebate}>
                  👀 查看AI辩论过程
                </Button>
              )}
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={(e) => {
                  e.stopPropagation();
                  navigator.clipboard.writeText(claim.text);
                }}
              >
                📋 复制声明
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClaimVerificationCard;