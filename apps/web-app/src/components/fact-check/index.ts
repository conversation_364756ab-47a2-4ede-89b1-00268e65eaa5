/**
 * 事实核查组件统一导出
 */

// 核心组件
export { default as FactCheckMain } from './core/FactCheckMain';

// 输入组件
export { default as TextInputArea } from './input/TextInputArea';

// 处理状态组件
export { default as ProcessingStatus } from './processing/ProcessingStatus';
export { default as AgentProgressCard } from './processing/AgentProgressCard';
export { default as CostTracker } from './processing/CostTracker';

// 结果展示组件
export { default as ResultsDisplay } from './results/ResultsDisplay';
export { default as DebateViewer } from './results/DebateViewer';
export { default as ClaimVerificationCard } from './results/ClaimVerificationCard';