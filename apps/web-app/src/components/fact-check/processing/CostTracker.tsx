/**
 * 成本追踪组件
 * 显示每日成本和当前任务成本
 */
'use client';

import React, { useState } from 'react';

interface CostTrackerProps {
  dailyCost: number;
  taskCost: number;
}

const CostTracker: React.FC<CostTrackerProps> = ({ dailyCost, taskCost }) => {
  const [showDetails, setShowDetails] = useState(false);
  
  // 每日预算
  const dailyBudget = 50.0;
  const usagePercent = Math.min((dailyCost / dailyBudget) * 100, 100);
  
  // 获取成本状态颜色
  const getCostStatusColor = () => {
    if (usagePercent < 50) return 'text-green-600';
    if (usagePercent < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  // 获取预算状态
  const getBudgetStatus = () => {
    if (usagePercent < 50) return '预算充足';
    if (usagePercent < 80) return '预算适中';
    if (usagePercent < 100) return '预算紧张';
    return '预算超限';
  };

  return (
    <div className="relative">
      <div 
        className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-2 rounded-md"
        onClick={() => setShowDetails(!showDetails)}
      >
        <span className="text-2xl">💰</span>
        <div className="text-right">
          <div className={`text-sm font-medium ${getCostStatusColor()}`}>
            今日: ${dailyCost.toFixed(2)}
          </div>
          {taskCost > 0 && (
            <div className="text-xs text-gray-500">
              本次: ${taskCost.toFixed(3)}
            </div>
          )}
        </div>
      </div>

      {/* 详细信息弹出框 */}
      {showDetails && (
        <div className="absolute right-0 top-full mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-4">
            <h4 className="font-medium text-gray-900 mb-3">成本追踪详情</h4>
            
            {/* 每日预算使用情况 */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">每日预算使用</span>
                <span className={`text-sm font-medium ${getCostStatusColor()}`}>
                  {usagePercent.toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    usagePercent < 50 ? 'bg-green-500' :
                    usagePercent < 80 ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`}
                  style={{ width: `${Math.min(usagePercent, 100)}%` }}
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>${dailyCost.toFixed(2)}</span>
                <span>${dailyBudget.toFixed(2)}</span>
              </div>
            </div>

            {/* 成本详情 */}
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">今日总成本:</span>
                <span className="font-medium">${dailyCost.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">当前任务:</span>
                <span className="font-medium">${taskCost.toFixed(3)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">剩余预算:</span>
                <span className={`font-medium ${
                  dailyBudget - dailyCost > 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  ${Math.max(0, dailyBudget - dailyCost).toFixed(2)}
                </span>
              </div>
            </div>

            {/* 预算状态 */}
            <div className={`p-2 rounded-md text-sm ${
              usagePercent < 50 ? 'bg-green-50 text-green-800' :
              usagePercent < 80 ? 'bg-yellow-50 text-yellow-800' :
              'bg-red-50 text-red-800'
            }`}>
              <div className="flex items-center gap-2">
                <span>
                  {usagePercent < 50 ? '✅' :
                   usagePercent < 80 ? '⚠️' : '🚨'}
                </span>
                <span>{getBudgetStatus()}</span>
              </div>
            </div>

            {/* 成本优化提示 */}
            {usagePercent > 70 && (
              <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-xs text-blue-800">
                  💡 提示: 考虑启用深度调研模式来获得更准确的结果，或选择特定的验证类型以控制成本。
                </p>
              </div>
            )}

            {/* 关闭按钮 */}
            <div className="mt-3 flex justify-end">
              <button
                onClick={() => setShowDetails(false)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 点击外部关闭 */}
      {showDetails && (
        <div 
          className="fixed inset-0 z-40"
          onClick={() => setShowDetails(false)}
        />
      )}
    </div>
  );
};

export default CostTracker;