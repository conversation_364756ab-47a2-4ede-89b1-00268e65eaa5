/**
 * 处理状态组件
 * 显示各Agent的工作进度和实时状态
 */
'use client';

import React from 'react';
import { FactCheckTask } from '../../store/factCheckStore';
import { Card, CardContent } from '../../ui/Card';

interface ProcessingStatusProps {
  task: FactCheckTask;
  onCancel: () => void;
}

const ProcessingStatus: React.FC<ProcessingStatusProps> = ({ task, onCancel }) => {
  // 计算总体进度
  const calculateOverallProgress = () => {
    if (!task.agents.length) return 0;
    const totalProgress = task.agents.reduce((sum, agent) => sum + agent.progress, 0);
    return Math.round(totalProgress / task.agents.length);
  };

  const overallProgress = calculateOverallProgress();

  return (
    <Card>
      <CardContent className="p-6">
        {/* 分析进度 */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium text-gray-900">分析进度</h3>
            <span className="text-sm font-medium text-gray-700">
              {overallProgress}%
            </span>
          </div>
          
          {/* 进度条 */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${overallProgress}%` }}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProcessingStatus;