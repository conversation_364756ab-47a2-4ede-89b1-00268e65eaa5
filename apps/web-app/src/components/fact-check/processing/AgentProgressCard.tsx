/**
 * Agent进度卡片组件
 * 显示单个Agent的工作状态和进度
 */
'use client';

import React from 'react';
import { AgentStatus } from '../../store/factCheckStore';

interface AgentProgressCardProps {
  agent: AgentStatus;
  displayName: string;
}

const AgentProgressCard: React.FC<AgentProgressCardProps> = ({ agent, displayName }) => {
  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'working':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'working':
        return '🔄';
      case 'error':
        return '❌';
      default:
        return '⏳';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'working':
        return '进行中';
      case 'error':
        return '错误';
      case 'idle':
        return '等待中';
      default:
        return status;
    }
  };

  const progressPercent = Math.round(agent.progress * 100);
  const statusColor = getStatusColor(agent.status);
  const statusIcon = getStatusIcon(agent.status);
  const statusText = getStatusText(agent.status);

  return (
    <div className={`p-4 rounded-lg border ${statusColor}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <span className="text-lg">{statusIcon}</span>
          <h4 className="font-medium text-gray-900">
            {displayName}
          </h4>
        </div>
        <div className="flex items-center gap-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColor}`}>
            {statusText}
          </span>
          <span className="text-sm font-medium text-gray-700">
            {progressPercent}%
          </span>
        </div>
      </div>

      {/* 进度条 */}
      <div className="mb-2">
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              agent.status === 'completed' ? 'bg-green-500' :
              agent.status === 'working' ? 'bg-blue-500' :
              agent.status === 'error' ? 'bg-red-500' :
              'bg-gray-400'
            }`}
            style={{ width: `${progressPercent}%` }}
          />
        </div>
      </div>

      {/* 当前任务描述 */}
      {agent.current_task && (
        <div className="text-sm text-gray-600">
          <span className="font-medium">当前任务: </span>
          {agent.current_task}
        </div>
      )}

      {/* 错误信息 */}
      {agent.status === 'error' && (
        <div className="mt-2 text-sm text-red-600">
          <span className="font-medium">错误: </span>
          {/* 显示错误信息的前100个字符 */}
          {typeof agent.current_task === 'string' ? 
            agent.current_task.substring(0, 100) : 
            '未知错误'
          }
        </div>
      )}

      {/* 最后更新时间 */}
      <div className="mt-2 text-xs text-gray-500">
        最后更新: {new Date(agent.last_update).toLocaleTimeString()}
      </div>
    </div>
  );
};

export default AgentProgressCard;