import React from 'react';
import { CheckCircle, Clock, AlertCircle, FileText } from 'lucide-react';

export interface TaskInfo {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  order: number;
}

interface TaskStatusCardProps {
  task: TaskInfo;
  isActive: boolean;
  onClick: () => void;
}

export function TaskStatusCard({ task, isActive, onClick }: TaskStatusCardProps) {
  const getTaskIcon = () => {
    switch (task.status) {
      case 'completed':
        return <CheckCircle className="w-6 h-6 text-green-500" />;
      case 'in_progress':
        return <Clock className="w-6 h-6 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="w-6 h-6 text-red-500" />;
      default:
        return <FileText className="w-6 h-6 text-gray-400" />;
    }
  };

  const getBorderColor = () => {
    switch (task.status) {
      case 'completed':
        return 'border-green-200 bg-green-50';
      case 'in_progress':
        return 'border-blue-200 bg-blue-50';
      case 'failed':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getStatusText = () => {
    switch (task.status) {
      case 'completed':
        return '已完成';
      case 'in_progress':
        return 'AG-UI 进行中';
      case 'failed':
        return '失败';
      default:
        return '等待中';
    }
  };

  return (
    <div
      onClick={onClick}
      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
        getBorderColor()
      } ${isActive ? 'ring-2 ring-blue-300' : ''} hover:shadow-md`}
    >
      <div className="flex items-center space-x-3">
        {getTaskIcon()}
        <div className="flex-1">
          <h3 className="font-medium text-gray-900">{task.title}</h3>
          <p className="text-sm text-gray-600 mt-1">{task.description}</p>
          <p className="text-sm text-gray-500 mt-1">
            状态: {getStatusText()}
          </p>
        </div>
      </div>
    </div>
  );
}