import React from 'react';

interface TaskProgressCardProps {
  task: {
    id: string;
    title: string;
    description: string;
    status: string;
  };
  progress?: number; // 进度百分比，默认根据状态计算
}

export function TaskProgressCard({ task, progress }: TaskProgressCardProps) {
  // 根据任务状态计算进度
  const calculateProgress = () => {
    if (progress !== undefined) return progress;
    
    switch (task.status) {
      case 'completed':
        return 100;
      case 'in_progress':
        return 60;
      case 'pending':
      default:
        return 0;
    }
  };

  const currentProgress = calculateProgress();

  return (
    <div className={`p-4 border rounded-lg mb-3 ${
      task.status === 'completed' ? 'bg-green-50 border-green-200' :
      task.status === 'in_progress' ? 'bg-blue-50 border-blue-200' :
      'bg-gray-50 border-gray-200'
    }`}>
      {/* 只保留进度条部分 */}
      <div className="mb-2">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>任务进度</span>
          <span>{currentProgress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-500 ease-out ${
              currentProgress === 100
                ? 'bg-gradient-to-r from-green-500 to-green-600'
                : currentProgress > 0
                ? 'bg-gradient-to-r from-blue-500 to-blue-600'
                : 'bg-gray-300'
            }`}
            style={{ width: `${currentProgress}%` }}
          />
        </div>
      </div>
    </div>
  );
}