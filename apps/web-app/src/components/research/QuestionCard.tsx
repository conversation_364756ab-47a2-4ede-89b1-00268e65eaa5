'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader } from '@/components/ui/Card';

interface Question {
  id: string;
  title: string;
  content: string;
  type: string;
  status: string;
  category: string;
  icon: string;
}

interface QuestionCardProps {
  question: Question;
  index: number;
}

export function QuestionCard({ question, index }: QuestionCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'generated':
        return 'bg-blue-50 border-blue-200';
      case 'processing':
        return 'bg-yellow-50 border-yellow-200';
      case 'completed':
        return 'bg-green-50 border-green-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'fact_check':
        return 'bg-purple-100 text-purple-800';
      case 'prediction':
        return 'bg-blue-100 text-blue-800';
      case 'quantitative':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className={`mb-3 transition-all duration-200 hover:shadow-md ${getStatusColor(question.status)}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{question.icon}</span>
            <span className="text-sm font-medium text-gray-600">
              问题 {index + 1}
            </span>
          </div>
          <span className={`px-2 py-1 text-xs rounded-full font-medium ${getTypeColor(question.type)}`}>
            {question.category}
          </span>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-gray-800 leading-relaxed">
          {question.content}
        </p>
      </CardContent>
    </Card>
  );
}