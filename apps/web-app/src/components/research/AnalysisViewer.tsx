// src/components/research/AnalysisViewer.tsx

import React, { useState, useEffect } from 'react';
import { FileText, ChevronDownIcon, CheckCircleIcon, ClockIcon } from 'lucide-react';
import { QuestionsList } from './QuestionsList';
import { TaskProgressCard } from './TaskProgressCard';
import { TypewriterEffect } from '../ui/TypewriterEffect'; // 修正的导入路径

// --- 接口定义 (如果尚未在文件顶部定义) ---
interface ContentData {
  [key: string]: any;
}

interface AnalysisViewerProps {
  activeTask: string;
  contentData: Record<string, any>;
  currentStep: string;
  onStepChange?: (step: string) => void;
  currentTaskContent: Record<string, string>;
  currentTypingTaskId: string;
}
// --- 接口定义结束 ---

// --- AnalysisProcessView 组件 (如果尚未定义) ---
interface AnalysisProcessViewProps {
  currentStep: string;
  onStepChange?: (step: string) => void;
  onAnalysisComplete?: () => void;
  contentData?: Record<string, any>; // 添加 contentData 属性
}

function AnalysisProcessView({ currentStep, onStepChange, onAnalysisComplete, contentData }: AnalysisProcessViewProps) {
  const [progress, setProgress] = useState(0);
  const [isAnalysisStarted, setIsAnalysisStarted] = useState(false);

  // 模拟任务数据 (如果需要)
  const tasks = [
    // ... 任务数据 ...
  ];
  const [currentTasks, setCurrentTasks] = useState(tasks);

  useEffect(() => {
    // 检测分析是否开始
    if (currentStep && !isAnalysisStarted) {
      console.log('🚀 [AnalysisProcessView] 分析开始 - currentStep:', currentStep);
      setIsAnalysisStarted(true);
      setProgress(10);
    }

    if (isAnalysisStarted) {
      const timer = setInterval(() => {
        setProgress(prev => {
          const newProgress = Math.min(prev + 2, 100);
          console.log('⏱️ [AnalysisProcessView] 进度更新:', {
            oldProgress: prev,
            newProgress,
            currentStep
          });

          // 根据进度更新任务状态逻辑 (如果需要)
          // ...

          // 当进度达到100%时的处理 (如果需要)
          if (newProgress === 100) {
            setTimeout(() => {
              // ...
              console.log('✅ [AnalysisProcessView] 进度条完成');
            }, 1000);
          }

          return newProgress;
        });
      }, 100);

      return () => clearInterval(timer);
    }
  }, [currentStep, isAnalysisStarted, onAnalysisComplete, onStepChange]);

  return (
    <div className="space-y-6">
      {/* 头部介绍 */}
      <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
        <div className="text-gray-700 leading-relaxed">
          {!isAnalysisStarted
            ? 'AI 等待开始特斯拉降价事件的投资影响分析...'
            : progress === 100
              ? '🎉 分析完成！正在跳转到结果页面...'
              : 'AI 正在深度分析特斯拉降价事件的投资影响，请稍候...'
          }
        </div>
        {currentStep && (
          <div className="mt-2 text-sm text-blue-600">
            当前步骤: {currentStep}
          </div>
        )}
      </div>

      {/* 总体进度条 */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900 flex items-center">
            📋 分析进度
          </h2>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            progress === 100
              ? 'bg-green-500 text-white'
              : isAnalysisStarted
                ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                : 'bg-gray-300 text-gray-600'
          }`}>
            {progress === 100 ? '已完成' : isAnalysisStarted ? '进行中' : '等待开始'}
          </span>
        </div>
        <TaskProgressCard
          task={{
            id: 'overall',
            title: '总体进度',
            description: '分析进度概览',
            status: progress === 100 ? 'completed' : isAnalysisStarted ? 'in_progress' : 'pending'
          }}
          progress={progress}
        />
         {/* 添加问题列表渲染 - 在进度条下方 */}
         {contentData && contentData.questions && (
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 mt-6">
            <QuestionsList
              questions={contentData.questions}
              category={contentData.category}
              totalCount={contentData.total_count}
            />
          </div>
        )}
      </div>
    </div>
  );
}
// --- AnalysisProcessView 组件结束 ---


export function AnalysisViewer({
  activeTask,
  contentData,
  currentStep,
  onStepChange,
  currentTaskContent,
  currentTypingTaskId
}: AnalysisViewerProps) {
  // 添加详细的调试日志
  console.log('🎯 [AnalysisViewer] 组件渲染 - 当前状态:', {
    activeTask,
    hasContentData: !!contentData,
    contentDataKeys: Object.keys(contentData || {}),
    currentStep,
    hasActiveTaskData: activeTask && contentData ? !!contentData[activeTask] : false,
    currentTaskContent,
    currentTypingTaskId
  });

  // 🔍 添加完整的 contentData JSON 打印
  console.log('📋 [AnalysisViewer] 完整的 contentData JSON:', JSON.stringify(contentData, null, 2));
  console.log('📋 [AnalysisViewer] 完整的 currentTaskContent JSON:', JSON.stringify(currentTaskContent, null, 2));
  // --- 修复关键点：在组件作用域内查找 data ---
  // 查找 contentData 中与 activeTask 匹配的键
  let data: any = null;
  let matchingKey: string | null = null;
  if (activeTask && contentData) {
    // 精确匹配 activeTask
    if (contentData.hasOwnProperty(activeTask)) {
      matchingKey = activeTask;
    } else {
      // 模糊匹配（如果需要）
      matchingKey = Object.keys(contentData).find(key => key.includes(activeTask)) || null;
    }

    if (matchingKey) {
      data = contentData[matchingKey];
    }
  }

  // 在AnalysisViewer组件中修改打字机逻辑
  const effectiveTypingTaskId = currentTypingTaskId || activeTask;
  const typingContent = (
    (currentTaskContent && currentTaskContent[effectiveTypingTaskId]) ||
    ''
  );

  // 修复：只要有effectiveTypingTaskId和typingContent就显示打字机效果
  const isCurrentlyTyping = Boolean(effectiveTypingTaskId && typingContent);
  
  // 如果正在打字，显示打字机效果
  if (isCurrentlyTyping && typingContent) {
    return (
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900">正在分析 - {effectiveTypingTaskId}</h2>
        <div className="bg-white p-4 rounded-lg border">
          <TypewriterEffect 
            text={typingContent} 
            speed={30} 
            isActive={true}
            onComplete={() => {
              // 打字完成后的回调
            }}
          />
        </div>
      </div>
    );
  }

  // --- 修复关键点：现在可以在后续逻辑中安全地访问 `data` 了 ---
  // 如果有活跃任务且有对应的内容数据，显示具体任务内容
  if (activeTask && data) {
    console.log('✅ [AnalysisViewer] 条件满足 - 渲染任务内容:', {
      activeTask,
      matchingKey,
      dataKeys: Object.keys(data)
    });

    // 注意：这里不再需要重新定义 `const data = contentData[activeTask];`
    // 因为我们已经在上面的组件作用域内定义并赋值了它。

    return (
      <div className="space-y-8">
        {/* 统一使用 QuestionsList 组件展示问题 */}
        {data.questions && (
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <QuestionsList
              questions={data.questions}
              category={data.category || '分析问题'}
              totalCount={data.total_count || data.questions.length}
            />
          </div>
        )}

        {/* 添加宏观分析结果的处理 */}
        {((activeTask && activeTask.includes('macro_analysis')) || data.macro_analysis_results) && (
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">宏观分析结果</h2>
            {data.macro_analysis_results && (
              <div className="space-y-4">
                {data.macro_analysis_results.map((result: string, index: number) => (
                  <div key={index} className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h3 className="font-medium text-blue-900 mb-2">分析结果 {index + 1}</h3>
                    <div className="text-gray-700 whitespace-pre-wrap">{result}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* 根据不同的任务类型渲染对应内容 */}
        {activeTask === 'truth-verification' && (
          <div className="space-y-8">
            {/* 事件真实性验证 */}
            {data.verification_items && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">事件真实性验证完成</h2>
                <div className="space-y-3">
                  {data.verification_items.map((item: any, index: number) => (
                    <div key={index} className="flex items-start space-x-3">
                      <span className={`${item.color} font-medium`}>{item.icon}</span>
                      <span className="text-gray-700">{item.text}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 官方信息验证 */}
            {data.official_info && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">官方信息验证</h3>
                <div className="space-y-2">
                  {data.official_info.map((info: string, index: number) => (
                    <div key={index} className="flex items-start space-x-3">
                      <span className="text-blue-600">•</span>
                      <span className="text-gray-700">{info}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 市场反应分析 */}
            {data.market_analysis && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">市场反应分析</h3>
                <div className="space-y-2">
                  {data.market_analysis.map((analysis: string, index: number) => (
                    <div key={index} className="flex items-start space-x-3">
                      <span className="text-orange-600">📈</span>
                      <span className="text-gray-700">{analysis}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 数据可信度评估 */}
            {data.credibility_scores && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">数据可信度评估</h3>
                <div className="space-y-3">
                  {Object.entries(data.credibility_scores).map(([key, value]: [string, any]) => (
                    <div key={key} className="flex items-center justify-between">
                      <span className="text-gray-700">
                        {key === 'authority' ? '信息来源权威性' :
                         key === 'timeliness' ? '时效性验证' :
                         key === 'accuracy' ? '数据准确性' : '影响范围确认'}:
                      </span>
                      <div className="flex items-center space-x-1">
                        <span className="text-yellow-500">
                          {'★'.repeat(value.score)}{'☆'.repeat(5 - value.score)}
                        </span>
                        <span className="text-gray-500 text-sm">({value.desc})</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Impact Simulation 内容渲染 */}
        {activeTask === 'impact-simulation' && (
          <div className="space-y-8">
            {/* 市场影响分析 */}
            {data.market_impact && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">市场影响分析</h2>
                {/* 即时影响 */}
                {data.market_impact.immediate && (
                  <div className="mb-6">
                    <h3 className="text-md font-medium text-gray-800 mb-3">即时市场反应</h3>
                    {data.market_impact.immediate.stock_changes && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">个股变化</h4>
                        <div className="space-y-2">
                          {data.market_impact.immediate.stock_changes.map((stock: any, index: number) => (
                            <div key={index} className="flex justify-between items-center">
                              <span className="font-mono">{stock.symbol}</span>
                              <span className={`${stock.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                                {stock.change}
                              </span>
                              <span className="text-gray-500 text-sm">成交量: {stock.volume}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* 中期影响 */}
                {data.market_impact.medium_term && (
                  <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                    <h3 className="text-md font-medium text-gray-800 mb-3">中期影响预测</h3>
                    {data.market_impact.medium_term.competitive_landscape && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">竞争格局变化</h4>
                        <div className="space-y-1">
                          {data.market_impact.medium_term.competitive_landscape.map((item: string, index: number) => (
                            <div key={index} className="text-gray-600 text-sm">• {item}</div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* 财务预测 */}
            {data.financial_projections && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">财务影响预测</h2>
                {Object.entries(data.financial_projections).map(([company, projections]: [string, any]) => (
                  <div key={company} className="mb-4 p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium text-gray-800 mb-2 capitalize">{company}</h3>
                    {typeof projections === 'object' && Object.entries(projections).map(([key, value]: [string, any]) => (
                      <div key={key} className="text-sm text-gray-600 mb-1">
                        <strong>{key.replace(/_/g, ' ')}:</strong> {value}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Thesis Generation 内容渲染 */}
        {activeTask === 'thesis-generation' && (
          <div className="space-y-8">
            {/* 投资论文 */}
            {data.investment_thesis && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">投资论文生成</h2>
                {/* 利好因素 */}
                {data.investment_thesis.bullish_factors && (
                  <div className="mb-6">
                    <h3 className="text-md font-medium text-green-700 mb-3">利好因素</h3>
                    <div className="space-y-2">
                      {data.investment_thesis.bullish_factors.map((factor: string, index: number) => (
                        <div key={index} className="flex items-start space-x-2">
                          <span className="text-green-500 mt-1">▲</span>
                          <span className="text-gray-700 text-sm">{factor}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 风险因素 */}
                {data.investment_thesis.bearish_factors && (
                  <div className="mb-6">
                    <h3 className="text-md font-medium text-red-700 mb-3">风险因素</h3>
                    <div className="space-y-2">
                      {data.investment_thesis.bearish_factors.map((factor: string, index: number) => (
                        <div key={index} className="flex items-start space-x-2">
                          <span className="text-red-500 mt-1">▼</span>
                          <span className="text-gray-700 text-sm">{factor}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 关键指标 */}
                {data.investment_thesis.key_metrics && (
                  <div className="mb-6">
                    <h3 className="text-md font-medium text-gray-800 mb-3">关键估值指标</h3>
                    <div className="space-y-3">
                      {data.investment_thesis.key_metrics.map((metric: any, index: number) => (
                        <div key={index} className="bg-blue-50 p-3 rounded">
                          <div className="flex justify-between items-center mb-1">
                            <span className="font-medium">{metric.metric}</span>
                            <span className="text-blue-600 font-semibold">{metric.value}</span>
                          </div>
                          <div className="text-sm text-gray-600">{metric.rationale}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 投资组合建议 */}
            {data.portfolio_allocation && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">投资组合建议</h2>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  {Object.entries(data.portfolio_allocation).map(([key, value]: [string, any]) => (
                    <div key={key} className="mb-2 text-sm">
                      <strong>{key.replace(/_/g, ' ')}:</strong> {value}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 默认情况 - 如果没有匹配到特定类型但有 data */}
        {!['truth-verification', 'impact-simulation', 'thesis-generation'].includes(activeTask) &&
         !activeTask.includes('macro_analysis') &&
         !data.macro_analysis_results &&
         !data.questions && ( // 添加对 questions 的检查，避免与上面的 QuestionsList 冲突
          <div className="text-gray-500">暂无特定类型数据</div>
        )}
      </div>
    );
  }
  // --- 修复关键点结束 ---


  // 如果分析已完成但还没有任务数据，显示等待状态
  if (currentStep === 'analysis_completed' && (!activeTask || !contentData || !contentData[activeTask])) {
    console.log('⏳ [AnalysisViewer] 分析完成，等待任务卡片生成');
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">分析完成</h3>
          <p className="text-gray-600">正在生成任务卡片和分析结果...</p>
        </div>
      </div>
    );
  }

  // 默认显示分析过程
  console.log('📊 [AnalysisViewer] 显示分析过程视图 - 原因:', {
    noActiveTask: !activeTask,
    noContentData: !contentData || !contentData[activeTask],
    currentStep
  });

  return (
    <div className="h-full">
      <AnalysisProcessView
        currentStep={currentStep}
        onStepChange={onStepChange}
        contentData={contentData} // 传递 contentData
        onAnalysisComplete={() => {
          console.log('🎉 [AnalysisViewer] 分析完成回调触发');
        }}
      />
    </div>
  );
}