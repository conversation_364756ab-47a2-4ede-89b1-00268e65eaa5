import React from 'react';

interface MessageLogProps {
  messages: string[];
  title?: string;
  maxHeight?: string;
}

export function MessageLog({ messages, title = "AG-UI 事件日志", maxHeight = "max-h-32" }: MessageLogProps) {
  if (messages.length === 0) {
    return null;
  }

  return (
    <div className="mt-6 p-4 bg-gray-50 rounded-lg">
      <h3 className="text-sm font-medium text-gray-700 mb-2">{title}</h3>
      <div className={`space-y-1 ${maxHeight} overflow-y-auto`}>
        {messages.map((message, index) => (
          <div key={index} className="text-xs text-gray-600">
            <span className="text-gray-500">[{new Date().toLocaleTimeString()}]</span> {message}
          </div>
        ))}
      </div>
    </div>
  );
}