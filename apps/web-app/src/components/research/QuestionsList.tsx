'use client';

import React from 'react';
import { QuestionCard } from './QuestionCard';

interface Question {
  id: string;
  title: string;
  content: string;
  type: string;
  status: string;
  category: string;
  icon: string;
}

interface QuestionsListProps {
  questions: Question[];
  category: string;
  totalCount: number;
}

export function QuestionsList({ questions, category, totalCount }: QuestionsListProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          {category}
        </h3>
        <span className="text-sm text-gray-600">
          共 {totalCount} 个问题
        </span>
      </div>
      
      <div className="space-y-3">
        {questions.map((question, index) => (
          <QuestionCard
            key={question.id}
            question={question}
            index={index}
          />
        ))}
      </div>
    </div>
  );
}