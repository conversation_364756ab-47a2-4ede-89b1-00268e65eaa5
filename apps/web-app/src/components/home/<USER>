'use client';

import { 
  HeartIcon, 
  ChatBubbleLeftIcon, 
  ShareIcon,
  EyeIcon,
  ClockIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { Badge } from '@/components/ui/Badge';

interface CommunityCardProps {
  id: string;
  title: string;
  description: string;
  author: string;
  avatar: string;
  publishTime: string;
  category: string;
  tags: string[];
  stats: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
  };
  onView: () => void;
}

const mockCommunityData: CommunityCardProps[] = [
  {
    id: '1',
    title: 'Meta Q3财报超预期，AI投资回报显现',
    description: '深度分析Meta第三季度财报中的AI业务进展，重点关注Reality Labs部门的投资回报情况，以及未来AI产品商业化前景...',
    author: 'AI投资专家',
    avatar: 'M',
    publishTime: '12分钟 • 01-16',
    category: '科技股',
    tags: ['AI', '财报分析'],
    stats: { views: 1240, likes: 142, comments: 28, shares: 15 },
    onView: () => console.log('Viewing Meta analysis...'),
  },
  {
    id: '2',
    title: '产业风向标',
    description: '从上市材料到下游需求，全面解析新能源汽车产业链投资机会，包括锂电池、充电桩、智能驾驶等细分领域...',
    author: '产业分析师',
    avatar: 'P',
    publishTime: '12分钟 • 01-16',
    category: '汽车',
    tags: ['新能源', '产业链'],
    stats: { views: 3810, likes: 68, comments: 19, shares: 12 },
    onView: () => console.log('Viewing industry analysis...'),
  },
  {
    id: '3',
    title: '美联储政策转向对AI投资的影响分析',
    description: '详解美联储货币政策调整背景下，AI行业投资策略的调整方向，以及对相关公司估值的影响...',
    author: '宏观策略师',
    avatar: 'H',
    publishTime: '10分钟 • 01-16',
    category: '宏观',
    tags: ['政策', '宏观经济'],
    stats: { views: 1640, likes: 156, comments: 34, shares: 21 },
    onView: () => console.log('Viewing macro analysis...'),
  },
  {
    id: '4',
    title: '半导体行业复苏势头与投资逻辑',
    description: '基于最新芯片需求数据和行业景气度指标，分析半导体板块的投资机会和风险点，重点关注存储芯片和模拟芯片领域...',
    author: '芯片分析师',
    avatar: 'C',
    publishTime: '10分钟 • 01-16',
    category: '半导体',
    tags: ['半导体', '周期性'],
    stats: { views: 1120, likes: 34, comments: 21, shares: 71 },
    onView: () => console.log('Viewing semiconductor analysis...'),
  },
];

function CommunityCard({
  title,
  description,
  author,
  avatar,
  publishTime,
  category,
  tags,
  stats,
  onView,
}: CommunityCardProps) {
  return (
    <div className="group cursor-pointer rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all duration-300 hover:shadow-lg dark:border-gray-800 dark:bg-gray-950">
      {/* Header */}
      <div className="mb-4 flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-purple-600 text-sm font-bold text-white">
            {avatar}
          </div>
          <div>
            <p className="font-medium text-gray-900 dark:text-gray-50">{author}</p>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <ClockIcon className="h-4 w-4" />
              <span>{publishTime}</span>
            </div>
          </div>
        </div>
        <Badge variant="info" size="sm">
          {category}
        </Badge>
      </div>

      {/* Content */}
      <div className="mb-4" onClick={onView}>
        <h3 className="mb-2 text-lg font-semibold text-gray-900 group-hover:text-blue-600 dark:text-gray-50">
          {title}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
          {description}
        </p>
      </div>

      {/* Tags */}
      <div className="mb-4 flex flex-wrap gap-2">
        {tags.map((tag, index) => (
          <span
            key={index}
            className="rounded-md bg-gray-100 px-2 py-1 text-xs text-gray-600 dark:bg-gray-800 dark:text-gray-400"
          >
            {tag}
          </span>
        ))}
      </div>

      {/* Stats */}
      <div className="flex items-center justify-between border-t border-gray-100 pt-4 dark:border-gray-800">
        <div className="flex items-center space-x-6 text-sm text-gray-500">
          <div className="flex items-center space-x-1">
            <EyeIcon className="h-4 w-4" />
            <span>{stats.views}</span>
          </div>
          <div className="flex items-center space-x-1">
            <HeartIcon className="h-4 w-4" />
            <span>{stats.likes}</span>
          </div>
          <div className="flex items-center space-x-1">
            <ChatBubbleLeftIcon className="h-4 w-4" />
            <span>{stats.comments}</span>
          </div>
          <div className="flex items-center space-x-1">
            <ShareIcon className="h-4 w-4" />
            <span>{stats.shares}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export function CommunitySection() {
  return (
    <section className="w-full bg-gray-50 py-12 dark:bg-gray-900">
      <div className="container mx-auto px-4 md:px-6">
        {/* Header */}
        <div className="mb-8 flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-50 md:text-3xl">
            来自社区
          </h2>
          <a
            href="#"
            className="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500"
          >
            查看全部
          </a>
        </div>

        {/* Category Tabs */}
        <div className="mb-8 flex flex-wrap gap-2">
          {['热门', '美股', '投资策略', '市场分析', '个股研报', '风险提示'].map((category) => (
            <button
              key={category}
              className={`rounded-full px-4 py-2 text-sm font-medium transition-colors ${
                category === '热门'
                  ? 'bg-gray-900 text-white dark:bg-white dark:text-gray-900'
                  : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
          {mockCommunityData.map((item) => (
            <CommunityCard key={item.id} {...item} />
          ))}
        </div>
      </div>
    </section>
  );
} 