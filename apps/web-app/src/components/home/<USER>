'use client';

import { MagnifyingGlassIcon, PaperClipIcon, GlobeAltIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

export function HeroSearchSection() {
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();

  const handleSearch = () => {
    if (searchQuery.trim()) {
      router.push(`/research?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <section className="relative w-full bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 py-16 md:py-24">
      <div className="mx-auto max-w-4xl px-4 text-center">

        {/* Main Heading */}
        <h1 className="mb-4 text-4xl font-bold tracking-tight text-white md:text-5xl lg:text-6xl">
          From event to edge in one search
        </h1>

        {/* Subtitle */}
        <p className="mb-12 text-lg text-white/90 md:text-xl">
          Let AI verify facts, predict impact, and craft your thesis — faster, deeper, sharper than ever
        </p>

        {/* Search Box */}
        <div className="mx-auto max-w-2xl">
          <div className="relative rounded-2xl bg-white p-2 shadow-2xl">
            <div className="flex items-center space-x-3">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="输入股票名称分析其财报表现或政策影响..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="border-0 bg-transparent text-base placeholder:text-gray-500 focus:ring-0"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div className="flex items-center space-x-2">
                <button className="rounded-lg p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600">
                  <PaperClipIcon className="h-5 w-5" />
                </button>
                <Button
                  onClick={handleSearch}
                  className="rounded-xl bg-gradient-to-r from-purple-600 to-blue-600 px-6 py-3 font-medium text-white hover:from-purple-700 hover:to-blue-700"
                >
                  <MagnifyingGlassIcon className="mr-2 h-5 w-5" />
                  分析
                </Button>
              </div>
            </div>
          </div>

          {/* Search Options */}
          <div className="mt-4 flex flex-wrap justify-center gap-3 text-sm">
            <button className="rounded-full bg-white/20 px-4 py-2 text-white backdrop-blur-sm hover:bg-white/30">
              📊 深度解读
            </button>
            <button className="rounded-full bg-white/20 px-4 py-2 text-white backdrop-blur-sm hover:bg-white/30">
              📈 直接分析
            </button>
            <button className="rounded-full bg-white/20 px-4 py-2 text-white backdrop-blur-sm hover:bg-white/30">
              📎 Attach
            </button>
            <button className="rounded-full bg-white/20 px-4 py-2 text-white backdrop-blur-sm hover:bg-white/30">
              🌐 Public
            </button>
          </div>

          {/* Helper Text */}
          <p className="mt-4 text-sm text-white/70">
            为您提供专业投资建议，基于实时数据和深度分析
          </p>
        </div>
      </div>
    </section>
  );
}