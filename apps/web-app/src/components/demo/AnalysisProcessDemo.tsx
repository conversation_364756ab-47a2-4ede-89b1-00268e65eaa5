'use client';

import React, { useState, useEffect } from 'react';
import { ChevronDownIcon, CheckCircleIcon, ClockIcon } from '@heroicons/react/24/outline';

interface Task {
  id: number;
  title: string;
  status: 'completed' | 'in-progress' | 'pending';
  items: string[];
  toolCalls?: ToolCall[];
  description?: string;
}

interface ToolCall {
  type: 'search' | 'browse';
  action: string;
  query: string;
  url?: string;
}

const AnalysisProcessDemo: React.FC = () => {
  const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set([1, 2]));
  const [progress, setProgress] = useState(33);
  
  const tasks: Task[] = [
    {
      id: 1,
      title: 'Next.js 核心原理研究',
      status: 'completed',
      items: [
        '研究 Next.js 的基本概念、核心特性及其与传统 React 应用的区别',
        '分析 Next.js 的底层技术架构、工作原理及关键组件',
        '调研 Next.js 的服务器端渲染(SSR)、静态生成(SSG)和静态导出机制'
      ]
    },
    {
      id: 2,
      title: 'Next.js 高级功能与最佳实践研究',
      status: 'in-progress',
      items: [
        '研究 Next.js 的路由系统、API 路由和动态路由实现方式',
        '调研 Next.js 的数据获取方法、性能优化策略和部署方案',
        '分析 Next.js 在大型项目中的应用模式和最佳实践案例'
      ],
      description: '我将使用网络深度搜索工具来全面收集 Next.js 的高级功能和最佳实践相关信息。这个工具能帮助我系统地获取最新和最主要的技术细节。',
      toolCalls: [
        {
          type: 'search',
          action: '搜索网页',
          query: 'Next.js routing systems Pages Router vs App Router'
        },
        {
          type: 'search',
          action: '搜索网页',
          query: 'Next.js API Routes Tutorial'
        },
        {
          type: 'search',
          action: '搜索网页',
          query: 'Next.js dynamic routing examples'
        },
        {
          type: 'browse',
          action: '浏览',
          query: 'Next.js官方文档',
          url: 'https://nextjs.org/docs/pages/building-your-application/routing/api-routes'
        }
      ]
    },
    {
      id: 3,
      title: '撰写 Next.js 原理及应用详细讲解',
      status: 'pending',
      items: [
        '撰写 Next.js 原理及应用的详细讲解文档'
      ]
    }
  ];

  const additionalTasks = [
    { title: '内容分析', status: 'pending' },
    { title: '生成报告', status: 'pending' },
    { title: '导出结果', status: 'pending' }
  ];

  const toggleSection = (taskId: number) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(taskId)) {
      newExpanded.delete(taskId);
    } else {
      newExpanded.add(taskId);
    }
    setExpandedSections(newExpanded);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'in-progress':
        return <ClockIcon className="w-5 h-5 text-blue-500" />;
      default:
        return <div className="w-5 h-5 border-2 border-gray-400 rounded-full" />;
    }
  };

  const getToolIcon = (type: string) => {
    switch (type) {
      case 'search':
        return '🔍';
      case 'browse':
        return '👁️';
      default:
        return '🔧';
    }
  };

  useEffect(() => {
    // 模拟进度更新
    const timer = setTimeout(() => {
      if (progress < 66) {
        setProgress(prev => Math.min(prev + 1, 66));
      }
    }, 100);
    return () => clearTimeout(timer);
  }, [progress]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-4xl mx-auto p-8">
        <div className="space-y-8">
          {/* 头部介绍 */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="text-gray-700 leading-relaxed">
              好的，我将为您创建一个关于 Next.js 原理及应用的详细讲解计划。
            </div>
          </div>

          {/* 待办清单 */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                📋 待办清单
              </h2>
              <span className="bg-gradient-to-r from-green-500 to-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                自动执行
              </span>
            </div>

            {/* 进度条 */}
            <div className="mb-6">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>总体进度</span>
                <span>{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>

            {/* 任务列表 */}
            <div className="space-y-4">
              {tasks.map((task) => (
                <div key={task.id} className="border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
                  <div 
                    className="bg-gradient-to-r from-gray-50 to-gray-100 p-4 cursor-pointer hover:from-gray-100 hover:to-gray-200 transition-all duration-200"
                    onClick={() => toggleSection(task.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(task.status)}
                        <h3 className="text-lg font-semibold text-gray-900">
                          {task.id}. {task.title}
                        </h3>
                      </div>
                      <ChevronDownIcon 
                        className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${
                          expandedSections.has(task.id) ? 'rotate-180' : ''
                        }`}
                      />
                    </div>
                  </div>
                  
                  <div className={`bg-white transition-all duration-300 overflow-hidden ${
                    expandedSections.has(task.id) ? 'max-h-screen p-6' : 'max-h-0'
                  }`}>
                    <ul className="space-y-4 text-gray-700 mb-6">
                      {task.items.map((item, index) => (
                        <li key={index} className="flex items-start space-x-3">
                          <span className="text-blue-600 mt-1">▶</span>
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>

                    {task.description && (
                      <div className="bg-gray-50 rounded-lg p-4 mb-4">
                        <p className="text-gray-600 mb-4">{task.description}</p>
                        <p className="text-gray-600 mb-4">
                          我将分步研究Next.js高级功能与最佳实践，首先搜索了解其路由系统。
                        </p>
                      </div>
                    )}

                    {task.toolCalls && (
                      <div className="space-y-3">
                        {task.toolCalls.map((toolCall, index) => (
                          <div key={index} className="flex items-center space-x-3 p-3 bg-white rounded border hover:shadow-sm transition-shadow">
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded font-medium">
                              MCP 工具
                            </span>
                            <span className="text-lg">{getToolIcon(toolCall.type)}</span>
                            <span className="text-gray-600 text-sm font-medium">{toolCall.action}</span>
                            {toolCall.url ? (
                              <a 
                                href={toolCall.url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 text-sm underline flex-1 truncate"
                              >
                                {toolCall.url}
                              </a>
                            ) : (
                              <span className="text-gray-500 text-sm flex-1">{toolCall.query}</span>
                            )}
                          </div>
                        ))}
                        
                        {task.id === 2 && (
                          <p className="text-gray-600 mt-4">
                            我初步分析了Next.js的App和Pages路由系统、API路由及动态路由，计划深入研究官方文档并学习大型项目实践。
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 执行状态 */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="text-gray-700 leading-relaxed mb-4">
              我将按照计划开始执行任务。首先，我会处理第一个阶段，研究 Next.js 的核心原理与技术架构。
            </div>

            {/* 其他任务项 */}
            <div className="space-y-3">
              {additionalTasks.map((task, index) => (
                <div key={index} className="rounded-lg p-4 border border-gray-200 bg-gray-50 hover:bg-gray-100 hover:-translate-y-0.5 transition-all duration-200">
                  <div className="flex items-center">
                    <div className="w-6 h-6 flex items-center justify-center">
                      <div className="w-5 h-5 border-2 border-gray-400 rounded-full" />
                    </div>
                    <span className="ml-3 text-gray-900 font-medium">
                      {index + 2}. {task.title}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalysisProcessDemo;