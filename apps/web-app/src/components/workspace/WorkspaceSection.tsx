'use client';

import { WorkspaceCard, WorkspaceCardProps } from './WorkspaceCard';

const mockWorkspaceData: WorkspaceCardProps[] = [
  {
    status: 'in-progress',
    title: '特斯拉财报分析',
    description: 'Q3 2024财报深度解读及投资建议',
    reportType: '分析报告',
    time: '2小时前',
    sentiment: 'positive',
    onView: () => console.log('Viewing Tesla report...'),
  },
  {
    status: 'completed',
    title: 'AI芯片行业调研',
    description: 'NVIDIA财报后的行业影响分析',
    reportType: '行业分析',
    time: '1天前',
    sentiment: 'positive',
    onView: () => console.log('Viewing AI chip report...'),
  },
  {
    status: 'completed',
    title: '新能源政策影响',
    description: '欧盟新能源补贴政策对相关公司的影响',
    reportType: '政策分析',
    time: '3天前',
    sentiment: 'neutral',
    onView: () => console.log('Viewing new energy report...'),
  },
];

export function WorkspaceSection() {
  return (
    <section className="w-full py-8 md:py-12">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mb-8 flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-50 md:text-3xl">
            我的工作区
          </h2>
          <a
            href="#"
            className="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500"
          >
            查看全部
          </a>
        </div>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {mockWorkspaceData.map((item, index) => (
            <WorkspaceCard key={index} {...item} />
          ))}
        </div>
      </div>
    </section>
  );
}