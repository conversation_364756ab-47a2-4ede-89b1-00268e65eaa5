'use client';

import {
  ClockIcon,
  DocumentTextIcon,
  ArrowUpIcon,
  ArrowRightIcon,
  MinusSmallIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import { Badge } from '@/components/ui/Badge';
import { Button, ButtonProps } from '@/components/ui/Button';
import clsx from 'clsx';

type Status = 'in-progress' | 'completed';
type Sentiment = 'positive' | 'negative' | 'neutral';

export interface WorkspaceCardProps {
  status: Status;
  title: string;
  description: string;
  reportType: string;
  time: string;
  sentiment: Sentiment;
  onView: () => void;
}

const sentimentConfig: Record<
  Sentiment,
  {
    icon: React.ElementType;
    text: string;
    className: string;
    buttonVariant: ButtonProps['variant'];
  }
> = {
  positive: {
    icon: ArrowUpIcon,
    text: '看涨',
    className: 'text-green-500',
    buttonVariant: 'success',
  },
  negative: {
    icon: ArrowDownIcon,
    text: '看跌',
    className: 'text-red-500',
    buttonVariant: 'danger',
  },
  neutral: {
    icon: MinusSmallIcon,
    text: '中性',
    className: 'text-gray-500',
    buttonVariant: 'secondary',
  },
};

const statusConfig = {
  'in-progress': {
    text: '进行中',
    variant: 'warning',
  },
  completed: {
    text: '已完成',
    variant: 'success',
  },
};

export function WorkspaceCard({
  status,
  title,
  description,
  reportType,
  time,
  sentiment,
  onView,
}: WorkspaceCardProps) {
  const {
    icon: SentimentIcon,
    text: sentimentText,
    className: sentimentClassName,
    buttonVariant,
  } = sentimentConfig[sentiment] || sentimentConfig.neutral;

  const { text: statusText, variant: statusVariant } = statusConfig[status];

  return (
    <div className="flex flex-col rounded-xl border border-gray-200 bg-white p-5 shadow-sm transition-shadow duration-300 hover:shadow-lg dark:border-gray-800 dark:bg-gray-950">
      <div className="mb-3 flex items-center justify-between">
        <Badge
          variant={
            status === 'in-progress'
              ? 'warning'
              : status === 'completed'
              ? 'success'
              : 'default'
          }
          size="sm"
        >
          {statusText}
        </Badge>
      </div>
      <div className="flex-grow">
        <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-50">
          {title}
        </h3>
        <p className="mb-4 text-sm text-gray-600 dark:text-gray-400">
          {description}
        </p>
      </div>
      <div className="mb-4 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
        <div className="flex items-center">
          <DocumentTextIcon className="mr-1.5 h-4 w-4" />
          <span>{reportType}</span>
        </div>
        <div className="flex items-center">
          <ClockIcon className="mr-1.5 h-4 w-4" />
          <span>{time}</span>
        </div>
      </div>
      <div className="flex items-center justify-end">
        {status === 'in-progress' ? (
          <Button
            variant="primary"
            size="sm"
            onClick={onView}
            className="group"
          >
            <span>查看</span>
            <ArrowRightIcon className="ml-1.5 h-4 w-4 transition-transform duration-200 group-hover:translate-x-1" />
          </Button>
        ) : (
          <Button
            variant={buttonVariant}
            size="sm"
            onClick={onView}
            className={clsx(
              'group',
              sentiment !== 'neutral' && sentimentClassName
            )}
          >
            <SentimentIcon
              className={clsx('mr-1.5 h-4 w-4', sentimentClassName)}
            />
            <span>{sentimentText}</span>
          </Button>
        )}
      </div>
    </div>
  );
}