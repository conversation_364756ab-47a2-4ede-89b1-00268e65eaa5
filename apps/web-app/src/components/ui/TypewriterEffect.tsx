import React, { useState, useEffect, useRef } from 'react';

interface TypewriterEffectProps {
  text: string;
  speed?: number;
  isActive?: boolean;
  onComplete?: () => void;
}

export function TypewriterEffect({ 
  text, 
  speed = 50, 
  isActive = true, 
  onComplete 
}: TypewriterEffectProps) {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const previousTextRef = useRef('');
  const isCompletedRef = useRef(false);

  useEffect(() => {
    if (!isActive || !text) {
      setDisplayedText(text);
      return;
    }

    // 检查是否是增量更新（新文本包含旧文本）
    const isIncremental = text.startsWith(previousTextRef.current);
    
    if (!isIncremental) {
      // 完全不同的文本，重置打字机
      setCurrentIndex(0);
      setDisplayedText('');
      isCompletedRef.current = false;
      previousTextRef.current = text;
      return;
    }
    
    // 增量更新，继续从当前位置打字
    previousTextRef.current = text;
    
    // 如果已经完成，不再继续
    if (isCompletedRef.current && currentIndex >= text.length) {
      return;
    }
    
    // 重置完成状态，因为有新内容
    if (currentIndex >= previousTextRef.current.length && text.length > previousTextRef.current.length) {
      isCompletedRef.current = false;
    }

    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayedText(text.slice(0, currentIndex + 1));
        setCurrentIndex(currentIndex + 1);
      }, speed);

      return () => clearTimeout(timer);
    } else if (onComplete && !isCompletedRef.current) {
      isCompletedRef.current = true;
      onComplete();
    }
  }, [text, currentIndex, speed, isActive, onComplete]);

  return (
    <span className="inline-block">
      {displayedText}
      {isActive && currentIndex < text.length && (
        <span className="animate-pulse">|</span>
      )}
    </span>
  );
}