"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';

interface FinancialData {
  year: string;
  revenue: number;
  netIncome: number;
  grossProfit: number;
  operatingIncome: number;
}

interface FinancialChartProps {
  data?: FinancialData[];
  type?: 'revenue' | 'profit' | 'comparison' | 'trend';
  title?: string;
  company?: string;
}

export function FinancialChart({ 
  data = [], 
  type = 'revenue',
  title,
  company = "示例公司"
}: FinancialChartProps) {

  // 生成示例数据
  const sampleData: FinancialData[] = [
    {
      year: '2021',
      revenue: 365.8,
      netIncome: 94.7,
      grossProfit: 152.8,
      operatingIncome: 108.9
    },
    {
      year: '2022', 
      revenue: 394.3,
      netIncome: 99.8,
      grossProfit: 170.8,
      operatingIncome: 119.4
    },
    {
      year: '2023',
      revenue: 383.3,
      netIncome: 97.0,
      grossProfit: 169.1,
      operatingIncome: 114.3
    }
  ];

  const chartData = data.length > 0 ? data : sampleData;

  const formatCurrency = (value: number) => `$${value.toFixed(1)}B`;

  const colors = {
    revenue: '#3B82F6',
    netIncome: '#10B981', 
    grossProfit: '#F59E0B',
    operatingIncome: '#8B5CF6'
  };

  const renderRevenueChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="year" />
        <YAxis tickFormatter={formatCurrency} />
        <Tooltip 
          formatter={(value: number) => [formatCurrency(value), '营业收入']}
          labelStyle={{ color: '#374151' }}
        />
        <Legend />
        <Bar 
          dataKey="revenue" 
          fill={colors.revenue} 
          name="营业收入"
          radius={[4, 4, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );

  const renderProfitChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="year" />
        <YAxis tickFormatter={formatCurrency} />
        <Tooltip 
          formatter={(value: number, name: string) => [formatCurrency(value), name]}
          labelStyle={{ color: '#374151' }}
        />
        <Legend />
        <Bar 
          dataKey="grossProfit" 
          fill={colors.grossProfit} 
          name="毛利润"
          radius={[2, 2, 0, 0]}
        />
        <Bar 
          dataKey="operatingIncome" 
          fill={colors.operatingIncome} 
          name="营业利润"
          radius={[2, 2, 0, 0]}
        />
        <Bar 
          dataKey="netIncome" 
          fill={colors.netIncome} 
          name="净利润"
          radius={[2, 2, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );

  const renderComparisonChart = () => {
    const latestYear = chartData[chartData.length - 1];
    const pieData = [
      { name: '净利润', value: latestYear.netIncome, color: colors.netIncome },
      { name: '其他费用', value: latestYear.grossProfit - latestYear.netIncome, color: '#E5E7EB' }
    ];

    return (
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={pieData}
            cx="50%"
            cy="50%"
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percent }) => `${name} ${percent ? (percent * 100).toFixed(1) : 0}%`}
          >
            {pieData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip formatter={(value: number) => formatCurrency(value)} />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  const renderTrendChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="year" />
        <YAxis tickFormatter={formatCurrency} />
        <Tooltip 
          formatter={(value: number, name: string) => [formatCurrency(value), name]}
          labelStyle={{ color: '#374151' }}
        />
        <Legend />
        <Line 
          type="monotone" 
          dataKey="revenue" 
          stroke={colors.revenue} 
          name="营业收入"
          strokeWidth={3}
          dot={{ fill: colors.revenue, strokeWidth: 2, r: 6 }}
        />
        <Line 
          type="monotone" 
          dataKey="netIncome" 
          stroke={colors.netIncome} 
          name="净利润"
          strokeWidth={3}
          dot={{ fill: colors.netIncome, strokeWidth: 2, r: 6 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  const getChartTitle = () => {
    if (title) return title;
    
    const typeNames = {
      revenue: '营业收入分析',
      profit: '利润结构分析', 
      comparison: '利润占比分析',
      trend: '财务趋势分析'
    };
    
    return typeNames[type];
  };

  const renderChart = () => {
    switch (type) {
      case 'revenue':
        return renderRevenueChart();
      case 'profit':
        return renderProfitChart();
      case 'comparison':
        return renderComparisonChart();
      case 'trend':
        return renderTrendChart();
      default:
        return renderRevenueChart();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-2">
          📊 {getChartTitle()}
        </h3>
        <p className="text-sm text-gray-600">
          {company} · 近3年财务数据
        </p>
      </div>
      
      <div className="w-full">
        {renderChart()}
      </div>
      
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-800 mb-2">
          📈 关键洞察
        </h4>
        <div className="text-xs text-gray-600 space-y-1">
          {type === 'revenue' && (
            <p>• 营业收入呈现{chartData[chartData.length - 1].revenue > chartData[0].revenue ? '增长' : '下降'}趋势</p>
          )}
          {type === 'profit' && (
            <>
              <p>• 毛利润率保持相对稳定</p>
              <p>• 净利润受营业外收支影响</p>
            </>
          )}
          {type === 'trend' && (
            <p>• 收入与利润走势{chartData[chartData.length - 1].revenue > chartData[0].revenue ? '向上' : '向下'}</p>
          )}
          <p>💡 数据为模拟数据，实际分析需使用真实财务报表</p>
        </div>
      </div>
    </div>
  );
}