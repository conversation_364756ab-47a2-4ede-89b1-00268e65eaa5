"use client";

import { useEffect, useState } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface StockChartProps {
  data?: Array<{
    time: string;
    open: number;
    high: number;
    low: number;
    close: number;
    volume?: number;
  }>;
  width?: number;
  height?: number;
  symbol?: string;
}

// 生成示例数据
const generateSampleData = () => {
  const sampleData = [];
  const basePrice = 150;
  let price = basePrice;

  for (let i = 0; i < 30; i++) {
    const date = new Date();
    date.setDate(date.getDate() - (29 - i));

    const change = (Math.random() - 0.5) * 10;
    const close = price + change;

    sampleData.push({
      time: date.toLocaleDateString(),
      price: Math.round(close * 100) / 100,
      volume: Math.floor(Math.random() * 1000000),
    });

    price = close;
  }

  return sampleData;
};

export function StockChart({
  data = [],
  height = 400,
  symbol = "STOCK"
}: StockChartProps) {
  const [chartData, setChartData] = useState<Array<{time: string; price: number; volume: number}>>(() => {
    // 只在组件初始化时生成示例数据
    return generateSampleData();
  });

  useEffect(() => {
    if (data.length > 0) {
      const formattedData = data.map(item => ({
        time: new Date(item.time).toLocaleDateString(),
        price: item.close,
        volume: item.volume || 0,
      }));
      setChartData(formattedData);
    }
  }, [data]);

  return (
    <div className="bg-white rounded-lg shadow-lg p-4">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-800">
          📈 {symbol} 股价走势
        </h3>
        <p className="text-sm text-gray-600">
          近30日价格走势
        </p>
      </div>

      <div style={{ width: '100%', height: height }}>
        <ResponsiveContainer>
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="time" />
            <YAxis />
            <Tooltip
              formatter={(value: number) => [`$${value.toFixed(2)}`, '价格']}
              labelFormatter={(label) => `日期: ${label}`}
            />
            <Line
              type="monotone"
              dataKey="price"
              stroke="#2196F3"
              strokeWidth={2}
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      <div className="mt-4 text-xs text-gray-500 space-y-1">
        <p>💡 这是一个交互式股价图表</p>
        <p>📊 显示近期价格走势</p>
      </div>
    </div>
  );
}