/**
 * 研究分析页面相关类型定义
 */

export interface ContentData {
  [key: string]: any;
}

export interface CustomResearchEvent {
  subType?: string;
  type?: string; // 添加 type 字段用于处理 QUESTIONS_GENERATED 等事件
  taskId?: string;
  status?: string;
  message?: string;
  tasks?: any[];
  activeTask?: string;
  data?: any;
  section?: string;
  // 添加问题生成相关字段
  step_name?: string;
  questions?: Array<{
    id: string;
    title: string;
    content: string;
    type: string;
    status: string;
    category: string;
    icon: string;
  }>;
  total_count?: number;
  category?: string;
  // 添加宏观分析相关字段
  macro_analysis_results?: string[];
  total_results?: number;
}

// 在 ResearchAnalysisState 接口中添加节点映射
export interface ResearchAnalysisState {
  tasks: any[];
  activeTask: string;
  contentData: ContentData;
  messages: string[];
  currentMessage: string;
  currentStep: string;
  currentTaskContent: { [taskId: string]: string };
  currentTypingTaskId: string;
  // 新增：节点到任务的映射
  nodeTaskMapping: { [nodeName: string]: string };
  
  step_name?: string;
  questions?: Array<{
    id: string;
    title: string;
    content: string;
    type: string;
    status: string;
    category: string;
    icon: string;
  }>;
  total_count?: number;
  category?: string;
}

export type ResearchEventType = 
  | 'RESEARCH_TASKS_INIT'
  | 'TASK_STATUS_UPDATE'
  | 'CONTENT_AREA_UPDATE'
  | 'TASK_DATA_CHUNK'
  | 'RESEARCH_ANALYSIS_COMPLETE'
  | 'QUESTIONS_GENERATED'
  | 'MACRO_ANALYSIS_COMPLETE';