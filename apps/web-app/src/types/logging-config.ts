/**
 * YAI LogLayer 配置类型定义
 */

export interface YAILogLayerConfig {
  sls: {
    endpoint: string;
    accessKeyId: string;
    accessKeySecret: string;
    project: string;
    logstore: string;
    region: string;
  };
  app: {
    name: string;
    environment: 'development' | 'staging' | 'production';
    version: string;
  };
  features: {
    enableBatch: boolean;
    batchSize: number;
    flushInterval: number;
    enableRetry: boolean;
    maxRetries: number;
    enableConsole: boolean; // 是否启用控制台输出
    enableSls: boolean; // 是否启用 SLS 输出
  };
}

export interface LogContext {
  traceId?: string;
  requestId?: string;
  userId?: string;
  component?: string;
  action?: string;
  sessionId?: string;
  [key: string]: any;
}