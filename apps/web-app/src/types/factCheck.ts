/**
 * 事实核查功能相关类型定义
 */

export interface Claim {
  id: string;
  text: string;
  type: 'financial_data' | 'corporate_statement' | 'market_prediction' | 'news_fact' | 'general';
  status: 'pending' | 'in_progress' | 'verified' | 'contradicted' | 'conflicted' | 'unverified' | 'error';
  confidence: number;
  sources?: Array<{
    name: string;
    url?: string;
    reliability_score: number;
  }>;
  verification_result?: any;
}

export interface AgentStatus {
  id: string;
  role: 'orchestrator' | 'claim_extractor' | 'financial_verifier' | 'corporate_verifier' | 'news_verifier' | 'debate_moderator' | 'synthesis_agent';
  status: 'idle' | 'working' | 'completed' | 'error';
  progress: number;
  current_task?: string;
  last_update: number;
}

export interface DebateMessage {
  agent_role: string;
  message: string;
  evidence?: string[];
  timestamp: number;
  support_level?: number;
}

export interface DebateRecord {
  id: string;
  claim_id: string;
  topic: string;
  participants: string[];
  messages: DebateMessage[];
  conclusion?: string;
  confidence?: number;
  status: 'active' | 'concluded';
}

export interface FactCheckTask {
  id: string;
  original_text: string;
  claims: Claim[];
  agents: AgentStatus[];
  debates: DebateRecord[];
  status: 'pending' | 'processing' | 'completed' | 'error';
  total_cost: number;
  start_time: number;
  end_time?: number;
  final_report?: any;
  error_message?: string;
}

export interface FactCheckOptions {
  enableFinancialVerification: boolean;
  enableCorporateVerification: boolean;
  enableNewsVerification: boolean;
  deepResearchMode: boolean;
}

export interface FactCheckState {
  current_task: FactCheckTask | null;
  is_processing: boolean;
  connection_status: 'connecting' | 'connected' | 'error';
  daily_cost: number;
  input_text: string;
  options: FactCheckOptions;
  selected_claim_id: string | null;
  active_debate_id: string | null;
  show_debug_info: boolean;
}