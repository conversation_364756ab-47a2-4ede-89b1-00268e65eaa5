/**
 * YAI Investor Insight 服务端日志系统
 * 基于 @yai-loglayer/server v0.7.9
 */

import { createServerLogger } from '@yai-loglayer/server';
import type { YAILogLayerConfig } from '../types/logging-config';

// 服务端日志配置 (复用环境变量)
const serverLogConfig: YAILogLayerConfig = {
  app: {
    name: process.env.NEXT_PUBLIC_SERVICE_NAME || 'yai-investor-insight-web-api',
    environment: (process.env.NODE_ENV as any) || 'development',
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0'
  },
  sls: {
    endpoint: process.env.NEXT_PUBLIC_SLS_ENDPOINT!,
    accessKeyId: process.env.NEXT_PUBLIC_SLS_ACCESS_KEY_ID!,
    accessKeySecret: process.env.NEXT_PUBLIC_SLS_ACCESS_KEY_SECRET!,
    project: process.env.NEXT_PUBLIC_SLS_PROJECT!,
    logstore: process.env.NEXT_PUBLIC_SLS_LOGSTORE!,
    region: process.env.NEXT_PUBLIC_SLS_REGION || 'cn-beijing'
  },
  features: {
    enableBatch: true,
    batchSize: 20, // 服务端可以更频繁发送
    flushInterval: 1000, // 1秒刷新间隔
    enableRetry: true,
    maxRetries: 3,
    enableConsole: process.env.NODE_ENV === 'development',
    enableSls: process.env.NEXT_PUBLIC_SLS_ENABLED === 'true'
  }
};

// 创建服务端日志器实例
let serverLoggerInstance: any = null;

const initServerLogger = async () => {
  if (!serverLoggerInstance) {
    const outputs: any[] = [];
    
    // 调试信息
    console.log('🔧 初始化服务端日志器配置:', {
      enableConsole: serverLogConfig.features.enableConsole,
      enableSls: serverLogConfig.features.enableSls,
      slsEndpoint: serverLogConfig.sls.endpoint,
      slsProject: serverLogConfig.sls.project,
      slsLogstore: serverLogConfig.sls.logstore
    });
    
    // 开发环境启用控制台输出
    if (serverLogConfig.features.enableConsole) {
      outputs.push({ type: 'console' });
      console.log('✅ 添加服务端控制台输出');
    }
    
    // 启用 SLS 输出
    if (serverLogConfig.features.enableSls && serverLogConfig.sls.endpoint) {
      const slsConfig = {
        type: 'sls',
        config: {
          ...serverLogConfig.sls,
          topic: process.env.NEXT_PUBLIC_SLS_TOPIC || 'api-server'
        },
        batch: serverLogConfig.features.enableBatch,
        batchSize: serverLogConfig.features.batchSize,
        retry: serverLogConfig.features.enableRetry,
        maxRetries: serverLogConfig.features.maxRetries
      };
      outputs.push(slsConfig);
      console.log('✅ 添加服务端 SLS 输出:', slsConfig);
    } else {
      console.log('❌ 服务端 SLS 输出未启用:', {
        enableSls: serverLogConfig.features.enableSls,
        hasEndpoint: !!serverLogConfig.sls.endpoint
      });
    }

    if (outputs.length === 0) {
      // 如果没有配置任何输出，至少保证控制台输出
      outputs.push({ type: 'console' });
      console.log('⚠️ 使用默认服务端控制台输出');
    }

    console.log('🚀 创建服务端日志器，输出配置:', outputs);
    
    // 使用简化的配置，符合 createServerLogger 的接口
    serverLoggerInstance = await createServerLogger(serverLogConfig.app.name, {
      level: 'debug',
      outputs
    });
    console.log('✅ 服务端日志器初始化完成');
  }
  return serverLoggerInstance;
};

// 服务端日志器 API
export const serverLogger = {
  debug: async (message: string, data?: any) => {
    const log = await initServerLogger();
    log.debug(message, { 
      data, 
      service: serverLogConfig.app.name,
      serviceName: serverLogConfig.app.name,
      timestamp: new Date().toISOString(),
      environment: 'server'
    });
  },
  
  info: async (message: string, data?: any) => {
    const log = await initServerLogger();
    log.info(message, { 
      data, 
      service: serverLogConfig.app.name,
      serviceName: serverLogConfig.app.name,
      timestamp: new Date().toISOString(),
      environment: 'server'
    });
  },
  
  warn: async (message: string, data?: any) => {
    const log = await initServerLogger();
    log.warn(message, { 
      data, 
      service: serverLogConfig.app.name,
      serviceName: serverLogConfig.app.name,
      timestamp: new Date().toISOString(),
      environment: 'server'
    });
  },
  
  error: async (message: string, data?: any) => {
    const log = await initServerLogger();
    log.error(message, { 
      data, 
      service: serverLogConfig.app.name,
      serviceName: serverLogConfig.app.name,
      timestamp: new Date().toISOString(),
      environment: 'server'
    });
  }
};

// 服务端专用工具函数
export async function logApiRouteRequest(method: string, path: string, traceId: string, data?: any) {
  const log = await initServerLogger();
  log.info('API Route 请求', { 
    method, path, traceId, data,
    type: 'api_route_request',
    service: serverLogConfig.app.name,
    serviceName: serverLogConfig.app.name,
    environment: 'server'
  });
}

export async function logApiRouteResponse(method: string, path: string, traceId: string, status: number, duration: number, error?: any) {
  const log = await initServerLogger();
  const logData = { 
    method, path, traceId, status, duration,
    type: 'api_route_response',
    service: serverLogConfig.app.name,
    serviceName: serverLogConfig.app.name,
    environment: 'server'
  };
  
  if (error) {
    log.error('API Route 失败', { ...logData, error: error.message, stack: error.stack });
  } else if (status >= 400) {
    log.warn('API Route 错误', logData);
  } else {
    log.info('API Route 成功', logData);
  }
}

// 重新导出 generateTraceId 供服务端使用
export function generateTraceId(): string {
  return `trace_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}