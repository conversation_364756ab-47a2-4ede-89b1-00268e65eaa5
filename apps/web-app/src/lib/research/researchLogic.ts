/**
 * 研究分析页面业务逻辑
 */
import { useCallback, useRef } from 'react';
import { AGUIEvent } from '@/hooks/useAGUI';
import { ContextLogger, LogContext, generateTraceId, logUserAction, logComponentMount, logComponentUnmount } from '@/lib/logger';
import { CustomResearchEvent, ResearchAnalysisState } from '@/types/research';

// 添加防抖函数
function debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
  let timeoutId: NodeJS.Timeout;
  return ((...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  }) as T;
}

interface UseResearchLogicProps {
  state: ResearchAnalysisState;
  setState: (updater: (prev: ResearchAnalysisState) => ResearchAnalysisState) => void;
}

export function useResearchLogic({ state, setState }: UseResearchLogicProps) {
  // Initialize logger with proper context
  const logger = useRef(new ContextLogger({
    component: 'ResearchLogic',
    page: '/research',
    traceId: generateTraceId()
  }));
  
  // 添加内容缓存引用
  const taskContentBuffer = useRef<{[key: string]: string}>({});
  const updateCounter = useRef<{[key: string]: number}>({});
  
  // Helper function to update logger context if needed
  const updateLoggerContext = (newContext: LogContext) => {
    logger.current = new ContextLogger({
      ...logger.current.context,
      ...newContext
    });
  };

  // 防抖更新任务内容的函数
  const updateTaskContentDebounced = useCallback(
    debounce((taskId: string, content: string, updateCount: number) => {
      // console.log(`🔄 [防抖更新] 任务 ${taskId} 第 ${updateCount} 次更新，内容长度: ${content.length}`);
      // console.log(`📝 [防抖更新] 内容预览: "${content.substring(0, 100)}${content.length > 100 ? '...' : ''}"`);
      
      setState(prev => {
        const newState = {
          ...prev,
          currentTaskContent: {
            ...prev.currentTaskContent,
            [taskId]: content
          }
        };
        
        // console.log(`✅ [防抖更新] 状态已更新，任务 ${taskId} 当前内容长度: ${newState.currentTaskContent[taskId]?.length || 0}`);
        // console.log(`📊 [防抖更新] 当前所有任务内容:`, Object.keys(newState.currentTaskContent).map(key => `${key}: ${newState.currentTaskContent[key]?.length || 0} 字符`));
        
        return newState;
      });
    }, 100), // 100ms 防抖
    [setState]
  );

  // 更新任务状态的辅助函数
  const updateTaskStatus = useCallback((taskId: string, status: string) => {
    setState(prev => ({
      ...prev,
      tasks: prev.tasks.map(task => 
        task.id === taskId 
          ? { ...task, status: status as any }
          : task
      )
    }));
  }, [setState]);

  // 从 messageId 中提取节点名称
  const extractNodeNameFromMessageId = useCallback((messageId: string): string | null => {
  // 定义已知的节点名称列表，按长度降序排列以确保最长匹配优先
  const knownNodeNames = [
  'generate_quantitative_questions',
  'generate_prediction_questions', 
  'quantitative_analysis_loop',
  'qualitative_analysis_loop', 
  'market_sentiment_analysis',
  'financial_data_analysis',
  'macro_analysis_loop',
  'industry_analysis_loop',
  'competitor_analysis',
  'news_analysis'
  ];
  
  // 使用 includes 判断，找到第一个匹配的节点名称
  for (const nodeName of knownNodeNames) {
  if (messageId.includes(nodeName)) {
  return nodeName;
  }
  }
  
  // 如果没有匹配到已知节点，直接返回 null
  return null;
  }, []);

  // 获取节点的显示名称
  const getNodeDisplayName = useCallback((nodeName: string): string => {
  const nodeDisplayNames: Record<string, string> = {
  'generate_fact_questions': '🔍 事实核查问题生成',
  'generate_quantitative_questions': '📊 量化问题生成', 
  'generate_prediction_questions': '🔮 预测问题生成',
  'quantitative_analysis_loop': '📈 量化分析',
  'qualitative_analysis_loop': '📝 定性分析',
  'market_sentiment_analysis': '💭 市场情绪分析',
  'financial_data_analysis': '💰 财务数据分析',
  'macro_analysis_loop': '🌍 宏观分析',
  'industry_analysis_loop': '🏭 行业分析'
  };
  
  return nodeDisplayNames[nodeName] || '';
  }, []);

  // 为节点获取或创建对应的任务
  const getOrCreateTaskForNode = useCallback((messageId: string): string => {
  // 从 messageId 中提取节点名称
  const nodeName = extractNodeNameFromMessageId(messageId);
  
  if (!nodeName) {
  console.warn(`无法从 messageId "${messageId}" 中提取节点名称`);
  return '';
  }
  
  let taskId = nodeName;
  const displayName = getNodeDisplayName(nodeName);
  if (displayName === '') {
  // 如果 getNodeDisplayName 返回空字符串，不创建任务
  console.warn(`未知的节点名称: ${nodeName}`);
  return '';
  }
  
  setState(prev => {
  // 检查是否已存在该任务
  const existingTask = prev.tasks.find(task => task.id === taskId);
  if (existingTask) {
  return prev; // 任务已存在，不需要创建
  }
  
  // 创建新任务
  const newTask = {
  id: taskId,
  title: displayName,
  description: `${displayName}任务`,
  status: 'in_progress' as const,
  order: prev.tasks.length + 1
  };
  
  return {
  ...prev,
  tasks: [...prev.tasks, newTask],
  currentTaskContent: {
  ...prev.currentTaskContent,
  [taskId]: ''
  }
  };
  });
  
  return taskId;
  }, [setState, state.tasks.length, extractNodeNameFromMessageId, getNodeDisplayName]);

  // 创建新任务的辅助函数
  const createTaskFromLLMCall = useCallback((content: string, messageId?: string) => {
  // 如果提供了 messageId，尝试从中提取节点名称
  if (messageId) {
  const taskId = getOrCreateTaskForNode(messageId);
  if (taskId) {
  return taskId;
  }
  }
  
  // 如果没有 messageId 或无法提取节点名称，使用通用任务名称
  const taskId = 'llm_analysis_task';
  const newTask = {
  id: taskId,
  title: `大模型分析任务 ${new Date().toLocaleTimeString()}`,
  description: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
  status: 'in_progress' as const,
  order: state.tasks.length + 1
  };
  
  setState(prev => {
  // 检查是否已存在该任务
  const existingTask = prev.tasks.find(task => task.id === taskId);
  if (existingTask) {
  return {
  ...prev,
  activeTask: taskId,
  currentTypingTaskId: taskId
  };
  }
  
  return {
  ...prev,
  tasks: [...prev.tasks, newTask],
  activeTask: taskId,
  currentTypingTaskId: taskId,
  currentTaskContent: {
  ...prev.currentTaskContent,
  [taskId]: ''
  }
  };
  });
  
  return taskId;
  }, [setState, state.tasks.length, getOrCreateTaskForNode]);

  // 处理自定义研究事件
  const handleCustomResearchEvent = useCallback((eventData: CustomResearchEvent) => {
    switch (eventData.subType) {
      case 'RESEARCH_TASKS_INIT':
        if (eventData.tasks && Array.isArray(eventData.tasks)) {
          setState(prev => ({
            ...prev,
            tasks: eventData.tasks!,
            messages: [...prev.messages, `📋 初始化 ${eventData.tasks!.length} 个研究任务`]
          }));
        }
        break;

      case 'TASK_STATUS_UPDATE':
        if (eventData.taskId && eventData.status) {
          updateTaskStatus(eventData.taskId, eventData.status);
          setState(prev => ({
            ...prev,
            messages: [...prev.messages, eventData.message || `📝 任务 ${eventData.taskId} 状态: ${eventData.status}`],
            currentStep: eventData.status === 'in_progress' ? (eventData.taskId || '') : 
                        eventData.status === 'completed' ? '' : prev.currentStep
          }));
        }
        break;

      case 'CONTENT_AREA_UPDATE':
        if (eventData.activeTask) {
          setState(prev => ({
            ...prev,
            activeTask: eventData.activeTask!,
            contentData: eventData.data ? {
              ...prev.contentData,
              [eventData.activeTask!]: eventData.data
            } : prev.contentData
          }));
        }
        break;

      case 'TASK_DATA_CHUNK':
        if (eventData.taskId && eventData.section && eventData.data) {
          setState(prev => ({
            ...prev,
            contentData: {
              ...prev.contentData,
              [eventData.taskId!]: {
                ...(prev.contentData[eventData.taskId!] || {}),
                [eventData.section!]: eventData.data
              }
            }
          }));
          // console.log('📊 接收任务数据块:', eventData.taskId, eventData.section);
        }
        break;

      case 'RESEARCH_ANALYSIS_COMPLETE':
        setState(prev => ({
          ...prev,
          messages: [...prev.messages, eventData.message || '🎉 所有研究分析任务已完成']
        }));
        break;
    }
  }, [setState, updateTaskStatus]);

  // AG-UI 事件处理
  const handleAGUIEvent = useCallback((event: AGUIEvent) => {
    // console.log('🔍 [调试日志] 收到 AG-UI 事件:', event.type, event);
    logger.current.debug('收到 AG-UI 事件', {
      eventType: event.type,
      event: JSON.stringify(event)
    });
  
    switch (event.type) {
      case 'RUN_STARTED':
        // console.log('🚀 [调试日志] AG-UI 运行开始事件触发');
        logger.current.info('AG-UI 运行开始', { eventType: 'RUN_STARTED' });
        setState(prev => ({
          ...prev,
          messages: [...prev.messages, '🚀 AG-UI 运行开始 - 启动投资研究分析...']
        }));
        break;
  
      case 'RUN_FINISHED':
        // console.log('✅ [调试日志] AG-UI 运行结束事件触发');
        logger.current.info('AG-UI 运行结束', { eventType: 'RUN_FINISHED' });
        
        // 完成当前正在进行的任务
        setState(prev => {
          const updatedTasks = prev.tasks.map(task => 
            task.id === prev.currentTypingTaskId 
              ? { ...task, status: 'completed' as const }
              : task
          );
          
          return {
            ...prev,
            tasks: updatedTasks,
            messages: [...prev.messages, '✅ AG-UI 运行完成 - 投资研究分析结束'],
            currentMessage: ''
          };
        });
        break;
  
      case 'TEXT_MESSAGE_START':
        // console.log('📝 [调试日志] 文本消息开始事件');
        logger.current.debug('文本消息开始', {
          eventType: 'TEXT_MESSAGE_START',
          message: event.value?.message
        });
        break;
        
      // 在 handleAGUIEvent 函数中修改 TEXT_MESSAGE_CHUNK 处理逻辑
      case 'TEXT_MESSAGE_CHUNK':
        // console.log('📝 [TEXT_MESSAGE_CHUNK] Full event object:', {
        //   type: event.type,
        //   messageId: event.messageId,
        //   delta: event.delta,
        //   content: event.content
        // });
        
        const textContent = event.delta || '';
        const messageId = event.messageId;
        
        if (textContent && messageId) {
          // console.log(`📝 [TEXT_MESSAGE_CHUNK] 处理文本块: messageId="${messageId}", 内容长度=${textContent.length}`);
          
          // 从 messageId 中提取节点名称
          const nodeName = extractNodeNameFromMessageId(messageId);
          // console.log(`🔍 [TEXT_MESSAGE_CHUNK] 提取的节点名称: "${nodeName}"`);
          
          if (nodeName) {
            // 根据节点名称路由到对应的任务
            const targetTaskId = getOrCreateTaskForNode(nodeName);
            // console.log(`🎯 [TEXT_MESSAGE_CHUNK] 目标任务ID: "${targetTaskId}"`);
            
            // 更新缓存
            if (!taskContentBuffer.current[targetTaskId]) {
              taskContentBuffer.current[targetTaskId] = '';
              updateCounter.current[targetTaskId] = 0;
              // console.log(`🆕 [TEXT_MESSAGE_CHUNK] 初始化任务 ${targetTaskId} 的缓存`);
            }
            
            const oldContent = taskContentBuffer.current[targetTaskId];
            taskContentBuffer.current[targetTaskId] += textContent;
            updateCounter.current[targetTaskId]++;
            
            const newContent = taskContentBuffer.current[targetTaskId];
            const updateCount = updateCounter.current[targetTaskId];
            
            // console.log(`📈 [TEXT_MESSAGE_CHUNK] 任务 ${targetTaskId} 内容更新:`);
            // console.log(`   - 更新次数: ${updateCount}`);
            // console.log(`   - 旧内容长度: ${oldContent.length}`);
            // console.log(`   - 新增内容: "${textContent}"`);
            // console.log(`   - 新内容长度: ${newContent.length}`);
            // console.log(`   - 当前缓存内容预览: "${newContent.substring(0, 100)}${newContent.length > 100 ? '...' : ''}"`);
            
            // 检查当前状态中的内容
            const currentStateContent = state.currentTaskContent[targetTaskId] || '';
            // console.log(`📊 [TEXT_MESSAGE_CHUNK] 当前状态中任务 ${targetTaskId} 的内容长度: ${currentStateContent.length}`);
            
            // 使用防抖更新
            updateTaskContentDebounced(targetTaskId, newContent, updateCount);
            
            // console.log(`📝 [TEXT_MESSAGE_CHUNK] 成功路由到节点 ${nodeName} 的任务 ${targetTaskId}`);
          } else {
            // console.log('⚠️ [TEXT_MESSAGE_CHUNK] 无法提取节点名称，使用默认路由');
            if (state.currentTypingTaskId) {
              // console.log(`🎯 [TEXT_MESSAGE_CHUNK] 使用当前打字任务ID: ${state.currentTypingTaskId}`);
              
              // 更新缓存
              if (!taskContentBuffer.current[state.currentTypingTaskId]) {
                taskContentBuffer.current[state.currentTypingTaskId] = '';
                updateCounter.current[state.currentTypingTaskId] = 0;
              }
              
              taskContentBuffer.current[state.currentTypingTaskId] += textContent;
              updateCounter.current[state.currentTypingTaskId]++;
              
              updateTaskContentDebounced(
                state.currentTypingTaskId, 
                taskContentBuffer.current[state.currentTypingTaskId],
                updateCounter.current[state.currentTypingTaskId]
              );
            } else {
              // console.log('❌ [TEXT_MESSAGE_CHUNK] 没有可用的任务ID，跳过内容更新');
            }
          }
        } else {
          // console.log('⚠️ [TEXT_MESSAGE_CHUNK] 跳过更新 - 缺少文本内容或messageId');
          // console.log(`   - textContent: "${textContent}"`);
          // console.log(`   - messageId: "${messageId}"`);
        }
        break;
        
      // 在 CUSTOM 事件处理中修改所有任务创建逻辑
      case 'CUSTOM':
        console.log('🔧 [调试日志] 收到自定义事件:', event.value);
        
        // 修改 QUESTIONS_GENERATED 事件处理
        if (event.value?.type === 'QUESTIONS_GENERATED') {
          // console.log('📝 [调试日志] 收到问题生成事件:', event.value);
          
          // 使用固定的任务 ID，基于事件类型和类别
          const taskId = `questions_${event.value.category || 'general'}`;
          
          // 检查是否已存在相同的任务
          const existingTask = state.tasks.find(task => task.id === taskId);
          if (!existingTask) {
            const newTask = {
              id: taskId,
              title: event.value.step_name || '问题生成',
              description: `生成了 ${event.value.total_count || 0} 个${event.value.category || ''}`,
              status: 'completed' as const
            };
            
            setState(prev => ({
              ...prev,
              tasks: [...prev.tasks, newTask],
              contentData: {
                ...prev.contentData,
                [taskId]: {
                  questions: event.value.questions || [],
                  category: event.value.category || '',
                  total_count: event.value.total_count || 0,
                  step_name: event.value.step_name || ''
                }
              },
              messages: [...prev.messages, `📝 ${event.value.step_name || '问题生成'}完成，生成了 ${event.value.total_count || 0} 个问题`]
            }));
          }
        }
        // 修改 MACRO_ANALYSIS_COMPLETE 事件处理
        else if (event.value?.type === 'MACRO_ANALYSIS_COMPLETE') {
          // console.log('📊 [调试日志] 收到宏观分析完成事件:', event.value);
          
          // 使用固定的任务 ID
          const taskId = 'macro_analysis_results';
          
          // 检查是否已存在相同的任务
          const existingTask = state.tasks.find(task => task.id === taskId);
          if (!existingTask) {
            const newTask = {
              id: taskId,
              title: `宏观分析结果 (${event.value.total_results}个)`,
              description: event.value.step_name,
              status: 'completed' as const
            };
            
            setState(prev => ({
              ...prev,
              tasks: [...prev.tasks, newTask],
              activeTask: taskId,
              contentData: {
                ...prev.contentData,
                [taskId]: {
                  macro_analysis_results: event.value.macro_analysis_results,
                  step_name: event.value.step_name,
                  total_results: event.value.total_results
                }
              },
              messages: [...prev.messages, `📊 完成宏观分析，生成了 ${event.value.total_results} 个分析结果`]
            }));
          }
        }
        // 处理 STEP_STARTED 和 STEP_FINISHED 事件
        else if (event.type === 'STEP_STARTED') {
          console.log('🚀 [调试日志] 步骤开始:', event.value);
          
          // 🔍 添加详细的 messageId 调试
          const messageId = event.messageId || event.value?.messageId || '';
          console.log('🔍 [STEP_STARTED] messageId 详情:', {
            eventMessageId: event.messageId,
            valueMessageId: event.value?.messageId,
            finalMessageId: messageId,
            eventKeys: Object.keys(event),
            valueKeys: event.value ? Object.keys(event.value) : []
          });
          
          // 修改：使用 extractNodeNameFromMessageId 函数来提取节点名称
          const nodeName = extractNodeNameFromMessageId(messageId);
          
          console.log('🔍 [STEP_STARTED] 节点名称提取结果:', {
            messageId,
            extractedNodeName: nodeName,
            extractionSuccess: !!nodeName
          });
          
          if (nodeName) {
            // 使用 getOrCreateTaskForNode 函数来获取或创建任务
            const taskId = getOrCreateTaskForNode(nodeName);
            
            console.log('✅ [STEP_STARTED] 设置 currentTypingTaskId:', {
              nodeName,
              taskId,
              stepName: event.value?.step_name
            });
            
            setState(prev => ({
              ...prev,
              activeTask: taskId,
              currentTypingTaskId: taskId,
              currentStep: event.value?.step_name || nodeName,
              messages: [...prev.messages, `🚀 开始: ${event.value?.step_name || nodeName}`]
            }));
          } else {
            console.log('⚠️ [STEP_STARTED] 无法提取节点名称，使用备用方案');
            
            // 如果无法提取节点名称，使用步骤名称作为后备方案
            const stepName = event.value?.step_name || 'unknown_step';
            const taskId = stepName;
            
            console.log('🔄 [STEP_STARTED] 备用方案:', {
              stepName,
              taskId,
              messageId
            });
            
            const existingTask = state.tasks.find(task => task.id === taskId);
            if (!existingTask) {
              const newTask = {
                id: taskId,
                title: event.value?.step_name || '未知步骤',
                description: event.value?.description || '',
                status: 'in_progress' as const
              };
              
              setState(prev => ({
                ...prev,
                tasks: [...prev.tasks, newTask],
                activeTask: taskId,
                currentTypingTaskId: taskId,
                currentStep: event.value?.step_name || '',
                messages: [...prev.messages, `🚀 开始: ${event.value?.step_name || '新步骤'}`]
              }));
            } else {
              // 如果任务已存在，只更新 currentTypingTaskId
              setState(prev => ({
                ...prev,
                activeTask: taskId,
                currentTypingTaskId: taskId,
                currentStep: event.value?.step_name || '',
                messages: [...prev.messages, `🚀 继续: ${event.value?.step_name || '步骤'}`]
              }));
            }
          }
        }
        else if (event.type === 'STEP_FINISHED') {
          console.log('✅ [调试日志] 步骤完成:', event.value);
          
          setState(prev => {
            const updatedTasks = prev.tasks.map(task => 
              task.id === prev.currentTypingTaskId 
                ? { ...task, status: 'completed' as const }
                : task
            );
            
            return {
              ...prev,
              tasks: updatedTasks,
              // 不立即清空，让用户能看到完整的打字机效果
              // currentTypingTaskId: '',
              messages: [...prev.messages, `✅ 完成: ${event.value?.step_name || '步骤'}`]
            };
          });
        }
        else if (event.value?.subType) {
          // 保留原有的自定义事件处理逻辑
          handleCustomResearchEvent(event.value);
        }
        break;
  
      default:
        console.log('❓ [调试日志] 未知事件类型:', event.type, event);
        if (event.rawEvent) {
          console.log('📄 [调试日志] 原始事件数据:', event.rawEvent);
          try {
            const rawData = typeof event.rawEvent === 'string' ? JSON.parse(event.rawEvent) : event.rawEvent;
            if (rawData.type) {
              console.log('🔄 [调试日志] 递归处理原始事件');
              handleAGUIEvent(rawData);
            }
          } catch (e) {
            console.error('❌ [调试日志] 原始事件解析失败:', e);
          }
        }
    }
  }, [state.currentMessage, state.currentTypingTaskId, setState, handleCustomResearchEvent, createTaskFromLLMCall, extractNodeNameFromMessageId, getOrCreateTaskForNode, updateTaskContentDebounced]);

  // 启动分析 - 修改为接受研究主题参数
  const startAnalysis = useCallback(async (
    sendMessage: (message: string) => Promise<void>, 
    isRunning: boolean,
    researchTopic?: string  // 新增参数
  ) => {
    console.log('🎬 [调试日志] startAnalysis 函数被调用，isRunning:', isRunning);
    
    if (isRunning) {
      console.log('⚠️ [调试日志] 分析已在运行中，忽略重复请求');
      logger.current.warn('分析已在运行中，忽略重复请求');
      return;
    }
  
    const topic = researchTopic || '特斯拉降价影响分析';  // 使用传入的主题或默认值
    
    console.log('📊 [调试日志] 记录用户操作：开始研究分析');
    logUserAction('start_research_analysis', {
      topic: topic  // 使用实际的研究主题
    });
  
    console.log('🔄 [调试日志] 重置状态，准备开始新的分析');
    setState(prev => {
      console.log('📝 [调试日志] 状态重置完成，设置 currentStep 为 analysis_started');
      return {
        ...prev,
        tasks: [],
        contentData: {},
        messages: [`🚀 使用标准 AG-UI SDK 启动投资研究分析: ${topic}`],  // 显示实际主题
        activeTask: '',
        currentMessage: '',
        currentStep: 'analysis_started',
        currentTaskContent: {},
        currentTypingTaskId: ''
      };
    });
  
    try {
      console.log('📤 [调试日志] 准备发送研究分析请求到后端');
      logger.current.info('发送研究分析请求', {
        action: 'start_analysis',
        topic: topic  // 记录实际主题
      });
      
      // 发送用户输入的研究主题到后端
      await sendMessage(`开始投资研究分析: ${topic}`);
      console.log('✅ [调试日志] 研究分析请求发送成功');
    } catch (error) {
      console.error('❌ [调试日志] 启动研究分析失败:', error);
      logger.current.error('启动研究分析失败', { error });
      setState(prev => ({
        ...prev,
        messages: [...prev.messages, `❌ 启动分析失败: ${(error as Error).message}`]
      }));
    }
  }, [setState]);

  // 组件生命周期钩子
  const useLifecycleLogging = () => {
    logComponentMount('ResearchAnalysisPage');
    return () => logComponentUnmount('ResearchAnalysisPage');
  };

  return {
    handleAGUIEvent,
    startAnalysis,
    updateTaskStatus,
    useLifecycleLogging,
    logger: logger.current,
    extractNodeNameFromMessageId,
    getOrCreateTaskForNode,
    getNodeDisplayName
  };
}
