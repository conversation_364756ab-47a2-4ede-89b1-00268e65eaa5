/**
 * 事实核查工具函数
 */

export interface FactCheckStartOptions {
  enableFinancialVerification?: boolean;
  enableCorporateVerification?: boolean;
  enableNewsVerification?: boolean;
  deepResearchMode?: boolean;
}

export function createFactCheckMessage(
  text: string,
  options: FactCheckStartOptions = {}
): string {
  const taskId = `fact_check_${Date.now()}`;
  
  return JSON.stringify({
    action: 'fact_check',
    taskId,
    text,
    options: {
      enableFinancialVerification: options.enableFinancialVerification ?? true,
      enableCorporateVerification: options.enableCorporateVerification ?? true,
      enableNewsVerification: options.enableNewsVerification ?? true,
      deepResearchMode: options.deepResearchMode ?? false
    }
  });
}

export function createCancelMessage(taskId: string): string {
  return JSON.stringify({
    action: 'cancel_fact_check',
    taskId
  });
}

export function generateTaskId(): string {
  // 事实核查使用固定的节点名称
  return 'generate_fact_questions';
}

export function validateFactCheckOptions(options: FactCheckStartOptions): boolean {
  return typeof options === 'object' && options !== null;
}

export function formatFactCheckError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'Unknown error occurred';
}