import { createApiClient, createSSEClient } from '@yai-investor-insight/api-client';
import { generateTraceId, logApiRequest, logApiResponse, extractTraceIdFromResponse } from './logger';

// API 配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const SSE_URL = `${API_BASE_URL}/api/stream`;

// 增强的 fetch 函数，支持 trace_id 和日志记录
async function enhancedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  const traceId = generateTraceId();
  const startTime = Date.now();

  // 添加 trace_id 到请求头
  const headers = {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'X-Request-ID': traceId,
    'X-Trace-ID': traceId,
    ...options.headers,
  };

  // 记录请求开始
  logApiRequest(
    options.method || 'GET',
    url,
    traceId,
    options.body
  );

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    });

    const duration = Date.now() - startTime;
    const responseTraceId = extractTraceIdFromResponse(response) || traceId;

    // 记录响应
    logApiResponse(
      options.method || 'GET',
      url,
      responseTraceId,
      response.status,
      duration
    );

    return response;
  } catch (error) {
    const duration = Date.now() - startTime;

    // 记录错误
    logApiResponse(
      options.method || 'GET',
      url,
      traceId,
      0,
      duration,
      error
    );

    throw error;
  }
}

// 创建 API 客户端实例（使用增强的 fetch）
export const apiClient = createApiClient({
  baseUrl: API_BASE_URL,
  timeout: 30000,
  fetch: enhancedFetch,  // 使用我们的增强 fetch
});

// 创建 SSE 客户端实例
export const sseClient = createSSEClient(SSE_URL);

// API 端点常量
export const API_ENDPOINTS = {
  TASKS: '/api/tasks',
  UPLOAD: '/api/upload',
  STREAM: '/api/stream',
  HEALTH: '/api/health'
} as const;
