/**
 * YAI Investor Insight 日志系统
 * 基于 loglayer-support v0.7.9
 * 修复K8s环境中的兼容性问题
 */

import { createBrowserLogger } from '@yai-loglayer/browser';
import type { YAILogLayerConfig, LogContext } from '../types/logging-config';

// 重新导出 LogContext 以便其他文件使用
export type { LogContext };

// 安全的环境变量读取函数
const getEnvVar = (key: string, defaultValue: string = ''): string => {
  try {
    if (typeof window !== 'undefined') {
      // 浏览器环境 - 多种方式尝试获取环境变量
      return (window as any).__NEXT_DATA__?.env?.[key] ||
             (window as any).__ENV__?.[key] ||
             process.env[key] ||
             defaultValue;
    }
    // 服务端环境
    return process.env[key] || defaultValue;
  } catch (error) {
    console.warn(`获取环境变量 ${key} 失败:`, error);
    return defaultValue;
  }
};

// 检查是否在K8s环境中
const isK8sEnvironment = (): boolean => {
  return !!(
    getEnvVar('KUBERNETES_SERVICE_HOST') ||
    getEnvVar('KUBERNETES_PORT') ||
    getEnvVar('HOSTNAME', '').includes('pod') ||
    getEnvVar('NODE_NAME')
  );
};

// 检查SLS配置是否完整
const checkSlsConfig = (): boolean => {
  const requiredVars = [
    'NEXT_PUBLIC_SLS_ENDPOINT',
    'NEXT_PUBLIC_SLS_ACCESS_KEY_ID',
    'NEXT_PUBLIC_SLS_ACCESS_KEY_SECRET',
    'NEXT_PUBLIC_SLS_PROJECT',
    'NEXT_PUBLIC_SLS_LOGSTORE'
  ];

  const missingVars = requiredVars.filter(key => {
    const value = getEnvVar(key);
    return !value || value.trim() === '';
  });

  if (missingVars.length > 0) {
    console.warn('SLS配置不完整，缺少环境变量:', missingVars);
    return false;
  }

  return true;
};

// 日志配置 (使用安全的环境变量读取)
const createLogConfig = (): YAILogLayerConfig => {
  const isK8s = isK8sEnvironment();
  const nodeEnv = getEnvVar('NODE_ENV', 'development');
  const slsEnabled = getEnvVar('NEXT_PUBLIC_SLS_ENABLED') === 'true';
  const slsConfigComplete = checkSlsConfig();

  console.log('🔧 环境检测:', {
    isK8s,
    nodeEnv,
    slsEnabled,
    slsConfigComplete
  });

  return {
    app: {
      name: getEnvVar('NEXT_PUBLIC_SERVICE_NAME', 'yai-investor-insight-web'),
      environment: (nodeEnv as any),
      version: getEnvVar('NEXT_PUBLIC_APP_VERSION', '1.0.0')
    },
    sls: {
      endpoint: getEnvVar('NEXT_PUBLIC_SLS_ENDPOINT'),
      accessKeyId: getEnvVar('NEXT_PUBLIC_SLS_ACCESS_KEY_ID'),
      accessKeySecret: getEnvVar('NEXT_PUBLIC_SLS_ACCESS_KEY_SECRET'),
      project: getEnvVar('NEXT_PUBLIC_SLS_PROJECT'),
      logstore: getEnvVar('NEXT_PUBLIC_SLS_LOGSTORE'),
      region: getEnvVar('NEXT_PUBLIC_SLS_REGION', 'cn-beijing')
    },
    features: {
      enableBatch: true,
      batchSize: isK8s ? 20 : 10, // K8s环境中增加批量大小
      flushInterval: isK8s ? 10000 : 5000, // K8s环境中增加刷新间隔
      enableRetry: true,
      maxRetries: isK8s ? 1 : 2, // K8s环境中减少重试次数
      enableConsole: nodeEnv === 'development' || !slsConfigComplete, // 配置不完整时强制启用控制台
      enableSls: slsEnabled && slsConfigComplete
    }
  };
};

const logConfig = createLogConfig();

// 创建日志器实例
let browserLogger: any = null;
let initPromise: Promise<any> | null = null;

const initLogger = async () => {
  // 防止重复初始化
  if (initPromise) {
    return initPromise;
  }

  if (!browserLogger) {
    initPromise = (async () => {
      try {
        const outputs: any[] = [];
        const isK8s = isK8sEnvironment();

        // 调试信息
        console.log('🔧 初始化日志器配置:', {
          isK8s,
          enableConsole: logConfig.features.enableConsole,
          enableSls: logConfig.features.enableSls,
          slsEndpoint: logConfig.sls.endpoint,
          slsProject: logConfig.sls.project,
          slsLogstore: logConfig.sls.logstore,
          hasSlsConfig: checkSlsConfig()
        });

        // 开发环境或SLS配置不完整时启用控制台输出
        if (logConfig.features.enableConsole || !logConfig.features.enableSls) {
          outputs.push({ type: 'console' });
          console.log('✅ 添加控制台输出');
        }

        // 启用 SLS 输出 (仅在配置完整时)
        if (logConfig.features.enableSls && logConfig.sls.endpoint) {
          try {
            const slsConfig = {
              type: 'sls',
              config: {
                ...logConfig.sls,
                topic: getEnvVar('NEXT_PUBLIC_SLS_TOPIC', 'default')
              },
              batch: logConfig.features.enableBatch,
              batchSize: logConfig.features.batchSize,
              retry: logConfig.features.enableRetry,
              maxRetries: logConfig.features.maxRetries
            };
            outputs.push(slsConfig);
            console.log('✅ 添加 SLS 输出:', slsConfig);
          } catch (error) {
            console.warn('⚠️ SLS 配置失败，回退到控制台输出:', error);
            if (!outputs.some(output => output.type === 'console')) {
              outputs.push({ type: 'console' });
            }
          }
        } else {
          console.log('❌ SLS 输出未启用:', {
            enableSls: logConfig.features.enableSls,
            hasEndpoint: !!logConfig.sls.endpoint,
            hasCompleteConfig: checkSlsConfig()
          });
        }

        if (outputs.length === 0) {
          // 如果没有配置任何输出，至少保证控制台输出
          outputs.push({ type: 'console' });
          console.log('⚠️ 使用默认控制台输出');
        }

        console.log('🚀 创建日志器，输出配置:', outputs);

        // K8s环境中使用更保守的初始化策略
        if (isK8s) {
          // 在K8s环境中，先尝试只使用控制台输出
          try {
            browserLogger = await createBrowserLogger(logConfig.app.name, {
              browser: { outputs: [{ type: 'console' }] }
            } as any);
            console.log('✅ K8s环境日志器初始化完成 (仅控制台)');
          } catch (error) {
            console.warn('⚠️ K8s环境日志器初始化失败，使用fallback:', error);
            throw error;
          }
        } else {
          // 非K8s环境使用完整配置
          browserLogger = await createBrowserLogger(logConfig.app.name, {
            browser: { outputs }
          } as any);
          console.log('✅ 日志器初始化完成');
        }

        return browserLogger;
      } catch (error) {
        console.error('❌ 日志器初始化失败:', error);
        // 创建一个简单的fallback日志器
        browserLogger = {
          debug: (msg: string, data?: any) => console.debug(`[DEBUG] ${msg}`, data),
          info: (msg: string, data?: any) => console.info(`[INFO] ${msg}`, data),
          warn: (msg: string, data?: any) => console.warn(`[WARN] ${msg}`, data),
          error: (msg: string, data?: any) => console.error(`[ERROR] ${msg}`, data)
        };
        return browserLogger;
      }
    })();
  }

  return initPromise;
};

// 全局日志器
export const logger = {
  debug: async (message: string, data?: any) => {
    const log = await initLogger();
    log.debug(message, { 
      data, 
      service: logConfig.app.name,
      serviceName: logConfig.app.name,
      timestamp: new Date().toISOString()
    });
  },
  
  info: async (message: string, data?: any) => {
    const log = await initLogger();
    log.info(message, { 
      data, 
      service: logConfig.app.name,
      serviceName: logConfig.app.name,
      timestamp: new Date().toISOString()
    });
  },
  
  warn: async (message: string, data?: any) => {
    const log = await initLogger();
    log.warn(message, { 
      data, 
      service: logConfig.app.name,
      serviceName: logConfig.app.name,
      timestamp: new Date().toISOString()
    });
  },
  
  error: async (message: string, data?: any) => {
    const log = await initLogger();
    log.error(message, { 
      data, 
      service: logConfig.app.name,
      serviceName: logConfig.app.name,
      timestamp: new Date().toISOString()
    });
  }
};

// 上下文日志器
export class ContextLogger {
  public context: LogContext;

  constructor(context: LogContext = {}) {
    this.context = context;
  }

  updateContext(newContext: LogContext) {
    this.context = { ...this.context, ...newContext };
  }

  private enrichLog(data: any = {}) {
    return {
      ...this.context,
      ...data,
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'SSR',
      url: typeof window !== 'undefined' ? window.location.href : 'SSR'
    };
  }

  async debug(message: string, data?: any) {
    const log = await initLogger();
    log.debug(message, this.enrichLog(data));
  }

  async info(message: string, data?: any) {
    const log = await initLogger();
    log.info(message, this.enrichLog(data));
  }

  async warn(message: string, data?: any) {
    const log = await initLogger();
    log.warn(message, this.enrichLog(data));
  }

  async error(message: string, data?: any) {
    const log = await initLogger();
    log.error(message, this.enrichLog(data));
  }
}

// 工具函数
export function generateTraceId(): string {
  return `trace_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

export function extractTraceIdFromResponse(response: Response): string | null {
  return response.headers.get('X-Request-ID') || response.headers.get('X-Trace-ID');
}

export async function logApiRequest(method: string, url: string, traceId: string, data?: any) {
  const log = await initLogger();
  log.info('API 请求', { 
    method, url, traceId, data,
    type: 'api_request',
    service: logConfig.app.name,
    serviceName: logConfig.app.name
  });
}

export async function logApiResponse(method: string, url: string, traceId: string, status: number, duration: number, error?: any) {
  const log = await initLogger();
  const logData = { 
    method, url, traceId, status, duration,
    type: 'api_response',
    service: logConfig.app.name,
    serviceName: logConfig.app.name
  };
  
  if (error) {
    log.error('API 失败', { ...logData, error: error.message });
  } else if (status >= 400) {
    log.warn('API 错误', logData);
  } else {
    log.info('API 成功', logData);
  }
}

export async function logComponentMount(componentName: string) {
  const log = await initLogger();
  log.debug('组件挂载', { 
    component: componentName,
    service: logConfig.app.name,
    serviceName: logConfig.app.name
  });
}

export async function logComponentUnmount(componentName: string) {
  const log = await initLogger();
  log.debug('组件卸载', { 
    component: componentName,
    service: logConfig.app.name,
    serviceName: logConfig.app.name
  });
}

export async function logUserAction(action: string, data?: any) {
  const log = await initLogger();
  log.info('用户操作', { 
    action, data, 
    type: 'user_action',
    service: logConfig.app.name,
    serviceName: logConfig.app.name
  });
}