/**
 * 事实核查功能的业务操作
 */
import { 
  FactCheckTask, 
  FactCheckOptions, 
  <PERSON><PERSON><PERSON>, 
  AgentStatus, 
  DebateR<PERSON>ord, 
  DebateMessage,
  FactCheckState 
} from '@/types/factCheck';

export interface FactCheckActions {
  setInputText: (text: string) => void;
  setOptions: (options: Partial<FactCheckOptions>) => void;
  setCurrentTask: (task: FactCheckTask | null) => void;
  setProcessing: (processing: boolean) => void;
  setConnectionStatus: (status: 'connecting' | 'connected' | 'error') => void;
  updateDailyCost: (cost: number) => void;
  
  startFactCheck: (text: string, options: FactCheckOptions) => void;
  cancelFactCheck: () => void;
  
  handleTaskStarted: (task: FactCheckTask) => void;
  handleClaimExtracted: (claims: Claim[]) => void;
  handleAgentProgress: (agent_status: AgentStatus) => void;
  handleVerificationComplete: (claim: Claim) => void;
  handleDebateStarted: (debate: DebateRecord) => void;
  handleDebateMessage: (claim_id: string, message: DebateMessage) => void;
  handleDebateConcluded: (debate: DebateRecord) => void;
  handleTaskComplete: (task: FactCheckTask) => void;
  handleCostUpdate: (cost: number) => void;
  handleError: (error: string) => void;
  
  selectClaim: (claim_id: string | null) => void;
  setActiveDebate: (debate_id: string | null) => void;
  toggleDebugInfo: () => void;
  
  reset: () => void;
  getClaimById: (claim_id: string) => Claim | undefined;
  getAgentByRole: (role: string) => AgentStatus | undefined;
  getDebateById: (debate_id: string) => DebateRecord | undefined;
}

export const createFactCheckActions = (
  set: (fn: (state: FactCheckState) => FactCheckState) => void,
  get: () => FactCheckState
): FactCheckActions => ({
  setInputText: (text) => set((state) => ({ ...state, input_text: text })),
  
  setOptions: (new_options) => set((state) => ({ 
    ...state, 
    options: { ...state.options, ...new_options } 
  })),
  
  setCurrentTask: (task) => set((state) => ({ ...state, current_task: task })),
  setProcessing: (processing) => set((state) => ({ ...state, is_processing: processing })),
  setConnectionStatus: (status) => set((state) => ({ ...state, connection_status: status })),
  updateDailyCost: (cost) => set((state) => ({ ...state, daily_cost: cost })),
  
  startFactCheck: (text, options) => {
    set((state) => ({
      ...state,
      input_text: text,
      options,
      is_processing: true,
      current_task: null,
      selected_claim_id: null,
      active_debate_id: null
    }));
  },
  
  cancelFactCheck: () => {
    set((state) => ({
      ...state,
      is_processing: false,
      current_task: null
    }));
  },
  
  handleTaskStarted: (task) => {
    set((state) => ({
      ...state,
      current_task: task,
      is_processing: true
    }));
  },
  
  handleClaimExtracted: (claims) => {
    set((state) => ({
      ...state,
      current_task: state.current_task ? {
        ...state.current_task,
        claims
      } : null
    }));
  },
  
  handleAgentProgress: (agent_status) => {
    set((state) => {
      if (!state.current_task) return state;
      
      const updated_agents = state.current_task.agents.map(agent =>
        agent.role === agent_status.role ? agent_status : agent
      );
      
      if (!updated_agents.find(a => a.role === agent_status.role)) {
        updated_agents.push(agent_status);
      }
      
      return {
        ...state,
        current_task: {
          ...state.current_task,
          agents: updated_agents
        }
      };
    });
  },
  
  handleVerificationComplete: (claim) => {
    set((state) => {
      if (!state.current_task) return state;
      
      const updated_claims = state.current_task.claims.map(c =>
        c.id === claim.id ? claim : c
      );
      
      return {
        ...state,
        current_task: {
          ...state.current_task,
          claims: updated_claims
        }
      };
    });
  },
  
  handleDebateStarted: (debate) => {
    set((state) => {
      if (!state.current_task) return state;
      
      return {
        ...state,
        current_task: {
          ...state.current_task,
          debates: [...state.current_task.debates, debate]
        }
      };
    });
  },
  
  handleDebateMessage: (claim_id, message) => {
    set((state) => {
      if (!state.current_task) return state;
      
      const updated_debates = state.current_task.debates.map(debate => {
        if (debate.claim_id === claim_id) {
          return {
            ...debate,
            messages: [...debate.messages, message]
          };
        }
        return debate;
      });
      
      return {
        ...state,
        current_task: {
          ...state.current_task,
          debates: updated_debates
        }
      };
    });
  },
  
  handleDebateConcluded: (concluded_debate) => {
    set((state) => {
      if (!state.current_task) return state;
      
      const updated_debates = state.current_task.debates.map(debate =>
        debate.id === concluded_debate.id ? concluded_debate : debate
      );
      
      return {
        ...state,
        current_task: {
          ...state.current_task,
          debates: updated_debates
        }
      };
    });
  },
  
  handleTaskComplete: (task) => {
    set((state) => ({
      ...state,
      current_task: task,
      is_processing: false
    }));
  },
  
  handleCostUpdate: (cost) => {
    set((state) => ({
      ...state,
      daily_cost: cost,
      current_task: state.current_task ? {
        ...state.current_task,
        total_cost: cost
      } : null
    }));
  },
  
  handleError: (error) => {
    set((state) => ({
      ...state,
      is_processing: false,
      current_task: state.current_task ? {
        ...state.current_task,
        status: 'error',
        error_message: error
      } : null
    }));
  },
  
  selectClaim: (claim_id) => set((state) => ({ ...state, selected_claim_id: claim_id })),
  setActiveDebate: (debate_id) => set((state) => ({ ...state, active_debate_id: debate_id })),
  toggleDebugInfo: () => set((state) => ({ ...state, show_debug_info: !state.show_debug_info })),
  
  reset: () => set((state) => ({
    ...state,
    current_task: null,
    is_processing: false,
    input_text: '',
    options: {
      enableFinancialVerification: true,
      enableCorporateVerification: true,
      enableNewsVerification: true,
      deepResearchMode: false
    },
    selected_claim_id: null,
    active_debate_id: null,
    show_debug_info: false
  })),
  
  getClaimById: (claim_id) => {
    const state = get();
    return state.current_task?.claims.find(c => c.id === claim_id);
  },
  
  getAgentByRole: (role) => {
    const state = get();
    return state.current_task?.agents.find(a => a.role === role);
  },
  
  getDebateById: (debate_id) => {
    const state = get();
    return state.current_task?.debates.find(d => d.id === debate_id);
  }
});