/**
 * 事实核查功能的状态管理
 * 使用Zustand进行状态管理
 */
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { FactCheckState, FactCheckOptions } from '@/types/factCheck';
import { createFactCheckActions, FactCheckActions } from './factCheckActions';

type FactCheckStore = FactCheckState & FactCheckActions;

const defaultOptions: FactCheckOptions = {
  enableFinancialVerification: true,
  enableCorporateVerification: true,
  enableNewsVerification: true,
  deepResearchMode: false
};

const initialState: FactCheckState = {
  current_task: null,
  is_processing: false,
  connection_status: 'connecting',
  daily_cost: 0,
  input_text: '',
  options: defaultOptions,
  selected_claim_id: null,
  active_debate_id: null,
  show_debug_info: false
};

export const useFactCheckStore = create<FactCheckStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      ...createFactCheckActions(set, get)
    }),
    {
      name: 'fact-check-store',
    }
  )
);

// 重新导出类型以保持向后兼容
export type { 
  Claim, 
  AgentStatus, 
  DebateMessage, 
  DebateRecord, 
  FactCheckTask, 
  FactCheckOptions 
} from '@/types/factCheck';