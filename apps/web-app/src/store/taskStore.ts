import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  AnalysisTask,
  TaskStatus
} from '@yai-investor-insight/shared-types';

interface TaskState {
  // 状态
  tasks: AnalysisTask[];
  currentTask: AnalysisTask | null;
  loading: boolean;
  error: string | null;
  
  // 过滤和排序
  statusFilter: TaskStatus | 'all';
  sortBy: 'created_at' | 'updated_at' | 'priority';
  sortOrder: 'asc' | 'desc';
  
  // Actions
  setTasks: (tasks: AnalysisTask[]) => void;
  addTask: (task: AnalysisTask) => void;
  updateTask: (taskId: string, updates: Partial<AnalysisTask>) => void;
  removeTask: (taskId: string) => void;
  setCurrentTask: (task: AnalysisTask | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 过滤和排序 Actions
  setStatusFilter: (status: TaskStatus | 'all') => void;
  setSortBy: (sortBy: 'created_at' | 'updated_at' | 'priority') => void;
  setSortOrder: (order: 'asc' | 'desc') => void;
  
  // 计算属性
  getFilteredTasks: () => AnalysisTask[];
  getTasksByStatus: (status: TaskStatus) => AnalysisTask[];
  getTaskStats: () => {
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
  };
}

export const useTaskStore = create<TaskState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      tasks: [],
      currentTask: null,
      loading: false,
      error: null,
      statusFilter: 'all',
      sortBy: 'created_at',
      sortOrder: 'desc',

      // Actions
      setTasks: (tasks) => set({ tasks }),
      
      addTask: (task) => set((state) => ({
        tasks: [task, ...state.tasks]
      })),
      
      updateTask: (taskId, updates) => set((state) => ({
        tasks: state.tasks.map(task => 
          task.id === taskId ? { ...task, ...updates } : task
        ),
        currentTask: state.currentTask?.id === taskId 
          ? { ...state.currentTask, ...updates }
          : state.currentTask
      })),
      
      removeTask: (taskId) => set((state) => ({
        tasks: state.tasks.filter(task => task.id !== taskId),
        currentTask: state.currentTask?.id === taskId ? null : state.currentTask
      })),
      
      setCurrentTask: (task) => set({ currentTask: task }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),
      
      // 过滤和排序
      setStatusFilter: (statusFilter) => set({ statusFilter }),
      setSortBy: (sortBy) => set({ sortBy }),
      setSortOrder: (sortOrder) => set({ sortOrder }),
      
      // 计算属性
      getFilteredTasks: () => {
        const { tasks, statusFilter, sortBy, sortOrder } = get();
        
        let filtered = tasks;
        
        // 状态过滤
        if (statusFilter !== 'all') {
          filtered = filtered.filter(task => task.status === statusFilter);
        }
        
        // 排序
        filtered.sort((a, b) => {
          let aValue: Date | number, bValue: Date | number;
          
          switch (sortBy) {
            case 'created_at':
              aValue = new Date(a.created_at);
              bValue = new Date(b.created_at);
              break;
            case 'updated_at':
              aValue = new Date(a.updated_at);
              bValue = new Date(b.updated_at);
              break;
            case 'priority':
              const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };
              aValue = priorityOrder[a.priority];
              bValue = priorityOrder[b.priority];
              break;
            default:
              return 0;
          }
          
          if (sortOrder === 'asc') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        });
        
        return filtered;
      },
      
      getTasksByStatus: (status) => {
        const { tasks } = get();
        return tasks.filter(task => task.status === status);
      },
      
      getTaskStats: () => {
        const { tasks } = get();
        return {
          total: tasks.length,
          pending: tasks.filter(t => t.status === TaskStatus.PENDING).length,
          processing: tasks.filter(t => t.status === TaskStatus.PROCESSING).length,
          completed: tasks.filter(t => t.status === TaskStatus.COMPLETED).length,
          failed: tasks.filter(t => t.status === TaskStatus.FAILED).length
        };
      }
    }),
    {
      name: 'task-store'
    }
  )
);
