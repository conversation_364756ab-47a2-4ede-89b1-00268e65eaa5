'use client';

import { useEffect, useState } from 'react';
import { logger } from '@/lib/logger';

interface DebugInfo {
  environment: {
    nodeEnv: string;
    isClient: boolean;
    isK8s: boolean;
    hostname: string;
  };
  envVars: Record<string, string>;
  loggerStatus: {
    initialized: boolean;
    error?: string;
  };
  testResults: {
    consoleLog: boolean;
    slsLog: boolean;
    error?: string;
  };
}

export default function DebugLoggingPage() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const getEnvVar = (key: string, defaultValue: string = ''): string => {
    try {
      if (typeof window !== 'undefined') {
        return (window as any).__NEXT_DATA__?.env?.[key] || 
               (window as any).__ENV__?.[key] ||
               process.env[key] || 
               defaultValue;
      }
      return process.env[key] || defaultValue;
    } catch (error) {
      return defaultValue;
    }
  };

  const isK8sEnvironment = (): boolean => {
    return !!(
      getEnvVar('KUBERNETES_SERVICE_HOST') ||
      getEnvVar('KUBERNETES_PORT') ||
      getEnvVar('KUBERNETES_DEPLOYMENT') ||
      getEnvVar('HOSTNAME', '').includes('pod') ||
      getEnvVar('NODE_NAME')
    );
  };

  const collectDebugInfo = async (): Promise<DebugInfo> => {
    const envVars = {
      'NODE_ENV': getEnvVar('NODE_ENV'),
      'NEXT_PUBLIC_SLS_ENABLED': getEnvVar('NEXT_PUBLIC_SLS_ENABLED'),
      'NEXT_PUBLIC_SLS_ENDPOINT': getEnvVar('NEXT_PUBLIC_SLS_ENDPOINT'),
      'NEXT_PUBLIC_SLS_ACCESS_KEY_ID': getEnvVar('NEXT_PUBLIC_SLS_ACCESS_KEY_ID'),
      'NEXT_PUBLIC_SLS_PROJECT': getEnvVar('NEXT_PUBLIC_SLS_PROJECT'),
      'NEXT_PUBLIC_SLS_LOGSTORE': getEnvVar('NEXT_PUBLIC_SLS_LOGSTORE'),
      'NEXT_PUBLIC_SLS_REGION': getEnvVar('NEXT_PUBLIC_SLS_REGION'),
      'NEXT_PUBLIC_SERVICE_NAME': getEnvVar('NEXT_PUBLIC_SERVICE_NAME'),
      'NEXT_PUBLIC_APP_VERSION': getEnvVar('NEXT_PUBLIC_APP_VERSION'),
      'KUBERNETES_SERVICE_HOST': getEnvVar('KUBERNETES_SERVICE_HOST'),
      'KUBERNETES_PORT': getEnvVar('KUBERNETES_PORT'),
      'KUBERNETES_DEPLOYMENT': getEnvVar('KUBERNETES_DEPLOYMENT'),
      'HOSTNAME': getEnvVar('HOSTNAME'),
    };

    let loggerStatus = { initialized: false };
    let testResults = { consoleLog: false, slsLog: false };

    try {
      // 测试日志器初始化
      await logger.info('调试页面测试日志', { 
        timestamp: new Date().toISOString(),
        testType: 'debug-page'
      });
      loggerStatus.initialized = true;
      testResults.consoleLog = true;
      testResults.slsLog = true;
    } catch (error) {
      loggerStatus.error = error instanceof Error ? error.message : String(error);
      testResults.error = loggerStatus.error;
    }

    return {
      environment: {
        nodeEnv: getEnvVar('NODE_ENV', 'unknown'),
        isClient: typeof window !== 'undefined',
        isK8s: isK8sEnvironment(),
        hostname: getEnvVar('HOSTNAME', 'unknown')
      },
      envVars,
      loggerStatus,
      testResults
    };
  };

  useEffect(() => {
    const loadDebugInfo = async () => {
      try {
        const info = await collectDebugInfo();
        setDebugInfo(info);
      } catch (error) {
        console.error('收集调试信息失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDebugInfo();
  }, []);

  const testLogger = async () => {
    try {
      await logger.info('手动测试日志', { 
        timestamp: new Date().toISOString(),
        testType: 'manual-test',
        userAgent: navigator.userAgent
      });
      alert('日志测试完成，请检查控制台和SLS');
    } catch (error) {
      alert(`日志测试失败: ${error}`);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在收集调试信息...</p>
        </div>
      </div>
    );
  }

  if (!debugInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">无法收集调试信息</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-900">
              日志系统调试信息
            </h1>
            <button
              onClick={testLogger}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
            >
              测试日志
            </button>
          </div>

          {/* 环境信息 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">环境信息</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">Node环境</div>
                <div className="font-medium">{debugInfo.environment.nodeEnv}</div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">客户端</div>
                <div className="font-medium">{debugInfo.environment.isClient ? '是' : '否'}</div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">K8s环境</div>
                <div className={`font-medium ${debugInfo.environment.isK8s ? 'text-green-600' : 'text-gray-600'}`}>
                  {debugInfo.environment.isK8s ? '是' : '否'}
                </div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">主机名</div>
                <div className="font-medium text-xs">{debugInfo.environment.hostname}</div>
              </div>
            </div>
          </div>

          {/* 日志器状态 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">日志器状态</h2>
            <div className="bg-gray-50 p-4 rounded">
              <div className="flex items-center mb-2">
                <div className={`w-3 h-3 rounded-full mr-2 ${debugInfo.loggerStatus.initialized ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="font-medium">
                  {debugInfo.loggerStatus.initialized ? '已初始化' : '初始化失败'}
                </span>
              </div>
              {debugInfo.loggerStatus.error && (
                <div className="text-red-600 text-sm mt-2">
                  错误: {debugInfo.loggerStatus.error}
                </div>
              )}
            </div>
          </div>

          {/* 测试结果 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">测试结果</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 p-4 rounded">
                <div className="flex items-center mb-2">
                  <div className={`w-3 h-3 rounded-full mr-2 ${debugInfo.testResults.consoleLog ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="font-medium">控制台日志</span>
                </div>
                <div className="text-sm text-gray-600">
                  {debugInfo.testResults.consoleLog ? '正常' : '失败'}
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded">
                <div className="flex items-center mb-2">
                  <div className={`w-3 h-3 rounded-full mr-2 ${debugInfo.testResults.slsLog ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="font-medium">SLS日志</span>
                </div>
                <div className="text-sm text-gray-600">
                  {debugInfo.testResults.slsLog ? '正常' : '失败'}
                </div>
              </div>
            </div>
            {debugInfo.testResults.error && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                <div className="text-red-800 text-sm">
                  <strong>错误详情:</strong> {debugInfo.testResults.error}
                </div>
              </div>
            )}
          </div>

          {/* 环境变量 */}
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-4">环境变量</h2>
            <div className="bg-gray-50 p-4 rounded overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-300">
                    <th className="text-left py-2 font-medium">变量名</th>
                    <th className="text-left py-2 font-medium">值</th>
                    <th className="text-left py-2 font-medium">状态</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(debugInfo.envVars).map(([key, value]) => (
                    <tr key={key} className="border-b border-gray-200">
                      <td className="py-2 font-mono text-xs">{key}</td>
                      <td className="py-2 font-mono text-xs">
                        {value ? (
                          key.includes('SECRET') || key.includes('KEY') ? 
                            '***' + value.slice(-4) : 
                            value
                        ) : (
                          <span className="text-gray-400">未设置</span>
                        )}
                      </td>
                      <td className="py-2">
                        <span className={`inline-block w-2 h-2 rounded-full ${value ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
