'use client';

import { useState } from 'react';
import { TaskStatus, AnalysisTask } from '@yai-investor-insight/shared-types';
import { Layout } from '@/components/layout/Layout';
import { TaskCard } from '@/components/tasks/TaskCard';
import { Button } from '@/components/ui/Button';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { useTasks, useTaskStats } from '@/hooks/useTasks';
import { useTaskStore } from '@/store/taskStore';
import { 
  FunnelIcon,
  ArrowsUpDownIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

const statusOptions = [
  { value: 'all', label: '全部' },
  { value: TaskStatus.PENDING, label: '等待中' },
  { value: TaskStatus.PROCESSING, label: '处理中' },
  { value: TaskStatus.COMPLETED, label: '已完成' },
  { value: TaskStatus.FAILED, label: '失败' }
];

const sortOptions = [
  { value: 'created_at', label: '创建时间' },
  { value: 'updated_at', label: '更新时间' },
  { value: 'priority', label: '优先级' }
];

export default function TasksPage() {
  const [, setSelectedTask] = useState<AnalysisTask | null>(null);
  
  const {
    statusFilter,
    sortBy,
    sortOrder,
    getFilteredTasks,
    setStatusFilter,
    setSortBy,
    setSortOrder
  } = useTaskStore();

  const taskStats = useTaskStats();
  const filteredTasks = getFilteredTasks();
  
  // 获取任务数据
  const { isLoading, error } = useTasks();

  const handleSortOrderToggle = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };

  if (error) {
    return (
      <Layout>
        <div className="text-center py-12">
          <p className="text-red-600">
            加载任务失败: {error?.message || '未知错误'}
          </p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              任务管理
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              管理和监控所有分析任务
            </p>
          </div>
          
          <div className="mt-4 sm:mt-0">
            <Link href="/create">
              <Button>
                <PlusIcon className="w-4 h-4 mr-2" />
                创建任务
              </Button>
            </Link>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    总任务
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {taskStats.total}
                  </p>
                </div>
                <Badge variant="default">{taskStats.total}</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    等待中
                  </p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {taskStats.pending}
                  </p>
                </div>
                <Badge variant="warning">{taskStats.pending}</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    处理中
                  </p>
                  <p className="text-2xl font-bold text-blue-600">
                    {taskStats.processing}
                  </p>
                </div>
                <Badge variant="info">{taskStats.processing}</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    已完成
                  </p>
                  <p className="text-2xl font-bold text-green-600">
                    {taskStats.completed}
                  </p>
                </div>
                <Badge variant="success">{taskStats.completed}</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    失败
                  </p>
                  <p className="text-2xl font-bold text-red-600">
                    {taskStats.failed}
                  </p>
                </div>
                <Badge variant="danger">{taskStats.failed}</Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 过滤和排序 */}
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <FunnelIcon className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    状态筛选:
                  </span>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value as TaskStatus | 'all')}
                    className="rounded-md border border-gray-300 bg-white px-3 py-1 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    {statusOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  <ArrowsUpDownIcon className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    排序:
                  </span>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as 'created_at' | 'updated_at' | 'priority')}
                    className="rounded-md border border-gray-300 bg-white px-3 py-1 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    {sortOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSortOrderToggle}
                  >
                    {sortOrder === 'asc' ? '升序' : '降序'}
                  </Button>
                </div>
              </div>

              <div className="text-sm text-gray-500">
                显示 {filteredTasks.length} 个任务
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* 任务列表 */}
        {isLoading ? (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-500">加载中...</p>
          </div>
        ) : filteredTasks.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">
              {statusFilter === 'all' ? '暂无任务' : `暂无${statusOptions.find(o => o.value === statusFilter)?.label}任务`}
            </p>
            <Link href="/create" className="mt-4 inline-block">
              <Button>创建第一个任务</Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTasks.map((task) => (
              <TaskCard
                key={task.id}
                task={task}
                onView={setSelectedTask}
              />
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
}
