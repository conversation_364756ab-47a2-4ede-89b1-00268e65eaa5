'use client';

import { <PERSON><PERSON><PERSON> } from "@/components/charts/StockChart";
import { FinancialChart } from "@/components/charts/FinancialChart";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/Button";
import { Card, CardHeader, CardContent } from "@/components/ui/Card";

import { useTaskStats } from "@/hooks/useTasks";
import Link from "next/link";
import {
  PlusIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline";

function HomeContent() {
  const taskStats = useTaskStats();

  return (
    <div className="space-y-8">
      {/* 欢迎区域 */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          🤖 AI 投资洞察平台
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          基于 AI Agent 的现代化投资研究平台
        </p>
        <div className="flex justify-center space-x-4">
          <Link href="/create">
            <Button size="lg">
              <PlusIcon className="w-5 h-5 mr-2" />
              创建分析任务
            </Button>
          </Link>
          <Link href="/tasks">
            <Button variant="outline" size="lg">
              <ChartBarIcon className="w-5 h-5 mr-2" />
              查看任务
            </Button>
          </Link>
        </div>
      </div>

      {/* 任务统计 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  总任务数
                </p>
                <p className="text-3xl font-bold text-gray-900">
                  {taskStats.total}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <ChartBarIcon className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  处理中
                </p>
                <p className="text-3xl font-bold text-blue-600">
                  {taskStats.processing}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <ClockIcon className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  已完成
                </p>
                <p className="text-3xl font-bold text-green-600">
                  {taskStats.completed}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircleIcon className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  成功率
                </p>
                <p className="text-3xl font-bold text-purple-600">
                  {taskStats.total > 0
                    ? Math.round((taskStats.completed / taskStats.total) * 100)
                    : 0
                  }%
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <ChartBarIcon className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 功能特性 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900">
              ✨ 核心功能
            </h2>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-3">
                <span className="text-blue-500">📊</span>
                <span className="text-gray-700">股票分析</span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-green-500">📈</span>
                <span className="text-gray-700">市场趋势</span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-purple-500">🔍</span>
                <span className="text-gray-700">深度洞察</span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-orange-500">🤖</span>
                <span className="text-gray-700">AI 助手</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-2xl font-semibold text-gray-900">
              🚀 快速开始
            </h3>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              开始您的投资研究之旅，体验 AI 驱动的分析能力
            </p>
            <div className="space-y-2 text-sm text-gray-500">
              <p className="font-medium">💡 您可以：</p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>分析特定股票的投资价值</li>
                <li>研究市场趋势和行业动态</li>
                <li>获取财务报告深度解读</li>
                <li>监控新闻情感分析</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 数据可视化展示 */}
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            📊 数据可视化展示
          </h2>
          <p className="text-gray-600">
            体验我们强大的数据分析和可视化能力
          </p>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          <StockChart symbol="AAPL" />
          <FinancialChart type="revenue" company="苹果公司" />
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          <FinancialChart type="profit" company="苹果公司" />
          <FinancialChart type="trend" company="苹果公司" />
        </div>
      </div>
    </div>
  );
}

export default function Home() {
  return (
    <Layout>
      <HomeContent />
    </Layout>
  );
}
