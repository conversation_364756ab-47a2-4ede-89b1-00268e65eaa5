'use client';

import { useEffect } from 'react';
import { Layout } from "@/components/layout/Layout";
import { HeroSearchSection } from "@/components/home/<USER>";
import { WorkspaceSection } from "@/components/workspace/WorkspaceSection";
import { CommunitySection } from "@/components/home/<USER>";
import { logger, ContextLogger, logComponentMount } from '@/lib/logger';

export default function HomePage() {
  useEffect(() => {
    // 记录主页加载
    const logPageLoad = async () => {
      try {
        await logger.info('JS前端主页加载', {
          page: 'home',
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href,
          referrer: document.referrer || 'direct'
        });

        await logComponentMount('HomePage');
      } catch (error) {
        console.error('日志记录失败:', error);
      }
    };

    logPageLoad();
  }, []);
  return (
    <Layout>
      <main className="flex flex-1 flex-col">
        {/* Hero Search Section - 顶部紫色搜索区域 */}
        <HeroSearchSection />
        
        {/* Workspace Section - 我的工作区 */}
        <div className="bg-white">
          <WorkspaceSection />
        </div>
        
        {/* Community Section - 来自社区 */}
        <CommunitySection />
      </main>
    </Layout>
  );
} 