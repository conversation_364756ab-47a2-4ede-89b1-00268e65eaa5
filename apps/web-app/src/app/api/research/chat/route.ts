import { NextRequest } from 'next/server';
import { serverLogger, logApiRouteRequest, logApiRouteResponse, generateTraceId } from '@/lib/server-logger';

export async function POST(request: NextRequest) {
  const traceId = generateTraceId();
  const startTime = Date.now();
  
  try {
    const body = await request.json();
    
    // 记录请求日志
    await logApiRouteRequest('POST', '/api/research/chat', traceId, {
      bodyKeys: Object.keys(body),
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for') || 'unknown'
    });
    
    // 转发请求到后端
    const response = await fetch('http://yai-investor-insight-api-server-service/research/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Trace-ID': traceId, // 传递 TraceID
      },
      body: JSON.stringify(body),
    });

    const duration = Date.now() - startTime;
    
    // 记录成功响应
    await logApiRouteResponse('POST', '/api/research/chat', traceId, response.status, duration);

    // 返回流式响应
    return new Response(response.body, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Trace-ID': traceId, // 返回 TraceID 给前端
      },
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // 记录错误日志
    await logApiRouteResponse('POST', '/api/research/chat', traceId, 500, duration, error);
    
    return Response.json({ 
      error: 'Internal Server Error',
      traceId 
    }, { 
      status: 500,
      headers: {
        'X-Trace-ID': traceId
      }
    });
  }
}