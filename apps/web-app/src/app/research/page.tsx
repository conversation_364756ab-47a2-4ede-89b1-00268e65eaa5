'use client';
import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/layout/Layout';
import { TaskStatusCard } from '@/components/research/TaskStatusCard'; // 确保 TaskInfo 类型也在该文件中导出或单独导入
import { AnalysisViewer } from '@/components/research/AnalysisViewer';
import { MessageLog } from '@/components/research/MessageLog';
import { Button } from '@/components/ui/Button';
// 移除了未使用的 TaskProgressCard 导入
import { useAGUI } from '@/hooks/useAGUI';
import { BarChart3 } from 'lucide-react';
import { useResearchLogic } from '@/lib/research/researchLogic';
import { ResearchAnalysisState } from '@/types/research'; // 确保此类型存在

// 注意: 如果 TaskStatusCard 文件没有同时导出 TaskInfo 类型，您可能需要单独导入它
// import { TaskInfo } from '@/types/task'; // 假设 TaskInfo 在这里定义

export default function ResearchAnalysisPage() {
  const [state, setState] = useState<ResearchAnalysisState>({
    tasks: [],
    activeTask: '',
    contentData: {},
    messages: [],
    currentMessage: '',
    currentStep: '',
    // 添加缺少的字段
    currentTaskContent: {},
    currentTypingTaskId: '',
    nodeTaskMapping: {} // 添加这个缺少的字段
  });

  // 新增：管理标题输入的状态
  const [researchTitle, setResearchTitle] = useState('特斯拉宣布降价20%，影响电动汽车行业竞争格局');

  const { 
    handleAGUIEvent, 
    startAnalysis, 
    useLifecycleLogging 
  } = useResearchLogic({ state, setState });

  React.useEffect(useLifecycleLogging, []);

  const { connectionStatus, isRunning, sendMessage } = useAGUI({
    url: "/api/research/chat", // 使用相对路径
    onEvent: handleAGUIEvent,
    onError: (error) => {
      setState(prev => ({
        ...prev,
        messages: [...prev.messages, `❌ 连接错误: ${error.message || '研究分析后端服务可能未启动'}`]
      }));
    },
    onComplete: () => console.log("AG-UI 流程完成")
  });

  // 修改处理开始分析的函数
  const handleStartAnalysis = async () => {
    // 修改 sendMessage 调用以匹配正确的类型签名
    const stopFunction = await sendMessage(researchTitle);
    // 如果需要，可以保存 stopFunction 用于后续停止操作
  };

  // 添加状态变化监听
  useEffect(() => {
    console.log('🏠 [ResearchPage] 状态变化:', {
      currentStep: state.currentStep,
      activeTask: state.activeTask,
      tasksCount: state.tasks.length,
      contentDataKeys: Object.keys(state.contentData),
      messagesCount: state.messages.length,
      currentMessageLength: state.currentMessage.length,
      currentTypingTaskId: state.currentTypingTaskId,
      timestamp: new Date().toISOString()
    });
    
    // 🔍 添加详细的 currentTypingTaskId 追踪
    console.log('🎯 [ResearchPage] currentTypingTaskId 详细信息:', {
      currentTypingTaskId: state.currentTypingTaskId,
      currentTaskContentKeys: Object.keys(state.currentTaskContent),
      currentTaskContentSummary: Object.entries(state.currentTaskContent).map(([key, content]) => ({
        taskId: key,
        contentLength: content.length,
        contentPreview: content.substring(0, 50) + (content.length > 50 ? '...' : '')
      })),
      activeTask: state.activeTask,
      isTypingTaskMatchingActive: state.currentTypingTaskId === state.activeTask
    });
  }, [state]);

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 flex">
        {/* 主要内容区域 */}
        <div className="flex-1 flex">
          {/* 左侧任务列表 */}
          <div className="w-80 bg-white border-r border-gray-200 p-6">
            <div className="mb-6">
              <button className="text-blue-600 hover:text-blue-700 flex items-center space-x-2 mb-4">
                <span>← 返回首页</span>
              </button>
              {/* 将固定标题改为可输入的文本框 */}
              <div className="mb-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  研究主题
                </label>
                <textarea
                  value={researchTitle}
                  onChange={(e) => setResearchTitle(e.target.value)}
                  className="w-full text-lg font-semibold text-gray-900 bg-white border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={2}
                  placeholder="请输入研究主题..."
                />
              </div>
              {/* 开始分析按钮 */}
              <Button
                onClick={handleStartAnalysis}
                disabled={isRunning || connectionStatus !== 'connected' || !researchTitle.trim()}
                className={`w-full mt-4 ${
                  isRunning || connectionStatus !== 'connected' || !researchTitle.trim()
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {isRunning ? '标准 AG-UI 分析中...' : 
                 connectionStatus !== 'connected' ? 'AG-UI 未连接' : 
                 !researchTitle.trim() ? '请输入研究主题' : '开始标准 AG-UI 分析'}
              </Button>
              {/* 状态指示 */}
              <div className="mt-2 text-xs text-center text-gray-500">
                使用标准 AG-UI 事件处理系统
              </div>
            </div>
            {/* 任务状态列表 */}
            <div className="space-y-4">
              {state.tasks.map((task) => (
              <div
                key={task.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  state.activeTask === task.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setState(prev => ({ ...prev, activeTask: task.id }))}
              >
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-900">
                    {task.title}
                    {task.nodeType && (
                      <span className="ml-2 text-xs text-gray-500">
                        [{task.nodeType}]
                      </span>
                    )}
                  </h3>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    task.status === 'completed'
                      ? 'bg-green-100 text-green-800'
                      : task.status === 'in_progress'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {task.status === 'completed' ? '✅ 完成' : 
                     task.status === 'in_progress' ? '🔄 进行中' : '⏳ 等待'}
                  </span>
                </div>
                {task.description && (
                  <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                )}
              </div>
              ))}
            </div>
            {/* 消息日志 */}
            <MessageLog messages={state.messages} />
          </div>
          {/* 右侧内容区域 */}
          <div className="flex-1 bg-white">
            {/* 顶部状态栏 */}
            <div className="border-b border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <BarChart3 className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h1 className="text-xl font-semibold text-gray-900">
                      {state.activeTask && state.tasks.find(t => t.id === state.activeTask)?.title || '标准 AG-UI 投资研究分析'}
                    </h1>
                    <p className="text-sm text-gray-600">
                      {state.activeTask && state.tasks.find(t => t.id === state.activeTask)?.description || '等待开始标准 AG-UI 分析...'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {/* AG-UI 连接状态 */}
                  <div className="flex items-center space-x-2 text-sm">
                    <div className={`w-2 h-2 rounded-full ${
                      connectionStatus === 'connected' ? 'bg-green-500' : 
                      connectionStatus === 'error' ? 'bg-red-500' : 'bg-yellow-500'
                    }`}></div>
                    <span className="text-gray-600">
                      {connectionStatus === 'connected' ? 'AG-UI 已连接' : 
                       connectionStatus === 'error' ? 'AG-UI 连接失败' : 'AG-UI 连接中...'}
                    </span>
                  </div>
                  {/* 当前步骤指示 */}
                  {state.currentStep && (
                    <div className="text-sm text-cyan-600 bg-cyan-50 px-2 py-1 rounded">
                      当前: {state.currentStep}
                    </div>
                  )}
                  {/* 任务状态 */}
                  {state.activeTask && state.tasks.find(t => t.id === state.activeTask) && (
                    <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                      state.tasks.find(t => t.id === state.activeTask)?.status === 'completed' 
                        ? 'bg-green-100 text-green-800'
                        : state.tasks.find(t => t.id === state.activeTask)?.status === 'in_progress'
                        ? 'bg-blue-100 text-blue-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {state.tasks.find(t => t.id === state.activeTask)?.status === 'completed' ? '已完成' : 
                       state.tasks.find(t => t.id === state.activeTask)?.status === 'in_progress' ? '标准 AG-UI 进行中' : '等待中'}
                    </span>
                  )}
                </div>
              </div>
            </div>
            {/* 内容区域 */}
            <div className="p-6 overflow-y-auto">
              {/* 移除了打字机卡片区域 */}
              <AnalysisViewer
                activeTask={state.activeTask}
                contentData={state.contentData}
                currentStep={state.currentStep}
                currentTaskContent={state.currentTaskContent} // 添加这行
                currentTypingTaskId={state.currentTypingTaskId} // 添加这行
                onStepChange={(step) => {
                  setState(prev => ({ ...prev, currentStep: step }));
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
