import { useRef, useEffect, useState } from 'react';
import { HttpAgent } from '@ag-ui/client';
import { ContextLogger, generateTraceId } from '../lib/logger';

export interface AGUIEvent {
  type: string;
  messageId?: string;
  threadId?: string;
  runId?: string;
  delta?: string;
  content?: string;
  data?: any;
  rawEvent?: any;
  custom?: any;
  state?: any;
  tool_call_id?: string;
  tool_call_name?: string;
  step_name?: string;
  error?: string;
  value?: any;
}

export interface UseAGUIOptions {
  url: string;
  onEvent?: (event: AGUIEvent) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
}

export function useAGUI(options: UseAGUIOptions) {
  const agentRef = useRef<HttpAgent | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error'>('connecting');
  const [isRunning, setIsRunning] = useState(false);

  // 创建专用的日志器
  const contextLogger = useRef(new ContextLogger({
    component: 'useAGUI',
    url: options.url
  }));

  useEffect(() => {
    try {
      agentRef.current = new HttpAgent({
        url: options.url
      });
      setConnectionStatus('connected');
      contextLogger.current.info('AG-UI HttpAgent 初始化成功');
    } catch (error) {
      contextLogger.current.error('AG-UI 客户端初始化失败', { error });
      setConnectionStatus('error');
      options.onError?.(error as Error);
    }
  }, [options.url]);

  const sendMessage = async (
    message: string,
    threadId?: string,
    runId?: string
  ) => {
    if (!agentRef.current || isRunning) {
      contextLogger.current.warn('AG-UI 客户端未准备好或正在运行中');
      return;
    }

    const traceId = generateTraceId();
    const finalThreadId = threadId || `thread_${Date.now()}`;
    const finalRunId = runId || `run_${Date.now()}`;

    setIsRunning(true);

    // 更新日志器上下文
    contextLogger.current.updateContext({
      traceId,
      threadId: finalThreadId,
      runId: finalRunId
    });

    try {
      const request = {
        threadId: finalThreadId,
        runId: finalRunId,
        messages: [
          {
            id: `msg_${Date.now()}`,
            role: "user" as const,
            content: message
          }
        ],
        tools: [],
        context: [],
        state: {},
        forwardedProps: {}
      };

      contextLogger.current.info('发送 AG-UI 消息', {
        message,
        request: JSON.stringify(request)
      });

      const stream = agentRef.current.run(request);

      const subscription = stream.subscribe({
        next: (event: any) => {
          contextLogger.current.debug('收到 AG-UI 事件', {
            eventType: event.type,
            event: JSON.stringify(event)
          });
          options.onEvent?.(event as AGUIEvent);
        },
        error: (error: any) => {
          contextLogger.current.error('AG-UI 事件流错误', { error });
          setIsRunning(false);
          options.onError?.(error);
        },
        complete: () => {
          contextLogger.current.info('AG-UI 事件流完成');
          setIsRunning(false);
          options.onComplete?.();
        }
      });

      return () => {
        subscription?.unsubscribe?.();
      };

    } catch (error) {
      contextLogger.current.error('发送消息失败', { error });
      setIsRunning(false);
      options.onError?.(error as Error);
    }
  };

  return {
    connectionStatus,
    isRunning,
    sendMessage,
    agent: agentRef.current
  };
}