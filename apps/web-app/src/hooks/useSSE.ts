import { useEffect, useCallback, useRef } from 'react';
import { SSEEvent } from '@yai-investor-insight/shared-types';
import { sseClient } from '@/lib/api';
import { useTaskStore } from '@/store/taskStore';

export const useSSE = () => {
  const { updateTask, addTask } = useTaskStore();
  const isConnectedRef = useRef(false);

  // 处理任务更新事件
  const handleTaskUpdate = useCallback((event: SSEEvent) => {
    if (event.type === 'task_update' && event.data) {
      updateTask(event.data.id, event.data);
    }
  }, [updateTask]);

  // 处理任务创建事件
  const handleTaskCreated = useCallback((event: SSEEvent) => {
    if (event.type === 'task_created' && event.data) {
      addTask(event.data);
    }
  }, [addTask]);

  // 处理任务完成事件
  const handleTaskCompleted = useCallback((event: SSEEvent) => {
    if (event.type === 'task_completed' && event.data) {
      updateTask(event.data.id, { 
        ...event.data, 
        status: 'completed',
        completed_at: new Date().toISOString()
      });
    }
  }, [updateTask]);

  // 处理任务失败事件
  const handleTaskFailed = useCallback((event: SSEEvent) => {
    if (event.type === 'task_failed' && event.data) {
      updateTask(event.data.id, { 
        ...event.data, 
        status: 'failed'
      });
    }
  }, [updateTask]);

  // 处理心跳事件
  const handleHeartbeat = useCallback((event: SSEEvent) => {
    console.log('SSE heartbeat received:', event.timestamp);
  }, []);

  // 连接 SSE
  const connect = useCallback(() => {
    if (isConnectedRef.current) return;

    try {
      // 注册事件监听器
      sseClient.on('task_update', handleTaskUpdate);
      sseClient.on('task_created', handleTaskCreated);
      sseClient.on('task_completed', handleTaskCompleted);
      sseClient.on('task_failed', handleTaskFailed);
      sseClient.on('heartbeat', handleHeartbeat);

      // 连接
      sseClient.connect();
      isConnectedRef.current = true;
      
      console.log('SSE connected successfully');
    } catch (error) {
      console.error('Failed to connect SSE:', error);
    }
  }, [
    handleTaskUpdate,
    handleTaskCreated,
    handleTaskCompleted,
    handleTaskFailed,
    handleHeartbeat
  ]);

  // 断开连接
  const disconnect = useCallback(() => {
    if (!isConnectedRef.current) return;

    try {
      // 移除事件监听器
      sseClient.off('task_update', handleTaskUpdate);
      sseClient.off('task_created', handleTaskCreated);
      sseClient.off('task_completed', handleTaskCompleted);
      sseClient.off('task_failed', handleTaskFailed);
      sseClient.off('heartbeat', handleHeartbeat);

      // 断开连接
      sseClient.disconnect();
      isConnectedRef.current = false;
      
      console.log('SSE disconnected');
    } catch (error) {
      console.error('Failed to disconnect SSE:', error);
    }
  }, [
    handleTaskUpdate,
    handleTaskCreated,
    handleTaskCompleted,
    handleTaskFailed,
    handleHeartbeat
  ]);

  // 组件挂载时连接，卸载时断开
  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // 页面可见性变化时重连
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        disconnect();
      } else {
        connect();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [connect, disconnect]);

  return {
    connect,
    disconnect,
    isConnected: isConnectedRef.current
  };
};
