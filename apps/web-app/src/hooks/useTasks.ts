
import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  AnalysisTask, 
  TaskStatus, 
  AnalysisType, 
  TaskPriority 
} from '@yai-investor-insight/shared-types';
import { apiClient } from '@/lib/api';
import { useTaskStore } from '@/store/taskStore';

// 获取任务列表
export const useTasks = (params?: {
  status?: TaskStatus;
  page?: number;
  page_size?: number;
}) => {
  const { setTasks, setLoading, setError } = useTaskStore();

  const query = useQuery({
    queryKey: ['tasks', params],
    queryFn: () => apiClient.getTasks(params),
    refetchInterval: 5000, // 每5秒刷新一次
    staleTime: 1000 * 60 * 2, // 2分钟内认为数据是新鲜的
  });

  // 使用 useEffect 替代 onSuccess/onError
  React.useEffect(() => {
    if (query.data) {
      if (query.data.success && query.data.data) {
        setTasks(query.data.data.items);
      }
      setLoading(false);
      setError(null);
    }
  }, [query.data, setTasks, setLoading, setError]);

  React.useEffect(() => {
    if (query.error) {
      setLoading(false);
      setError((query.error as Error).message || '获取任务列表失败');
    }
  }, [query.error, setLoading, setError]);

  return query;
};

// 获取单个任务
export const useTask = (taskId: string) => {
  const { setCurrentTask } = useTaskStore();

  const query = useQuery({
    queryKey: ['task', taskId],
    queryFn: () => apiClient.getTask(taskId),
    enabled: !!taskId,
    refetchInterval: 2000, // 每2秒刷新一次
  });

  React.useEffect(() => {
    if (query.data && query.data.success && query.data.data) {
      setCurrentTask(query.data.data);
    }
  }, [query.data, setCurrentTask]);

  return query;
};

// 创建任务
export const useCreateTask = () => {
  const queryClient = useQueryClient();
  const { addTask } = useTaskStore();

  return useMutation({
    mutationFn: (taskData: {
      title: string;
      description?: string;
      analysis_type: AnalysisType;
      input_data: Record<string, unknown>;
      priority?: TaskPriority;
    }) => apiClient.createTask(taskData),
    onSuccess: (response) => {
      if (response.success && response.data) {
        addTask(response.data);
        queryClient.invalidateQueries({ queryKey: ['tasks'] });
      }
    },
    onError: (error: Error) => {
      console.error('创建任务失败:', error);
    }
  });
};

// 更新任务
export const useUpdateTask = () => {
  const queryClient = useQueryClient();
  const { updateTask } = useTaskStore();

  return useMutation({
    mutationFn: ({ taskId, updates }: { taskId: string; updates: Partial<AnalysisTask> }) =>
      apiClient.updateTask(taskId, updates),
    onSuccess: (response, variables) => {
      if (response.success && response.data) {
        updateTask(variables.taskId, response.data);
        queryClient.invalidateQueries({ queryKey: ['tasks'] });
        queryClient.invalidateQueries({ queryKey: ['task', variables.taskId] });
      }
    }
  });
};

// 删除任务
export const useDeleteTask = () => {
  const queryClient = useQueryClient();
  const { removeTask } = useTaskStore();

  return useMutation({
    mutationFn: (taskId: string) => apiClient.deleteTask(taskId),
    onSuccess: (response, taskId) => {
      if (response.success) {
        removeTask(taskId);
        queryClient.invalidateQueries({ queryKey: ['tasks'] });
      }
    }
  });
};

// 文件上传
export const useUploadFile = () => {
  return useMutation({
    mutationFn: (file: File) => apiClient.uploadFile(file),
    onError: (error: Error) => {
      console.error('文件上传失败:', error);
    }
  });
};

// 任务统计 Hook
export const useTaskStats = () => {
  const { getTaskStats } = useTaskStore();
  return getTaskStats();
};
