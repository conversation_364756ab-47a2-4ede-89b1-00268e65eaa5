/**
 * 事实核查功能的AGUI CustomEvent集成
 * 扩展现有的AGUI系统，支持事实核查专用事件
 */
import { useRef, useCallback } from 'react';
import { useAGUI } from './useAGUI';
import { useFactCheckEventHandler } from './factCheckEvents';
import { ContextLogger } from '../lib/logger';
import { 
  createFactCheckMessage, 
  createCancelMessage, 
  formatFactCheckError, 
  FactCheckStartOptions 
} from '../lib/factCheckUtils';
import { 
  FactCheckTask,
  Claim, 
  AgentStatus, 
  DebateRecord, 
  DebateMessage
} from '@/types/factCheck';

interface UseFactCheckAGUIOptions {
  onTaskStarted?: (task: FactCheckTask) => void;
  onClaimExtracted?: (claims: Claim[]) => void;
  onAgentProgress?: (agentStatus: AgentStatus) => void;
  onVerificationComplete?: (claim: Claim) => void;
  onDebateStarted?: (debate: DebateRecord) => void;
  onDebateMessage?: (claimId: string, message: DebateMessage) => void;
  onDebateConcluded?: (debate: DebateRecord) => void;
  onTaskComplete?: (task: FactCheckTask) => void;
  onCostUpdate?: (cost: number) => void;
  onError?: (error: string) => void;
}

export function useFactCheckAGUI(options: UseFactCheckAGUIOptions) {
  const contextLogger = useRef(new ContextLogger({
    component: 'useFactCheckAGUI'  
  }));

  const handleAGUIEvent = useFactCheckEventHandler(options);

  const { connectionStatus, isRunning, sendMessage, agent } = useAGUI({
    url: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
    onEvent: handleAGUIEvent,
    onError: (error) => {
      contextLogger.current.error('AGUI连接错误', { error });
      options.onError?.(formatFactCheckError(error));
    },
    onComplete: () => {
      contextLogger.current.info('AGUI事件流完成');
    }
  });

  const startFactCheck = useCallback(async (
    text: string,
    factCheckOptions: FactCheckStartOptions = {}
  ) => {
    if (!agent || isRunning) {
      contextLogger.current.warn('AGUI客户端未准备好或正在运行中');
      return;
    }

    const taskId = `fact_check_${Date.now()}`;
    
    contextLogger.current.info('启动事实核查任务', {
      taskId,
      textLength: text.length,
      options: factCheckOptions
    });

    const message = createFactCheckMessage(text, factCheckOptions);

    try {
      const unsubscribe = await sendMessage(message, taskId, `run_${taskId}`);
      return { taskId, unsubscribe };
    } catch (error) {
      contextLogger.current.error('启动事实核查失败', { error, taskId });
      throw error;
    }
  }, [agent, isRunning, sendMessage]);

  const cancelFactCheck = useCallback(async (taskId: string) => {
    if (!agent) {
      contextLogger.current.warn('AGUI客户端未准备好');
      return;
    }

    const message = createCancelMessage(taskId);

    try {
      await sendMessage(message, taskId);
      contextLogger.current.info('事实核查任务取消请求已发送', { taskId });
    } catch (error) {
      contextLogger.current.error('取消事实核查失败', { error, taskId });
      throw error;
    }
  }, [agent, sendMessage]);

  return {
    connectionStatus,
    isRunning,
    startFactCheck,
    cancelFactCheck,
    agent
  };
}