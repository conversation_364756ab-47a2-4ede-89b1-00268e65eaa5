/**
 * 事实核查事件处理逻辑
 */
import { useCallback } from 'react';
import { AGUIEvent } from './useAGUI';
import { 
  FactCheckTask,
  Claim, 
  AgentStatus, 
  DebateRecord, 
  DebateMessage
} from '@/types/factCheck';

// 事实核查专用事件类型
export enum FactCheckEventType {
  TASK_STARTED = 'fact_check_task_started',
  CLAIM_EXTRACTED = 'fact_check_claim_extracted', 
  AGENT_PROGRESS = 'fact_check_agent_progress',
  VERIFICATION_COMPLETE = 'fact_check_verification_complete',
  DEBATE_STARTED = 'fact_check_debate_started',
  DEBATE_MESSAGE = 'fact_check_debate_message',
  DEBATE_CONCLUDED = 'fact_check_debate_concluded',
  TASK_COMPLETE = 'fact_check_task_complete',
  COST_UPDATE = 'fact_check_cost_update',
  ERROR = 'fact_check_error'
}

// Agent角色定义
export enum AgentRole {
  ORCHESTRATOR = 'orchestrator',
  CLAIM_EXTRACTOR = 'claim_extractor', 
  FINANCIAL_VERIFIER = 'financial_verifier',
  CORPORATE_VERIFIER = 'corporate_verifier',
  NEWS_VERIFIER = 'news_verifier',
  DEBATE_MODERATOR = 'debate_moderator',
  SYNTHESIS_AGENT = 'synthesis_agent'
}

// 声明验证状态
export enum VerificationStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  VERIFIED = 'verified',
  CONTRADICTED = 'contradicted', 
  CONFLICTED = 'conflicted',
  UNVERIFIED = 'unverified',
  ERROR = 'error'
}

// 事实核查自定义事件接口
export interface FactCheckCustomEvent extends AGUIEvent {
  type: FactCheckEventType;
  payload: {
    taskId: string;
    agentId?: string;
    agentRole?: AgentRole;
    status?: string;
    progress?: number;
    message?: string;
    data?: any;
    cost?: number;
    timestamp: number;
  };
}

interface UseFactCheckEventsOptions {
  onTaskStarted?: (task: FactCheckTask) => void;
  onClaimExtracted?: (claims: Claim[]) => void;
  onAgentProgress?: (agentStatus: AgentStatus) => void;
  onVerificationComplete?: (claim: Claim) => void;
  onDebateStarted?: (debate: DebateRecord) => void;
  onDebateMessage?: (claimId: string, message: DebateMessage) => void;
  onDebateConcluded?: (debate: DebateRecord) => void;
  onTaskComplete?: (task: FactCheckTask) => void;
  onCostUpdate?: (cost: number) => void;
  onError?: (error: string) => void;
}

export function useFactCheckEventHandler(options: UseFactCheckEventsOptions) {
  return useCallback((event: AGUIEvent) => {
    // 检查是否是事实核查相关事件
    if (event.custom && event.custom.type && 
        Object.values(FactCheckEventType).includes(event.custom.type)) {
      
      const factCheckEvent = event as FactCheckCustomEvent;
      
      console.debug('收到事实核查事件', {
        type: factCheckEvent.type,
        payload: factCheckEvent.payload
      });

      // 根据事件类型分发处理
      switch (factCheckEvent.type) {
        case FactCheckEventType.TASK_STARTED:
          options.onTaskStarted?.(factCheckEvent.payload.data as FactCheckTask);
          break;

        case FactCheckEventType.CLAIM_EXTRACTED:
          options.onClaimExtracted?.(factCheckEvent.payload.data as Claim[]);
          break;

        case FactCheckEventType.AGENT_PROGRESS:
          options.onAgentProgress?.(factCheckEvent.payload.data as AgentStatus);
          break;

        case FactCheckEventType.VERIFICATION_COMPLETE:
          options.onVerificationComplete?.(factCheckEvent.payload.data as Claim);
          break;

        case FactCheckEventType.DEBATE_STARTED:
          options.onDebateStarted?.(factCheckEvent.payload.data as DebateRecord);
          break;

        case FactCheckEventType.DEBATE_MESSAGE:
          options.onDebateMessage?.(
            factCheckEvent.payload.data.claimId,
            factCheckEvent.payload.data.message
          );
          break;

        case FactCheckEventType.DEBATE_CONCLUDED:
          options.onDebateConcluded?.(factCheckEvent.payload.data as DebateRecord);
          break;

        case FactCheckEventType.TASK_COMPLETE:
          options.onTaskComplete?.(factCheckEvent.payload.data as FactCheckTask);
          break;

        case FactCheckEventType.COST_UPDATE:
          options.onCostUpdate?.(factCheckEvent.payload.cost || 0);
          break;

        case FactCheckEventType.ERROR:
          options.onError?.(factCheckEvent.payload.message || 'Unknown error');
          break;

        default:
          console.warn('未处理的事实核查事件类型', {
            type: factCheckEvent.type
          });
      }
    }
  }, [options]);
}