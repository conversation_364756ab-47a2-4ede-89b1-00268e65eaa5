/** @type {import('next').NextConfig} */
const nextConfig = {
  // output: 'standalone' 适用于 Next.js 12+，并已成为 App Router 的标准配置
  output: 'standalone',

  // 配置 Next.js 直接编译我们的库，无需预构建
  transpilePackages: [
    '@yai-investor-insight/shared-fe-kit', 
    '@yai-investor-insight/demo-feature-fe',
    '@yai-investor-insight/api-client',
    '@yai-investor-insight/shared-types'
  ],



  // 不再需要 target: 'server'，因为 'standalone' 输出模式已包含此行为
  // target: 'server',

  // 生产环境 Source Maps，保持关闭以优化性能和安全
  productionBrowserSourceMaps: false,

  eslint: {
    // 允许在 CI/CD 中通过环境变量控制是否跳过 ESLint
    ignoreDuringBuilds: process.env.NEXT_ESLINT_SKIP === 'true',
  },

  typescript: {
    // 允许在 CI/CD 中通过环境变量控制是否跳过类型检查
    ignoreBuildErrors: process.env.NEXT_TYPESCRIPT_SKIP === 'true',
  },

  // 'serverComponentsExternalPackages' 已从 'experimental' 移至顶层
  // 用于告知 Next.js 不要将某些包打包到 Server Components 中
  serverExternalPackages: [
    // 如果有特定的包需要排除，可以在此添加
    // 'some-package', 
  ],

  // 'experimental' 字段中的旧配置已移除或成为稳定功能
  experimental: {
    // 保持服务端 source maps 开启，有助于在生产环境中调试错误
    serverSourceMaps: true,
  },

  webpack: (config, { isServer, dev }) => {
    // 开发模式下启用 source maps
    if (dev) {
      config.devtool = isServer ? 'eval-source-map' : 'eval-cheap-module-source-map';
    }
    
    // 解决 Node.js 模块在浏览器环境中的兼容性问题
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      };
    }
    return config;
  },
};

module.exports = nextConfig;
