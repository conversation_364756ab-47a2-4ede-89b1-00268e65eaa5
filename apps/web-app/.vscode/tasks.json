{"version": "2.0.0", "tasks": [{"label": "install-deps", "type": "shell", "command": "pnpm", "args": ["install"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "dev-server", "type": "shell", "command": "pnpm", "args": ["dev"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "isBackground": true}, {"label": "build", "type": "shell", "command": "pnpm", "args": ["build"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}}, {"label": "test", "type": "shell", "command": "pnpm", "args": ["test"], "options": {"cwd": "${workspaceFolder}"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}}, {"label": "lint", "type": "shell", "command": "pnpm", "args": ["lint"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}}]}