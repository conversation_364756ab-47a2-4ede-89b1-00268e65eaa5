{"version": "0.2.0", "configurations": [{"name": "🌐 Debug Next.js Dev", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "pnpm", "runtimeArgs": ["dev"], "port": 3000, "preLaunchTask": "install-deps", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}, {"name": "🔧 Debug Next.js Server", "type": "node", "request": "attach", "port": 9229, "localRoot": "${workspaceFolder}", "remoteRoot": "/app", "skipFiles": ["<node_internals>/**"]}, {"name": "🧪 Debug Jest Tests", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "pnpm", "runtimeArgs": ["test", "--watch"], "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}, {"name": "🏗️ Debug Build", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "pnpm", "runtimeArgs": ["build"], "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}]}