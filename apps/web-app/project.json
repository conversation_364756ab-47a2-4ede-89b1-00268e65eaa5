{"name": "web-app", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/web-app/src", "projectType": "application", "tags": ["type:app", "platform:web", "framework:next"], "targets": {"dev": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "web-app:build:development", "dev": true}, "configurations": {"development": {"buildTarget": "web-app:build:development", "dev": true}, "production": {"buildTarget": "web-app:build:production", "dev": false}}}, "build": {"executor": "@nx/next:build", "outputs": ["{projectRoot}/.next"], "defaultConfiguration": "production", "options": {}, "configurations": {"development": {"outputPath": "dist/apps/web-app"}, "production": {"outputPath": "dist/apps/web-app"}}}, "start": {"executor": "@nx/next:server", "defaultConfiguration": "production", "options": {"buildTarget": "web-app:build:production", "dev": false}, "configurations": {"development": {"buildTarget": "web-app:build:development", "dev": true}, "production": {"buildTarget": "web-app:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "web-app:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/web-app/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverage": true}}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "web-app:build", "staticFilePath": "dist/apps/web-app"}}, "type-check": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit", "cwd": "apps/web-app"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/web-app/**/*.{ts,tsx,js,jsx}"]}}}}