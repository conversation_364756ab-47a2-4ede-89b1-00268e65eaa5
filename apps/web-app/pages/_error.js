function Error({ statusCode }) {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h1>
        {statusCode
          ? `服务器发生了 ${statusCode} 错误`
          : '客户端发生了错误'}
      </h1>
      <p>抱歉，出现了一些问题。</p>
      <a href="/" style={{ color: '#0070f3', textDecoration: 'underline' }}>
        返回首页
      </a>
    </div>
  );
}

Error.getInitialProps = ({ res, err }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
  return { statusCode };
};

export default Error;