# 投资洞察 API 服务日志配置
# 基于 yai-loguru-sinks 的企业级日志配置

handlers:
  # 控制台输出 - 显示 INFO 及以上级别
  - sink: "sys.stdout"
    level: "INFO"
    format: "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
    colorize: true
    
  # 文件输出 - 记录所有级别日志
  - sink: "logs/investor-insight.log"
    level: "DEBUG"
    format: "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message} | {extra}"
    rotation: "10 MB"
    retention: "7 days"
    compression: "gz"
    
  # 阿里云 SLS 输出 - DEBUG 级别上报，启用 PackId 功能
  - sink: "sls://yai-log-test/app-log?region=cn-beijing&pack_id_enabled=true&pack_id_prefix=investor-insight"
    level: "DEBUG"
    format: "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message} | {extra}"
