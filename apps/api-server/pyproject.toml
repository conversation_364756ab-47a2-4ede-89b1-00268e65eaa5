[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "investor-insight"
version = "1.0.0"
description = "AI-powered investment research platform backend"
dependencies = [
    "yai-nexus-agentkit[all]>=0.2.6",
    "fastapi>=0.116.1",
    "uvicorn[standard]>=0.35.0",
    "pydantic>=2.11.7",
    "langchain==0.3.26",
    "langchain-openai>=0.3.28",
    "langchain-anthropic>=0.3.17",
    "langgraph>=0.5.3",
    "langfuse~=3.2.1",
    "ag-ui-protocol>=0.1.0",
    "yai-loguru-sinks>=0.6.2",
    "structlog>=25.4.0",
    "opentelemetry-api>=1.35.0",
    "opentelemetry-sdk>=1.35.0",
    "python-dotenv>=1.0.0",
    "dependency-injector>=4.42.1",
    "pydantic-settings>=2.3.0",
    "passlib>=1.7.4",
    "bcrypt>=4.1.0",
    "PyJWT>=2.10.1",
    "yfinance>=0.2.0",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "httpx>=0.25.0",
    "openai>=1.0.0",
    "anthropic>=0.30.0",
    "demo-feature-bs>=0.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0",
    "ruff>=0.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.5.0"
]

[tool.ruff]
line-length = 88
target-version = "py311"
select = ["E", "F", "UP"]
fixable = ["ALL"]

[tool.mypy]
python_version = "3.11"
strict = true
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
asyncio_mode = "auto"

[tool.uv.sources]
demo-feature-bs = { path = "../../libs/demo-feature-bs", editable = true }
