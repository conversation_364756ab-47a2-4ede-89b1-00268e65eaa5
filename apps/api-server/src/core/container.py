"""依赖注入容器 - 基于dependency-injector的专业IoC容器"""
from dependency_injector import containers, providers
from dependency_injector.wiring import Provide, inject
from typing import AsyncGenerator
import structlog

from .settings import settings


class ApplicationContainer(containers.DeclarativeContainer):
    """主应用容器"""
    
    # 配置
    config = providers.Configuration()
    
    # 日志
    logger = providers.Singleton(
        structlog.get_logger,
        "main"
    )
    
    # 基础设施层 - 监控
    monitoring_service = providers.Factory(
        lambda: None  # 占位符，后续会实现具体的监控服务
    )
    
    # 基础设施层 - 外部服务
    llm_client = providers.Factory(
        lambda: None  # 占位符，后续会实现具体的LLM客户端
    )
    
    # 基础设施层 - 工具
    financials_tool = providers.Factory(
        lambda: None  # 占位符，后续会注册具体工具
    )
    
    news_search_tool = providers.Factory(
        lambda: None  # 占位符，后续会注册具体工具
    )
    
    research_tool = providers.Factory(
        lambda: None  # 占位符，后续会注册具体工具
    )


class InfrastructureContainer(containers.DeclarativeContainer):
    """基础设施容器"""
    
    # 配置提供者
    config = providers.Configuration()
    
    # 日志服务
    structlog_logger = providers.Singleton(
        structlog.get_logger
    )
    
    # LLM服务（占位符）
    openai_client = providers.Factory(
        lambda: None  # 后续实现
    )
    
    # 数据库连接（占位符）
    database_connection = providers.Factory(
        lambda: None  # 后续实现  
    )
    
    # 监控服务
    tracer = providers.Factory(
        lambda: None  # 后续实现
    )
    
    metrics_collector = providers.Factory(
        lambda: None  # 后续实现
    )


class DomainContainer(containers.DeclarativeContainer):
    """领域容器"""
    
    # 配置
    config = providers.Configuration()
    
    # 领域服务（占位符）
    research_domain_service = providers.Factory(
        lambda: None  # 后续实现
    )
    
    chat_domain_service = providers.Factory(
        lambda: None  # 后续实现
    )


class ApplicationServicesContainer(containers.DeclarativeContainer):
    """应用服务容器"""
    
    # 配置
    config = providers.Configuration()
    
    # 依赖的其他容器
    infrastructure = providers.DependenciesContainer()
    domain = providers.DependenciesContainer()
    
    # 应用服务（占位符）
    chat_use_case = providers.Factory(
        lambda: None  # 后续实现
    )
    
    research_use_case = providers.Factory(
        lambda: None  # 后续实现
    )


class APIContainer(containers.DeclarativeContainer):
    """API容器"""
    
    # 配置
    config = providers.Configuration()
    
    # 依赖的其他容器
    application = providers.DependenciesContainer()
    
    # API服务
    chat_handler = providers.Factory(
        lambda: None  # 后续实现
    )
    
    health_handler = providers.Factory(
        lambda: None  # 后续实现
    )


class MainContainer(containers.DeclarativeContainer):
    """主容器 - 组装所有子容器"""
    
    # 配置提供者
    config = providers.Configuration()
    
    # 子容器
    infrastructure = providers.Container(
        InfrastructureContainer,
        config=config.infrastructure
    )
    
    domain = providers.Container(
        DomainContainer,
        config=config.domain
    )
    
    application = providers.Container(
        ApplicationServicesContainer,
        config=config.application,
        infrastructure=infrastructure,
        domain=domain
    )
    
    api = providers.Container(
        APIContainer,
        config=config.api,
        application=application
    )
    
    # 主应用服务
    app = providers.Factory(
        lambda: None  # 后续会注册FastAPI应用实例
    )


class ContainerManager:
    """容器管理器 - 提供容器生命周期管理"""
    
    def __init__(self):
        self._container: MainContainer = None
        self._is_initialized = False
    
    def initialize(self) -> MainContainer:
        """初始化容器"""
        if self._is_initialized:
            return self._container
            
        self._container = MainContainer()
        
        # 加载配置
        self._container.config.from_dict({
            "infrastructure": {
                "database": settings.database.model_dump(),
                "llm": settings.llm.model_dump(),
                "monitoring": settings.monitoring.model_dump()
            },
            "domain": {},
            "application": {},
            "api": {
                "security": settings.security.model_dump()
            }
        })
        
        # 线程配置 - 用于FastAPI依赖注入
        self._container.wire(modules=[
            "src.api.v1.endpoints.chat",
            "src.api.v1.endpoints.research", 
            "src.api.v1.endpoints.health",
            "src.api.v1.deps",
            "src.application.services",
            "src.application.use_cases"
        ])
        
        self._is_initialized = True
        return self._container
    
    def get_container(self) -> MainContainer:
        """获取容器实例"""
        if not self._is_initialized:
            return self.initialize()
        return self._container
    
    def shutdown(self) -> None:
        """关闭容器"""
        if self._container:
            self._container.unwire()
        self._is_initialized = False


# 全局容器管理器
container_manager = ContainerManager()


# 便捷的依赖注入装饰器
def inject_dependencies(func):
    """依赖注入装饰器"""
    return inject(func)


# 便捷的获取容器方法
def get_container() -> MainContainer:
    """获取主容器"""
    return container_manager.get_container()


# 便捷的获取服务方法
def get_service(service_name: str):
    """根据服务名获取服务实例"""
    container = get_container()
    
    # 通过点记号导航到具体服务
    service_path = service_name.split(".")
    current = container
    
    for path_segment in service_path:
        current = getattr(current, path_segment)
    
    return current()