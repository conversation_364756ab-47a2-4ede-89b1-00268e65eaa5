"""统一异常体系 - 分层异常定义"""
from typing import Any, Dict, Optional
from enum import Enum


class ErrorCode(str, Enum):
    """错误代码枚举"""
    # 通用错误
    INTERNAL_ERROR = "INTERNAL_ERROR"
    INVALID_INPUT = "INVALID_INPUT"
    NOT_FOUND = "NOT_FOUND"
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    
    # 业务错误
    BUSINESS_RULE_VIOLATION = "BUSINESS_RULE_VIOLATION"
    INVALID_OPERATION = "INVALID_OPERATION"
    RESOURCE_CONFLICT = "RESOURCE_CONFLICT"
    
    # 外部服务错误
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    LLM_SERVICE_ERROR = "LLM_SERVICE_ERROR"
    DATABASE_ERROR = "DATABASE_ERROR"
    
    # 应用层错误
    VALIDATION_ERROR = "VALIDATION_ERROR"
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR"


class BaseAppException(Exception):
    """应用基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.INTERNAL_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error": self.error_code.value,
            "message": self.message,
            "details": self.details
        }


# 领域层异常
class DomainException(BaseAppException):
    """领域层异常基类"""
    pass


class EntityNotFoundError(DomainException):
    """实体未找到异常"""
    
    def __init__(self, entity_name: str, entity_id: Any):
        super().__init__(
            message=f"{entity_name} with id '{entity_id}' not found",
            error_code=ErrorCode.NOT_FOUND,
            details={"entity": entity_name, "id": str(entity_id)}
        )


class BusinessRuleViolationError(DomainException):
    """业务规则违反异常"""
    
    def __init__(self, rule: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Business rule violation: {rule}",
            error_code=ErrorCode.BUSINESS_RULE_VIOLATION,
            details=details
        )


class InvalidOperationError(DomainException):
    """无效操作异常"""
    
    def __init__(self, operation: str, reason: str):
        super().__init__(
            message=f"Invalid operation '{operation}': {reason}",
            error_code=ErrorCode.INVALID_OPERATION,
            details={"operation": operation, "reason": reason}
        )


# 应用层异常
class ApplicationException(BaseAppException):
    """应用层异常基类"""
    pass


class ValidationError(ApplicationException):
    """验证错误异常"""
    
    def __init__(self, field: str, value: Any, reason: str):
        super().__init__(
            message=f"Validation error for field '{field}': {reason}",
            error_code=ErrorCode.VALIDATION_ERROR,
            details={"field": field, "value": str(value), "reason": reason}
        )


class ConfigurationError(ApplicationException):
    """配置错误异常"""
    
    def __init__(self, config_name: str, reason: str):
        super().__init__(
            message=f"Configuration error for '{config_name}': {reason}",
            error_code=ErrorCode.CONFIGURATION_ERROR,
            details={"config": config_name, "reason": reason}
        )


# 基础设施层异常
class InfrastructureException(BaseAppException):
    """基础设施层异常基类"""
    pass


class ExternalServiceError(InfrastructureException):
    """外部服务错误异常"""
    
    def __init__(self, service: str, operation: str, reason: str):
        super().__init__(
            message=f"External service '{service}' error during '{operation}': {reason}",
            error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
            details={"service": service, "operation": operation, "reason": reason}
        )


class LLMServiceError(InfrastructureException):
    """LLM服务错误异常"""
    
    def __init__(self, model: str, operation: str, reason: str):
        super().__init__(
            message=f"LLM service error with model '{model}' during '{operation}': {reason}",
            error_code=ErrorCode.LLM_SERVICE_ERROR,
            details={"model": model, "operation": operation, "reason": reason}
        )


class DatabaseError(InfrastructureException):
    """数据库错误异常"""
    
    def __init__(self, operation: str, reason: str):
        super().__init__(
            message=f"Database error during '{operation}': {reason}",
            error_code=ErrorCode.DATABASE_ERROR,
            details={"operation": operation, "reason": reason}
        )


# API层异常
class APIException(BaseAppException):
    """API层异常基类"""
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        error_code: ErrorCode = ErrorCode.INTERNAL_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code, details)
        self.status_code = status_code


class UnauthorizedError(APIException):
    """未授权异常"""
    
    def __init__(self, reason: str = "Authentication required"):
        super().__init__(
            message=f"Unauthorized: {reason}",
            status_code=401,
            error_code=ErrorCode.UNAUTHORIZED,
            details={"reason": reason}
        )


class ForbiddenError(APIException):
    """禁止访问异常"""
    
    def __init__(self, reason: str = "Access forbidden"):
        super().__init__(
            message=f"Forbidden: {reason}",
            status_code=403,
            error_code=ErrorCode.FORBIDDEN,
            details={"reason": reason}
        )


class NotFoundError(APIException):
    """资源未找到异常"""
    
    def __init__(self, resource: str):
        super().__init__(
            message=f"Resource not found: {resource}",
            status_code=404,
            error_code=ErrorCode.NOT_FOUND,
            details={"resource": resource}
        )


class BadRequestError(APIException):
    """错误请求异常"""
    
    def __init__(self, reason: str):
        super().__init__(
            message=f"Bad request: {reason}",
            status_code=400,
            error_code=ErrorCode.INVALID_INPUT,
            details={"reason": reason}
        )