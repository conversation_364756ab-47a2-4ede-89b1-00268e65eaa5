"""应用配置管理 - 基于Pydantic Settings的统一配置"""
from typing import Optional, List
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
import os


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    host: str = Field(default="localhost", description="数据库主机")
    port: int = Field(default=5432, description="数据库端口")
    name: str = Field(default="investor_insight", description="数据库名称")
    user: str = Field(default="postgres", description="数据库用户")
    password: str = Field(default="", description="数据库密码")
    pool_size: int = Field(default=10, description="连接池大小")
    
    @property
    def url(self) -> str:
        """获取数据库连接URL"""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


class LLMSettings(BaseSettings):
    """LLM服务配置"""
    openai_api_key: str = Field(default="", description="OpenAI API密钥")
    openai_base_url: str = Field(default="https://api.openai.com/v1", description="OpenAI基础URL")
    model: str = Field(default="gpt-4", description="默认模型")
    max_tokens: int = Field(default=4096, description="最大令牌数")
    temperature: float = Field(default=0.7, description="温度参数")
    timeout: int = Field(default=60, description="请求超时时间(秒)")


class MonitoringSettings(BaseSettings):
    """监控配置"""
    log_level: str = Field(default="INFO", description="日志级别")
    enable_tracing: bool = Field(default=True, description="启用链路追踪")
    jaeger_endpoint: Optional[str] = Field(default=None, description="Jaeger端点")
    metrics_enabled: bool = Field(default=True, description="启用指标收集")


class SecuritySettings(BaseSettings):
    """安全配置"""
    secret_key: str = Field(default="your-secret-key-change-this", description="应用密钥")
    algorithm: str = Field(default="HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间(分钟)")
    allowed_hosts: List[str] = Field(default=["*"], description="允许的主机列表")
    cors_origins: List[str] = Field(default=["*"], description="CORS允许的源")


class AppSettings(BaseSettings):
    """主应用配置"""
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        env_nested_delimiter="__",
        extra="ignore"  # 忽略额外的字段，避免日志相关环境变量导致的验证错误
    )
    
    # 基础配置
    app_name: str = Field(default="Investor Insight API", description="应用名称")
    version: str = Field(default="1.0.0", description="应用版本")
    environment: str = Field(default="development", description="运行环境")
    debug: bool = Field(default=True, description="调试模式")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8000, description="服务器端口")
    reload: bool = Field(default=True, description="自动重载")
    
    # 嵌套配置
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    llm: LLMSettings = Field(default_factory=LLMSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    
    @property
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return self.environment.lower() == "production"
    
    @property
    def is_development(self) -> bool:
        """判断是否为开发环境"""
        return self.environment.lower() == "development"


# 全局配置实例
settings = AppSettings()