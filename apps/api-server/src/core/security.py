"""安全配置和工具类"""
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from passlib.context import CryptContext
import jwt
from .settings import settings
from .exceptions import UnauthorizedError


class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.secret_key = settings.security.secret_key
        self.algorithm = settings.security.algorithm
        self.access_token_expire_minutes = settings.security.access_token_expire_minutes
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """生成密码哈希"""
        return self.pwd_context.hash(password)
    
    def create_access_token(
        self, 
        data: Dict[str, Any], 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """验证访问令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise UnauthorizedError("Token has expired")
        except jwt.JWTError:
            raise UnauthorizedError("Invalid token")
    
    def extract_user_from_token(self, token: str) -> Optional[str]:
        """从令牌中提取用户信息"""
        try:
            payload = self.verify_token(token)
            user_id: str = payload.get("sub")
            return user_id
        except UnauthorizedError:
            return None


class APIKeyManager:
    """API密钥管理器"""
    
    def __init__(self):
        self.valid_api_keys = set()  # 在实际应用中应该从数据库或配置文件读取
    
    def is_valid_api_key(self, api_key: str) -> bool:
        """验证API密钥"""
        return api_key in self.valid_api_keys
    
    def add_api_key(self, api_key: str) -> None:
        """添加API密钥"""
        self.valid_api_keys.add(api_key)
    
    def revoke_api_key(self, api_key: str) -> None:
        """撤销API密钥"""
        self.valid_api_keys.discard(api_key)


class RateLimiter:
    """速率限制器"""
    
    def __init__(self):
        self.requests = {}  # 在实际应用中应该使用Redis等外部存储
    
    def is_allowed(
        self, 
        identifier: str, 
        max_requests: int = 100, 
        window_minutes: int = 60
    ) -> bool:
        """检查是否允许请求"""
        now = datetime.utcnow()
        window_start = now - timedelta(minutes=window_minutes)
        
        if identifier not in self.requests:
            self.requests[identifier] = []
        
        # 移除过期的请求记录
        self.requests[identifier] = [
            req_time for req_time in self.requests[identifier] 
            if req_time > window_start
        ]
        
        # 检查是否超过限制
        if len(self.requests[identifier]) >= max_requests:
            return False
        
        # 记录当前请求
        self.requests[identifier].append(now)
        return True


# 全局安全管理器实例
security_manager = SecurityManager()
api_key_manager = APIKeyManager()
rate_limiter = RateLimiter()