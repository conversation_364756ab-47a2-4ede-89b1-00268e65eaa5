from __future__ import annotations

from dataclasses import dataclass, field
from typing import TypedDict, List, Dict, Any

from langgraph.graph import add_messages
from typing_extensions import Annotated

import operator


class InvestmentAnalysisState(TypedDict):
    """事件驱动投资分析状态"""
    messages: Annotated[list, add_messages]
    profile: str  # 投资标的基本信息
    
    # 节点1: 广度问题构建
    fact_check_questions: Annotated[list, operator.add]
    
    # 节点2: 预测数据需求构建  
    prediction_questions: Annotated[list, operator.add]
    
    # 节点3: 量化筛选因子数据需求
    quantitative_questions: Annotated[list, operator.add]
    
    # 循环分析结果
    macro_analysis_results: Annotated[list, operator.add]  # 宏观与行业分析
    industry_chain_results: Annotated[list, operator.add]  # 产业链分析
    quantitative_results: Annotated[list, operator.add]    # 量化投资策略分析
    final_synthesis_results: Annotated[list, operator.add] # 最终综合分析
    
    # 循环控制
    macro_loop_count: int
    industry_loop_count: int 
    quantitative_loop_count: int
    final_loop_count: int
    
    # 配置参数
    max_loops: int
    fmp_api_key: str


class LoopAnalysisState(TypedDict):
    """循环分析状态"""
    query: str
    analysis_type: str  # macro, industry, quantitative, final
    loop_id: str
    search_results: Annotated[list, operator.add]
    processed_results: str


class QuestionGenerationState(TypedDict):
    """问题生成状态"""
    questions: List[str]
    question_type: str  # fact_check, prediction, quantitative


class FinalInvestmentThesis(TypedDict):
    """最终投资论点"""
    investment_recommendation: str  # 买入/卖出/观望
    core_thesis: str  # 核心论点
    key_pillars: List[str]  # 关键支柱
    main_risks: List[str]  # 主要风险
    valuation_prediction: Dict[str, Any]  # 估值预测
    detailed_analysis: str  # 详细分析
    critical_questions: List[str]  # 待评审的关键问题


@dataclass(kw_only=True)
class InvestmentAnalysisOutput:
    """投资分析输出"""
    final_thesis: FinalInvestmentThesis = field(default=None)
    analysis_summary: str = field(default=None)