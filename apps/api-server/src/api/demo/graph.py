"""
事件驱动投资分析图构建器
"""

from dotenv import load_dotenv
from langgraph.graph import StateGraph
from langgraph.graph import START, END

from .state import InvestmentAnalysisState
from .configuration import VestConfiguration
from .nodes import (
    NodeNames,
    generate_fact_questions,
    generate_prediction_questions,
    generate_quantitative_questions,
    macro_analysis_loop,
    industry_analysis_loop,
    quantitative_analysis_loop,
    finalize_investment_thesis,
    should_continue_macro_loop,
    should_continue_industry_loop,
    should_continue_quantitative_loop,
)

load_dotenv()


# 图配置常量
class GraphConfig:
    """图配置常量"""
    GRAPH_NAME = "event-driven-investment-analysis"


# 创建事件驱动投资分析图
builder = StateGraph(InvestmentAnalysisState, config_schema=VestConfiguration)

# 定义节点
builder.add_node(NodeNames.GENERATE_FACT_QUESTIONS, generate_fact_questions)
builder.add_node(NodeNames.MACRO_ANALYSIS_LOOP, macro_analysis_loop)
builder.add_node(NodeNames.GENERATE_PREDICTION_QUESTIONS, generate_prediction_questions)
builder.add_node(NodeNames.INDUSTRY_ANALYSIS_LOOP, industry_analysis_loop)
builder.add_node(NodeNames.GENERATE_QUANTITATIVE_QUESTIONS, generate_quantitative_questions)
builder.add_node(NodeNames.QUANTITATIVE_ANALYSIS_LOOP, quantitative_analysis_loop)
builder.add_node(NodeNames.FINALIZE_INVESTMENT_THESIS, finalize_investment_thesis)

# 设置入口点
builder.add_edge(START, NodeNames.GENERATE_FACT_QUESTIONS)

# 添加边
builder.add_edge(NodeNames.GENERATE_FACT_QUESTIONS, NodeNames.MACRO_ANALYSIS_LOOP)

# 宏观分析循环
builder.add_conditional_edges(
    NodeNames.MACRO_ANALYSIS_LOOP,
    should_continue_macro_loop,
    [NodeNames.MACRO_ANALYSIS_LOOP, NodeNames.GENERATE_PREDICTION_QUESTIONS]
)

builder.add_edge(NodeNames.GENERATE_PREDICTION_QUESTIONS, NodeNames.INDUSTRY_ANALYSIS_LOOP)

# 产业链分析循环
builder.add_conditional_edges(
    NodeNames.INDUSTRY_ANALYSIS_LOOP,
    should_continue_industry_loop,
    [NodeNames.INDUSTRY_ANALYSIS_LOOP, NodeNames.GENERATE_QUANTITATIVE_QUESTIONS]
)

builder.add_edge(NodeNames.GENERATE_QUANTITATIVE_QUESTIONS, NodeNames.QUANTITATIVE_ANALYSIS_LOOP)

# 量化分析循环
builder.add_conditional_edges(
    NodeNames.QUANTITATIVE_ANALYSIS_LOOP,
    should_continue_quantitative_loop,
    [NodeNames.QUANTITATIVE_ANALYSIS_LOOP, NodeNames.FINALIZE_INVESTMENT_THESIS]
)

# 最终综合
builder.add_edge(NodeNames.FINALIZE_INVESTMENT_THESIS, END)

graph = builder.compile(name=GraphConfig.GRAPH_NAME)