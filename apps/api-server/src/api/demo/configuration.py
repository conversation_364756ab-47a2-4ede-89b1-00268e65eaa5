import os
from pydantic import BaseModel, Field
from typing import Any, Optional

from langchain_core.runnables import RunnableConfig
from shared_bs_llm.llm import LLMClientEnum


class VestConfiguration(BaseModel):
    """事件驱动投资分析配置"""

    # LLM模型配置
    analysis_model: LLMClientEnum = Field(
        default=LLMClientEnum.CLAUDE_SONNET_4,
        metadata={
            "description": "用于投资分析的LLM模型"
        },
    )

    question_generation_model: LLMClientEnum = Field(
        default=LLMClientEnum.CLAUDE_SONNET_4,
        metadata={
            "description": "用于问题生成的LLM模型"
        },
    )

    synthesis_model: LLMClientEnum = Field(
        default=LLMClientEnum.CLAUDE_SONNET_4,
        metadata={
            "description": "用于最终综合分析的LLM模型"
        },
    )

    # 搜索配置
    search_engine: str = Field(
        default="search_pro",
        metadata={"description": "搜索引擎类型"}
    )

    search_count: int = Field(
        default=15,
        metadata={"description": "搜索结果数量"}
    )

    search_recency_filter: str = Field(
        default="month",
        metadata={"description": "搜索时间过滤"}
    )

    search_content_size: str = Field(
        default="high",
        metadata={"description": "搜索内容大小"}
    )

    # 循环控制配置 todo 把循环应该设置为3
    max_macro_loops: int = Field(
        default=1,
        metadata={"description": "宏观分析最大循环次数"}
    )

    max_industry_loops: int = Field(
        default=1,
        metadata={"description": "产业链分析最大循环次数"}
    )

    max_quantitative_loops: int = Field(
        default=1,
        metadata={"description": "量化分析最大循环次数"}
    )

    max_final_loops: int = Field(
        default=2,
        metadata={"description": "最终综合分析最大循环次数"}
    )

    # API配置
    fmp_api_key: str = Field(
        default="",
        metadata={"description": "Financial Modeling Prep API密钥"}
    )

    # 模型参数
    analysis_temperature: float = Field(
        default=0.7,
        metadata={"description": "分析模型温度"}
    )

    synthesis_temperature: float = Field(
        default=0.8,
        metadata={"description": "综合分析模型温度"}
    )

    # 调试配置
    enable_debug: bool = Field(
        default=True,
        metadata={"description": "启用调试输出"}
    )

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "VestConfiguration":
        """从RunnableConfig创建配置实例"""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )

        # 从环境变量或配置中获取原始值
        raw_values: dict[str, Any] = {
            name: os.environ.get(name.upper(), configurable.get(name))
            for name in cls.model_fields.keys()
        }

        # 过滤None值并转换模型字段
        values = {}
        for k, v in raw_values.items():
            if v is not None:
                if k.endswith('_model') and isinstance(v, str):
                    try:
                        values[k] = LLMClientEnum(v)
                    except ValueError:
                        continue
                else:
                    values[k] = v

        return cls(**values)