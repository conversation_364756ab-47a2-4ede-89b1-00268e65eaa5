from openai import OpenAI
from os import getenv
from dotenv import load_dotenv
import json

# 加载环境变量
load_dotenv()

def create_openai_client():
    """创建OpenAI客户端"""
    api_key = getenv("OPENROUTER_API_KEY")
    if not api_key:
        raise ValueError("OPENROUTER_API_KEY环境变量未设置")
    
    client = OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=api_key,
    )
    return client

def text_completion_demo(client):
    """文本对话demo"""
    print("=== 文本对话Demo ===")
    
    try:
        completion = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": getenv("YOUR_SITE_URL", "https://localhost:3000"),
                "X-Title": getenv("YOUR_SITE_NAME", "AI Demo"),
            },
            model="qwen/qwen3-coder",
            messages=[
                {
                    "role": "user",
                    "content": "请简要介绍一下人工智能在金融投资领域的应用"
                }
            ],
            temperature=0.7,
            max_tokens=1000
        )
        
        print(f"回答: {completion.choices[0].message.content}")
        print(f"使用的模型: {completion.model}")
        print(f"Token使用情况: {completion.usage}")
        
    except Exception as e:
        print(f"文本对话错误: {str(e)}")

def image_analysis_demo(client):
    """图像分析demo"""
    print("\n=== 图像分析Demo ===")
    
    try:
        completion = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": getenv("YOUR_SITE_URL", "https://localhost:3000"),
                "X-Title": getenv("YOUR_SITE_NAME", "AI Demo"),
            },
            model="openai/gpt-4-vision-preview",  # 使用支持图像的模型
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请描述这张图片中的内容，包括主要元素和场景"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
                            }
                        }
                    ]
                }
            ],
            max_tokens=500
        )
        
        print(f"图像分析结果: {completion.choices[0].message.content}")
        
    except Exception as e:
        print(f"图像分析错误: {str(e)}")
        print("提示: 图像分析功能需要支持视觉的模型，如gpt-4-vision-preview")

def streaming_demo(client):
    """流式响应demo"""
    print("\n=== 流式响应Demo ===")
    
    try:
        stream = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": getenv("YOUR_SITE_URL", "https://localhost:3000"),
                "X-Title": getenv("YOUR_SITE_NAME", "AI Demo"),
            },
            model="anthropic/claude-3-sonnet",
            messages=[
                {
                    "role": "user",
                    "content": "请解释什么是区块链技术，用简单易懂的语言"
                }
            ],
            stream=True,
            max_tokens=300
        )
        
        print("流式回答: ", end="")
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                print(chunk.choices[0].delta.content, end="")
        print("\n")
        
    except Exception as e:
        print(f"流式响应错误: {str(e)}")

def check_available_models(client):
    """检查可用模型"""
    print("\n=== 检查可用模型 ===")
    
    try:
        models = client.models.list()
        print("可用模型数量:", len(models.data))
        
        # 显示前10个模型
        print("\n前10个可用模型:")
        for i, model in enumerate(models.data[:10]):
            print(f"{i+1}. {model.id}")
            
    except Exception as e:
        print(f"获取模型列表错误: {str(e)}")

def main():
    """主函数"""
    print("OpenAI客户端Demo - OpenRouter集成")
    print("=" * 50)
    
    try:
        # 创建客户端
        client = create_openai_client()
        print("✅ OpenAI客户端创建成功")
        
        # 运行各种demo
        text_completion_demo(client)
        # image_analysis_demo(client)
        # streaming_demo(client)
        # check_available_models(client)
        
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        print("\n请确保在.env文件中设置了以下环境变量:")
        print("OPENROUTER_API_KEY=your_api_key_here")
        print("YOUR_SITE_URL=https://your-site.com (可选)")
        print("YOUR_SITE_NAME=Your Site Name (可选)")
        
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        print("\n常见问题解决方案:")
        print("1. 检查API密钥是否正确")
        print("2. 确认OpenRouter账户有足够余额")
        print("3. 验证网络连接")
        print("4. 检查所选模型是否可用")

if __name__ == "__main__":
    main()