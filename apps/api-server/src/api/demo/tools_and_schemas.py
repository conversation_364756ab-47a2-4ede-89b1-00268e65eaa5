from typing import List, Dict, Any
from pydantic import BaseModel, Field


class QuestionList(BaseModel):
    """问题列表"""
    questions: List[str] = Field(
        description="生成的问题列表，每个问题都应该是具体、可操作的"
    )
    rationale: str = Field(
        description="生成这些问题的理由和逻辑"
    )


class AnalysisResult(BaseModel):
    """分析结果"""
    analysis_content: str = Field(
        description="详细的分析内容"
    )
    key_findings: List[str] = Field(
        description="关键发现列表"
    )
    data_sources: List[str] = Field(
        description="数据来源列表"
    )
    confidence_level: str = Field(
        description="分析结果的置信度：高/中/低"
    )


class InvestmentThesis(BaseModel):
    """投资论点"""
    investment_recommendation: str = Field(
        description="投资建议：买入/卖出/观望"
    )
    core_thesis: str = Field(
        description="核心投资论点，不超过200字"
    )
    key_pillars: List[str] = Field(
        description="支撑论点的3-5个关键支柱"
    )
    main_risks: List[str] = Field(
        description="3-5个主要风险因素"
    )
    target_price_range: str = Field(
        description="目标价格区间"
    )
    expected_return: str = Field(
        description="预期回报率"
    )
    investment_timeframe: str = Field(
        description="投资时间框架"
    )
    catalysts: List[str] = Field(
        description="可能的催化剂事件"
    )
    critical_questions: List[str] = Field(
        description="待最终评审的2-3个核心问题"
    )


class LoopContinuation(BaseModel):
    """循环继续判断"""
    should_continue: bool = Field(
        description="是否应该继续循环分析"
    )
    reason: str = Field(
        description="继续或停止的原因"
    )
    next_focus_areas: List[str] = Field(
        description="如果继续，下一轮应该关注的领域"
    )