"""
事件驱动投资分析图的节点函数
"""

import os
from datetime import datetime  # 添加这行
from typing import List, Dict, Any
from langchain_core.messages import AIMessage, HumanMessage
from langgraph.types import Send
from langchain_core.runnables import RunnableConfig
from langchain_core.output_parsers import Pyd<PERSON>cOutputParser
from .llm_client_utils import LLMClientUtils

from .tools_and_schemas import QuestionList, AnalysisResult, InvestmentThesis, LoopContinuation
from .state import (
    InvestmentAnalysisState,
    QuestionGenerationState,
    LoopAnalysisState,
)
from .configuration import VestConfiguration
from .search import VestSearch
from .prompts import (
    FACT_CHECK_PROMPT,
    PREDICTION_PROMPT,
    QUANTITATIVE_PROMPT,
    MACRO_ANALYSIS_PROMPT,
    INDUSTRY_ANALYSIS_PROMPT,
    QUANTITATIVE_ANALYSIS_PROMPT,
    FINAL_SYNTHESIS_PROMPT,
    LOOP_EVALUATION_PROMPT,
)


# 在文件顶部添加导入
from ..core.events import EventEmitter


# 节点名称常量
class NodeNames:
    """图节点名称常量"""
    GENERATE_FACT_QUESTIONS = "generate_fact_questions"
    GENERATE_PREDICTION_QUESTIONS = "generate_prediction_questions"
    GENERATE_QUANTITATIVE_QUESTIONS = "generate_quantitative_questions"
    MACRO_ANALYSIS_LOOP = "macro_analysis_loop"
    INDUSTRY_ANALYSIS_LOOP = "industry_analysis_loop"
    QUANTITATIVE_ANALYSIS_LOOP = "quantitative_analysis_loop"
    FINAL_SYNTHESIS_LOOP = "final_synthesis_loop"
    FINALIZE_INVESTMENT_THESIS = "finalize_investment_thesis"


# 问题生成节点
async def generate_fact_questions(
    state: InvestmentAnalysisState, config: RunnableConfig
):
    """生成事实核查问题"""
    configurable = VestConfiguration.from_runnable_config(config)
    
    # 发送步骤开始事件
    yield EventEmitter.create_step_started_event(
        step_name="生成事实核查问题",
        description="正在生成投资分析所需的事实核查问题..."
    )
    
    # 创建PydanticOutputParser
    parser = PydanticOutputParser(pydantic_object=QuestionList)
    
    llm = LLMClientUtils.create_llm(
        model_type=configurable.question_generation_model,
        temperature=0.7,
        streaming=False
    )
    
    # 在prompt中添加格式说明
    formatted_prompt = FACT_CHECK_PROMPT.format(
        profile=state["profile"]
    ) + "\n\n" + parser.get_format_instructions()
    
    try:
        # 调用LLM获取响应
        response = await llm.ainvoke(formatted_prompt)
        
        # 使用parser解析响应
        result = parser.parse(response.content)
        
        # 发送问题卡片数据事件
        yield {
            "type": "CustomEvent",
            "data": {
                "type": "QUESTIONS_GENERATED",
                "step_name": "生成事实核查问题",
                "questions": [
                    {
                        "id": f"fact_q_{i+1}",
                        "title": f"事实核查问题 {i+1}",
                        "content": question,
                        "type": "fact_check",
                        "status": "generated",
                        "category": "事实核查",
                        "icon": "🔍"
                    }
                    for i, question in enumerate(result.questions)
                ],
                "total_count": len(result.questions),
                "category": "事实核查问题"
            }
        }
        
        # 发送步骤完成事件
        yield EventEmitter.create_step_finished_event(
            step_name="生成事实核查问题",
            description=f"成功生成 {len(result.questions)} 个事实核查问题",
            result_summary=f"生成了 {len(result.questions)} 个问题"
        )
        
        # 返回结果
        yield {
            "fact_check_questions": result.questions,
        }
        
    except Exception as e:
        # 发送步骤失败事件
        yield EventEmitter.create_step_finished_event(
            step_name="生成事实核查问题",
            description="事实核查问题生成失败",
            error=str(e)
        )
        if configurable.enable_debug:
            print(f"事实核查问题生成异常: {str(e)}")
        raise


async def generate_prediction_questions(
    state: InvestmentAnalysisState, config: RunnableConfig
):
    """生成预测数据需求问题"""
    configurable = VestConfiguration.from_runnable_config(config)
    
    # 发送步骤开始事件
    yield EventEmitter.create_step_started_event(
        step_name="生成预测性问题",
        description="正在生成投资预测相关的问题..."
    )
    
    # 创建PydanticOutputParser
    parser = PydanticOutputParser(pydantic_object=QuestionList)
    
    llm = LLMClientUtils.create_llm(
        model_type=configurable.question_generation_model,
        temperature=0.7,
        streaming=False
    )
    
    # 使用宏观分析结果作为输入
    macro_summary = "\n".join(state.get("macro_analysis_results", []))
    
    # 在prompt中添加格式说明
    formatted_prompt = PREDICTION_PROMPT.format(
        macro_analysis=macro_summary or "暂无宏观分析结果"
    ) + "\n\n" + parser.get_format_instructions()
    
    try:
        # 调用LLM获取响应
        response = await llm.ainvoke(formatted_prompt)
        
        # 使用parser解析响应
        result = parser.parse(response.content)
        
        # 发送问题卡片数据事件
        yield {
            "type": "CustomEvent",
            "data": {
                "type": "QUESTIONS_GENERATED",
                "step_name": "生成预测性问题",
                "questions": [
                    {
                        "id": f"pred_q_{i+1}",
                        "title": f"预测性问题 {i+1}",
                        "content": question,
                        "type": "prediction",
                        "status": "generated",
                        "category": "预测分析",
                        "icon": "📈"
                    }
                    for i, question in enumerate(result.questions)
                ],
                "total_count": len(result.questions),
                "category": "预测性问题"
            }
        }
        
        # 发送步骤完成事件
        yield EventEmitter.create_step_finished_event(
            step_name="生成预测性问题",
            description=f"成功生成 {len(result.questions)} 个预测性问题",
            result_summary=f"生成了 {len(result.questions)} 个问题"
        )
        
        # 返回结果
        yield {
            "prediction_questions": result.questions,
        }
        
    except Exception as e:
        # 发送步骤失败事件
        yield EventEmitter.create_step_finished_event(
            step_name="生成预测性问题",
            description="预测性问题生成失败",
            error=str(e)
        )
        if configurable.enable_debug:
            print(f"预测性问题生成异常: {str(e)}")
        raise


async def generate_quantitative_questions(
    state: InvestmentAnalysisState, config: RunnableConfig
):
    """生成量化筛选问题"""
    configurable = VestConfiguration.from_runnable_config(config)
    
    # 发送步骤开始事件
    yield EventEmitter.create_step_started_event(
        step_name="生成量化分析问题",
        description="正在生成量化投资分析问题..."
    )
    
    # 创建PydanticOutputParser
    parser = PydanticOutputParser(pydantic_object=QuestionList)
    
    llm = LLMClientUtils.create_llm(
        model_type=configurable.question_generation_model,
        temperature=0.7,
        streaming=False
    )
    
    # 使用产业链分析结果作为输入
    industry_summary = "\n".join(state.get("industry_chain_results", []))
    
    # 在prompt中添加格式说明
    formatted_prompt = QUANTITATIVE_PROMPT.format(
        industry_analysis=industry_summary or "暂无产业链分析结果"
    ) + "\n\n" + parser.get_format_instructions()
    
    try:
        # 调用LLM获取响应
        response = await llm.ainvoke(formatted_prompt)
        
        # 使用parser解析响应
        result = parser.parse(response.content)
        
        # 发送问题卡片数据事件
        yield {
            "type": "CustomEvent",
            "data": {
                "type": "QUESTIONS_GENERATED",
                "step_name": "生成量化分析问题",
                "questions": [
                    {
                        "id": f"quant_q_{i+1}",
                        "title": f"量化分析问题 {i+1}",
                        "content": question,
                        "type": "quantitative",
                        "status": "generated",
                        "category": "量化分析",
                        "icon": "📊"
                    }
                    for i, question in enumerate(result.questions)
                ],
                "total_count": len(result.questions),
                "category": "量化分析问题"
            }
        }
        
        # 发送步骤完成事件
        yield EventEmitter.create_step_finished_event(
            step_name="生成量化分析问题",
            description=f"成功生成 {len(result.questions)} 个量化分析问题",
            result_summary=f"生成了 {len(result.questions)} 个问题"
        )
        
        # 返回结果
        yield {
            "quantitative_questions": result.questions,
        }
        
    except Exception as e:
        # 发送步骤失败事件
        yield EventEmitter.create_step_finished_event(
            step_name="生成量化分析问题",
            description="量化分析问题生成失败",
            error=str(e)
        )
        if configurable.enable_debug:
            print(f"量化分析问题生成异常: {str(e)}")
        raise


async def macro_analysis_loop(
    state: InvestmentAnalysisState, config: RunnableConfig
):
    """宏观与行业分析循环"""
    configurable = VestConfiguration.from_runnable_config(config)
    
    # 检查循环次数
    current_count = state.get("macro_loop_count", 0)
    if current_count >= configurable.max_macro_loops:
        yield {"macro_loop_count": current_count}
        return
    
    # 发送步骤开始事件
    yield EventEmitter.create_step_started_event(
        step_name=f"宏观分析循环 (第{current_count + 1}轮)",
        description="正在执行宏观经济和行业分析..."
    )
    
    # 执行搜索和分析
    search = VestSearch(configurable)
    questions = state.get("fact_check_questions", [])
    
    analysis_results = []
    for i, question in enumerate(questions[:5]):
        try:
            # 发送子步骤事件
            yield {
                "type": "ANALYSIS_PROGRESS",
                "data": {
                    "step": f"分析问题 {i+1}/{min(len(questions), 5)}",
                    "question": question,
                    "progress": (i + 1) / min(len(questions), 5) * 100,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            search_result = await search.search(question)
            
            # 使用LLM分析搜索结果
            llm = LLMClientUtils.create_llm(
                model_type=configurable.analysis_model,
                temperature=configurable.analysis_temperature,
                streaming=False
            )
            
            formatted_prompt = MACRO_ANALYSIS_PROMPT.format(
                question=question,
                search_content=search_result.get('search_content', ''),
                profile=state["profile"]
            )
            
            analysis = await llm.ainvoke(formatted_prompt)
            analysis_results.append(analysis.content)
            
        except Exception as e:
            if configurable.enable_debug:
                print(f"宏观分析异常 - 问题: '{question}', 错误: {str(e)}")
    
    # 发送步骤完成事件
    yield EventEmitter.create_step_finished_event(
        step_name=f"宏观分析循环 (第{current_count + 1}轮)",
        description=f"完成 {len(analysis_results)} 个问题的宏观分析",
        result_summary=f"分析了 {len(analysis_results)} 个宏观经济问题"
    )
    
    # 发送宏观分析完成的自定义事件
    yield {
        "type": "CustomEvent",
        "data": {
            "type": "MACRO_ANALYSIS_COMPLETE",
            "macro_analysis_results": analysis_results,
            "step_name": f"宏观分析循环 (第{current_count + 1}轮)",
            "total_results": len(analysis_results),
            "timestamp": datetime.now().isoformat()
        }
    }
    
    # 返回结果
    yield {
        "macro_analysis_results": analysis_results,
        "macro_loop_count": current_count + 1,
    }


async def industry_analysis_loop(
    state: InvestmentAnalysisState, config: RunnableConfig
):
    """产业链分析循环"""
    configurable = VestConfiguration.from_runnable_config(config)
    
    current_count = state.get("industry_loop_count", 0)
    if current_count >= configurable.max_industry_loops:
        return {"industry_loop_count": current_count}
    
    search = VestSearch(configurable)
    questions = state.get("prediction_questions", [])
    
    analysis_results = []
    for question in questions[:5]:
        try:
            search_result = await search.search(question)
            
            llm = LLMClientUtils.create_llm(
                model_type=configurable.analysis_model,
                temperature=configurable.analysis_temperature,
                streaming=False
            )
            
            formatted_prompt = INDUSTRY_ANALYSIS_PROMPT.format(
                question=question,
                search_content=search_result.get('search_content', ''),
                macro_analysis="\n".join(state.get("macro_analysis_results", []))
            )
            
            analysis = await llm.ainvoke(formatted_prompt)
            analysis_results.append(analysis.content)
            
        except Exception as e:
            if configurable.enable_debug:
                print(f"产业链分析异常 - 问题: '{question}', 错误: {str(e)}")
            analysis_results.append(f"分析失败: {str(e)}")
    
    return {
        "industry_chain_results": analysis_results,
        "industry_loop_count": current_count + 1,
    }


async def quantitative_analysis_loop(
    state: InvestmentAnalysisState, config: RunnableConfig
):
    """量化投资策略分析循环"""
    configurable = VestConfiguration.from_runnable_config(config)
    
    current_count = state.get("quantitative_loop_count", 0)
    if current_count >= configurable.max_quantitative_loops:
        return {"quantitative_loop_count": current_count}
    
    search = VestSearch(configurable)
    questions = state.get("quantitative_questions", [])
    
    analysis_results = []
    for question in questions[:5]:
        try:
            search_result = await search.search(question)
            
            llm = LLMClientUtils.create_llm(
                model_type=configurable.analysis_model,
                temperature=configurable.analysis_temperature,
                streaming=False
            )
            
            formatted_prompt = QUANTITATIVE_ANALYSIS_PROMPT.format(
                question=question,
                search_content=search_result.get('search_content', ''),
                industry_analysis="\n".join(state.get("industry_chain_results", []))
            )
            
            analysis = await llm.ainvoke(formatted_prompt)
            analysis_results.append(analysis.content)
            
        except Exception as e:
            if configurable.enable_debug:
                print(f"量化分析异常 - 问题: '{question}', 错误: {str(e)}")
            analysis_results.append(f"分析失败: {str(e)}")
    
    return {
        "quantitative_results": analysis_results,
        "quantitative_loop_count": current_count + 1,
    }


async def finalize_investment_thesis(
    state: InvestmentAnalysisState, config: RunnableConfig
):
    """最终投资论点综合"""
    configurable = VestConfiguration.from_runnable_config(config)
    
    # 创建PydanticOutputParser
    parser = PydanticOutputParser(pydantic_object=InvestmentThesis)
    
    llm = LLMClientUtils.create_llm(
        model_type=configurable.synthesis_model,
        temperature=configurable.synthesis_temperature,
        streaming=False
    )
    
    # 整合所有分析结果
    macro_summary = "\n".join(state.get("macro_analysis_results", []))
    industry_summary = "\n".join(state.get("industry_chain_results", []))
    quantitative_summary = "\n".join(state.get("quantitative_results", []))
    
    formatted_prompt = FINAL_SYNTHESIS_PROMPT.format(
        profile=state["profile"],
        macro_analysis=macro_summary,
        industry_analysis=industry_summary,
        quantitative_analysis=quantitative_summary
    ) + "\n\n" + parser.get_format_instructions()
    
    try:
        response = await llm.ainvoke(formatted_prompt)
        result = parser.parse(response.content)
        
        return {
            "final_synthesis_results": [result.model_dump_json()],
        }
        
    except Exception as e:
        if configurable.enable_debug:
            print(f"最终综合分析异常: {str(e)}")
        raise


# 路由函数
def should_continue_macro_loop(
    state: InvestmentAnalysisState, config: RunnableConfig
) -> str:
    """判断是否继续宏观分析循环"""
    configurable = VestConfiguration.from_runnable_config(config)
    current_count = state.get("macro_loop_count", 0)
    
    if current_count >= configurable.max_macro_loops:
        return NodeNames.GENERATE_PREDICTION_QUESTIONS
    else:
        return NodeNames.MACRO_ANALYSIS_LOOP


def should_continue_industry_loop(
    state: InvestmentAnalysisState, config: RunnableConfig
) -> str:
    """判断是否继续产业链分析循环"""
    configurable = VestConfiguration.from_runnable_config(config)
    current_count = state.get("industry_loop_count", 0)
    
    if current_count >= configurable.max_industry_loops:
        return NodeNames.GENERATE_QUANTITATIVE_QUESTIONS
    else:
        return NodeNames.INDUSTRY_ANALYSIS_LOOP


def should_continue_quantitative_loop(
    state: InvestmentAnalysisState, config: RunnableConfig
) -> str:
    """判断是否继续量化分析循环"""
    configurable = VestConfiguration.from_runnable_config(config)
    current_count = state.get("quantitative_loop_count", 0)
    
    if current_count >= configurable.max_quantitative_loops:
        return NodeNames.FINALIZE_INVESTMENT_THESIS
    else:
        return NodeNames.QUANTITATIVE_ANALYSIS_LOOP