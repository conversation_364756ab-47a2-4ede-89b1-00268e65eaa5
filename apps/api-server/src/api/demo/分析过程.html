<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>AI 分析进度</title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" rel="stylesheet">
<script>
tailwind.config = {
  theme: {
    extend: {
      colors: {
        primary: "#2563eb",
        secondary: "#6B7280",
      },
      borderRadius: {
        none: "0px",
        sm: "4px",
        DEFAULT: "8px",
        md: "12px",
        lg: "16px",
        xl: "20px",
        "2xl": "24px",
        "3xl": "32px",
        full: "9999px",
        button: "8px",
      },
    },
  },
};
</script>
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
.task-item:hover { 
  background-color: rgba(79, 70, 229, 0.05); 
  transform: translateY(-1px);
  transition: all 0.2s ease;
}
.task-item.active { 
  background-color: rgba(79, 70, 229, 0.1); 
  border-left: 4px solid #2563eb;
}
.collapsible-section {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.collapsible-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  cursor: pointer;
  transition: all 0.2s ease;
}
.section-header:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
}
.section-content {
  max-height: 1000px;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
}
.section-content.collapsed {
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}
.rotate-180 {
  transform: rotate(180deg);
}
.progress-bar {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  height: 4px;
  border-radius: 2px;
  transition: width 0.5s ease;
}
.status-badge {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}
</style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
<div class="min-h-screen">
<main class="max-w-4xl mx-auto p-8">
<div class="space-y-8">
<!-- 头部介绍 -->
<div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
<div class="text-gray-700 leading-relaxed">好的，我将为您创建一个关于 Next.js 原理及应用的详细讲解计划。</div>
</div>

<!-- 待办清单标题 -->
<div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
<div class="flex items-center justify-between mb-6">
<h2 class="text-2xl font-bold text-gray-900 flex items-center">
<i class="ri-task-line text-primary mr-3"></i>
待办清单
</h2>
<span class="status-badge">自动执行</span>
</div>

<!-- 进度条 -->
<div class="mb-6">
<div class="flex justify-between text-sm text-gray-600 mb-2">
<span>总体进度</span>
<span>33%</span>
</div>
<div class="w-full bg-gray-200 rounded-full h-2">
<div class="progress-bar" style="width: 33%"></div>
</div>
</div>

<!-- 任务列表 -->
<div class="space-y-4">
<!-- 第一个任务 -->
<div class="collapsible-section">
<div class="section-header p-4" onclick="toggleSection(this)">
<div class="flex items-center justify-between">
<div class="flex items-center space-x-3">
<i class="ri-checkbox-circle-fill text-green-500 text-xl"></i>
<h3 class="text-lg font-semibold text-gray-900">1. Next.js 核心原理研究</h3>
</div>
<i class="ri-arrow-down-s-line text-gray-500 transition-transform duration-200"></i>
</div>
</div>
<div class="section-content p-6 bg-white">
<ul class="space-y-4 text-gray-700">
<li class="flex items-start space-x-3">
<i class="ri-arrow-right-s-line text-primary mt-1"></i>
<span>研究 Next.js 的基本概念、核心特性及其与传统 React 应用的区别</span>
</li>
<li class="flex items-start space-x-3">
<i class="ri-arrow-right-s-line text-primary mt-1"></i>
<span>分析 Next.js 的底层技术架构、工作原理及关键组件</span>
</li>
<li class="flex items-start space-x-3">
<i class="ri-arrow-right-s-line text-primary mt-1"></i>
<span>调研 Next.js 的服务器端渲染(SSR)、静态生成(SSG)和静态导出机制</span>
</li>
</ul>
</div>
</div>

<!-- 第二个任务 -->
<div class="collapsible-section">
<div class="section-header p-4" onclick="toggleSection(this)">
<div class="flex items-center justify-between">
<div class="flex items-center space-x-3">
<i class="ri-checkbox-circle-line text-blue-500 text-xl"></i>
<h3 class="text-lg font-semibold text-gray-900">2. Next.js 高级功能与最佳实践研究</h3>
</div>
<i class="ri-arrow-down-s-line text-gray-500 transition-transform duration-200"></i>
</div>
</div>
<div class="section-content p-6 bg-white">
<ul class="space-y-4 text-gray-700 mb-6">
<li class="flex items-start space-x-3">
<i class="ri-arrow-right-s-line text-primary mt-1"></i>
<span>研究 Next.js 的路由系统、API 路由和动态路由实现方式</span>
</li>
<li class="flex items-start space-x-3">
<i class="ri-arrow-right-s-line text-primary mt-1"></i>
<span>调研 Next.js 的数据获取方法、性能优化策略和部署方案</span>
</li>
<li class="flex items-start space-x-3">
<i class="ri-arrow-right-s-line text-primary mt-1"></i>
<span>分析 Next.js 在大型项目中的应用模式和最佳实践案例</span>
</li>
</ul>

<!-- 详细研究过程 -->
<div class="bg-gray-50 rounded-lg p-4">
<p class="text-gray-600 mb-4">我将使用网络深度搜索工具来全面收集 Next.js 的高级功能和最佳实践相关信息。这个工具能帮助我系统地获取最新和最主要的技术细节。</p>
<p class="text-gray-600 mb-4">我将分步研究Next.js高级功能与最佳实践，首先搜索了解其路由系统。</p>

<div class="space-y-3">
<div class="flex items-center space-x-3 p-2 bg-white rounded border">
<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">MCP 工具</span>
<i class="ri-search-line text-gray-400"></i>
<span class="text-gray-600 text-sm">搜索网页</span>
<span class="text-gray-500 text-sm">Next.js routing systems Pages Router vs App Router</span>
</div>
<div class="flex items-center space-x-3 p-2 bg-white rounded border">
<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">MCP 工具</span>
<i class="ri-search-line text-gray-400"></i>
<span class="text-gray-600 text-sm">搜索网页</span>
<span class="text-gray-500 text-sm">Next.js API Routes Tutorial</span>
</div>
<div class="flex items-center space-x-3 p-2 bg-white rounded border">
<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">MCP 工具</span>
<i class="ri-search-line text-gray-400"></i>
<span class="text-gray-600 text-sm">搜索网页</span>
<span class="text-gray-500 text-sm">Next.js dynamic routing examples</span>
</div>
</div>

<p class="text-gray-600 my-4">我初步分析了Next.js的App和Pages路由系统、API路由及动态路由，计划深入研究官方文档并学习大型项目实践。</p>

<div class="flex items-center space-x-3 p-2 bg-white rounded border">
<span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">MCP 工具</span>
<i class="ri-eye-line text-gray-400"></i>
<span class="text-gray-600 text-sm">浏览</span>
<a href="https://nextjs.org/docs/pages/building-your-application/routing/api-routes" class="text-blue-600 hover:text-blue-800 text-sm underline" target="_blank">https://nextjs.org/docs/pages/building-your-application/routing/api-routes</a>
</div>
</div>
</div>
</div>

<!-- 第三个任务 -->
<div class="collapsible-section">
<div class="section-header p-4" onclick="toggleSection(this)">
<div class="flex items-center justify-between">
<div class="flex items-center space-x-3">
<i class="ri-checkbox-blank-circle-line text-gray-400 text-xl"></i>
<h3 class="text-lg font-semibold text-gray-900">3. 撰写 Next.js 原理及应用详细讲解</h3>
</div>
<i class="ri-arrow-down-s-line text-gray-500 transition-transform duration-200"></i>
</div>
</div>
<div class="section-content p-6 bg-white collapsed">
<ul class="space-y-4 text-gray-700">
<li class="flex items-start space-x-3">
<i class="ri-arrow-right-s-line text-primary mt-1"></i>
<span>撰写 Next.js 原理及应用的详细讲解文档</span>
</li>
</ul>
</div>
</div>
</div>

<!-- 执行状态 -->
<div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
<div class="text-gray-700 leading-relaxed mb-4">我将按照计划开始执行任务。首先，我会处理第一个阶段，研究 Next.js 的核心原理与技术架构。</div>

<!-- 其他任务项 -->
<div class="space-y-3">
<div class="task-item rounded-lg p-4 border border-gray-200 bg-gray-50">
<div class="flex items-center">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-checkbox-blank-circle-line text-gray-400"></i>
</div>
<span class="ml-3 text-gray-900 font-medium">2. 内容分析</span>
</div>
</div>
<div class="task-item rounded-lg p-4 border border-gray-200 bg-gray-50">
<div class="flex items-center">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-checkbox-blank-circle-line text-gray-400"></i>
</div>
<span class="ml-3 text-gray-900 font-medium">3. 生成报告</span>
</div>
</div>
<div class="task-item rounded-lg p-4 border border-gray-200 bg-gray-50">
<div class="flex items-center">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-checkbox-blank-circle-line text-gray-400"></i>
</div>
<span class="ml-3 text-gray-900 font-medium">4. 导出结果</span>
</div>
</div>
</div>
</div>
</div>
</main>
</div>

<script>
function toggleSection(header) {
  const content = header.nextElementSibling;
  const arrow = header.querySelector('.ri-arrow-down-s-line');
  
  if (content.classList.contains('collapsed')) {
    content.classList.remove('collapsed');
    arrow.classList.add('rotate-180');
  } else {
    content.classList.add('collapsed');
    arrow.classList.remove('rotate-180');
  }
}

// 初始化：设置第一个和第二个章节为展开状态
document.addEventListener('DOMContentLoaded', function() {
  const sections = document.querySelectorAll('.collapsible-section');
  
  // 展开前两个章节
  for (let i = 0; i < Math.min(2, sections.length); i++) {
    const content = sections[i].querySelector('.section-content');
    const arrow = sections[i].querySelector('.ri-arrow-down-s-line');
    
    content.classList.remove('collapsed');
    arrow.classList.add('rotate-180');
  }
});
</script>
</body>
</html>