"""事件发送工具"""
import time
import json
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime

_INTERNAL_EVENT_MARKER = "__internal_agui_event__"

class EventEmitter:
    """简化的事件发送器，使用 yield 直接发送事件"""
    
    @staticmethod
    def create_step_started_event(
        step_name: str,
        description: str = "",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """构造步骤开始事件"""
        return {
            "type": "CustomEvent",
            "data": {
                "type": "STEP_STARTED",
                "step_name": step_name,
                "description": description,
                "metadata": metadata or {},
                "timestamp": datetime.now().isoformat()
            }
        }
    
    @staticmethod
    def create_step_finished_event(
        step_name: str,
        description: str = "",
        result_summary: str = "",
        error: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """构造步骤完成事件"""
        return {
            "type": "CustomEvent",
            "data": {
                "type": "STEP_FINISHED",
                "step_name": step_name,
                "description": description,
                "result_summary": result_summary,
                "error": error,
                "metadata": metadata or {},
                "timestamp": datetime.now().isoformat(),
                "success": error is None
            }
        }
    
    @staticmethod
    async def emit_step_started(
        step_name: str,
        description: str = "",
        metadata: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """发送步骤开始事件"""
        yield EventEmitter.create_step_started_event(step_name, description, metadata)
        
    @staticmethod
    async def emit_step_finished(
        step_name: str,
        description: str = "",
        result_summary: str = "",
        error: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """发送步骤完成事件"""
        yield EventEmitter.create_step_finished_event(step_name, description, result_summary, error, metadata)
        
    @staticmethod
    async def emit_custom_event(
        event_name: str,
        data: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """发送自定义事件"""
        yield {
            "type": event_name,
            "data": {
                **data,
                "timestamp": datetime.now().isoformat()
            }
        }

# 便捷函数，可以直接在节点中使用
async def step_started(step_name: str, description: str = "") -> AsyncGenerator[Dict[str, Any], None]:
    """便捷函数：发送步骤开始事件"""
    yield EventEmitter.create_step_started_event(step_name, description)

async def step_finished(step_name: str, description: str = "", result_summary: str = "", error: Optional[str] = None) -> AsyncGenerator[Dict[str, Any], None]:
    """便捷函数：发送步骤完成事件"""
    yield EventEmitter.create_step_finished_event(step_name, description, result_summary, error)

# 装饰器，用于自动发送步骤开始和完成事件
def track_step(step_name: str, description: str = ""):
    """装饰器：自动跟踪步骤的开始和完成"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 发送开始事件
            yield EventEmitter.create_step_started_event(step_name, description)
            
            try:
                # 执行原函数
                if hasattr(func, '__aiter__'):  # 如果是异步生成器
                    async for result in func(*args, **kwargs):
                        yield result
                else:  # 如果是普通异步函数
                    result = await func(*args, **kwargs)
                    yield result
                
                # 发送完成事件
                yield EventEmitter.create_step_finished_event(step_name, f"{description} 完成")
                    
            except Exception as e:
                # 发送错误事件
                yield EventEmitter.create_step_finished_event(step_name, description, error=str(e))
                raise
                
        return wrapper
    return decorator
