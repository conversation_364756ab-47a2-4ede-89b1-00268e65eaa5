"""API数据模式 - 与业务层DTOs分离的API层模型"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field


# 通用响应模型
class SuccessResponse(BaseModel):
    """成功响应模型"""
    success: bool = Field(True, description="操作是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="响应时间")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(False, description="操作是否成功")
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误详情")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详细信息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="错误时间")


# 聊天相关API模型
class ChatSessionCreateRequest(BaseModel):
    """创建聊天会话API请求"""
    initial_message: Optional[str] = Field(None, max_length=1000, description="初始消息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "initial_message": "请帮我分析一下苹果公司的最新财报"
            }
        }


class ChatMessageSendRequest(BaseModel):
    """发送聊天消息API请求"""
    message: str = Field(..., min_length=1, max_length=10000, description="消息内容")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "请分析特斯拉的Q3财报数据",
                "metadata": {
                    "source": "web",
                    "priority": "normal"
                }
            }
        }


class ChatSessionResponse(BaseModel):
    """聊天会话API响应"""
    session_id: str = Field(..., description="会话ID")
    title: str = Field(..., description="会话标题")
    status: str = Field(..., description="会话状态")
    created_at: datetime = Field(..., description="创建时间")
    message_count: int = Field(..., description="消息数量")
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "session-12345678",
                "title": "苹果公司财报分析",
                "status": "active",
                "created_at": "2024-01-01T00:00:00Z",
                "message_count": 5
            }
        }


# 研究分析相关API模型
class ResearchPipelineCreateRequest(BaseModel):
    """创建研究流水线API请求"""
    name: str = Field(..., min_length=1, max_length=200, description="流水线名称")
    description: str = Field(..., max_length=1000, description="流水线描述")
    thread_id: Optional[str] = Field(None, description="关联的聊天线程ID")
    
    class Config:
        json_schema_extra = {
            "example": {
                "name": "特斯拉投资分析",
                "description": "对特斯拉公司进行全面的投资研究分析",
                "thread_id": "thread-12345678"
            }
        }


class ResearchTaskAddRequest(BaseModel):
    """添加研究任务API请求"""
    task_type: str = Field(..., description="任务类型")
    title: str = Field(..., min_length=1, max_length=200, description="任务标题")
    description: str = Field(..., max_length=1000, description="任务描述")
    context: Optional[Dict[str, Any]] = Field(None, description="任务上下文")
    
    class Config:
        json_schema_extra = {
            "example": {
                "task_type": "truth-verification",
                "title": "验证财报数据真实性",
                "description": "验证特斯拉Q3财报数据的准确性和一致性",
                "context": {
                    "ticker": "TSLA",
                    "period": "Q3-2024"
                }
            }
        }


class ResearchTaskResponse(BaseModel):
    """研究任务API响应"""
    task_id: str = Field(..., description="任务ID")
    title: str = Field(..., description="任务标题")
    task_type: str = Field(..., description="任务类型")
    status: str = Field(..., description="任务状态")
    order: int = Field(..., description="执行顺序")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    duration: Optional[float] = Field(None, description="执行时长（秒）")
    
    class Config:
        json_schema_extra = {
            "example": {
                "task_id": "task-12345678",
                "title": "验证财报数据真实性",
                "task_type": "truth-verification",
                "status": "completed",
                "order": 1,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:05:00Z",
                "duration": 30.5
            }
        }


class ResearchPipelineResponse(BaseModel):
    """研究流水线API响应"""
    pipeline_id: str = Field(..., description="流水线ID")
    name: str = Field(..., description="流水线名称")
    description: str = Field(..., description="流水线描述")
    total_tasks: int = Field(..., description="总任务数")
    completed_tasks: int = Field(..., description="已完成任务数")
    failed_tasks: int = Field(..., description="失败任务数")
    progress_percentage: float = Field(..., description="进度百分比")
    is_completed: bool = Field(..., description="是否已完成")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    tasks: List[ResearchTaskResponse] = Field(default_factory=list, description="任务列表")
    
    class Config:
        json_schema_extra = {
            "example": {
                "pipeline_id": "pipeline-12345678",
                "name": "特斯拉投资分析",
                "description": "对特斯拉公司进行全面的投资研究分析",
                "total_tasks": 3,
                "completed_tasks": 2,
                "failed_tasks": 0,
                "progress_percentage": 66.7,
                "is_completed": False,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:15:00Z",
                "tasks": []
            }
        }


# 流式响应事件模型
class StreamEvent(BaseModel):
    """流式响应事件模型"""
    type: str = Field(..., description="事件类型")
    data: Dict[str, Any] = Field(..., description="事件数据")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="事件时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "type": "task_start",
                "data": {
                    "task_id": "task-12345678",
                    "task_type": "truth-verification",
                    "title": "验证财报数据真实性"
                },
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }


# 健康检查响应模型
class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="版本号")
    timestamp: datetime = Field(..., description="检查时间")
    environment: Optional[str] = Field(None, description="运行环境")
    dependencies: Optional[Dict[str, str]] = Field(None, description="依赖服务状态")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "version": "1.0.0",
                "timestamp": "2024-01-01T00:00:00Z",
                "environment": "production",
                "dependencies": {
                    "database": "available",
                    "llm_service": "available",
                    "tools": "available"
                }
            }
        }