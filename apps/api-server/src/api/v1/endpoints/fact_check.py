"""
事实核查API端点
"""
import json
import asyncio
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import logging

from ....domain.fact_check.workflow import get_fact_check_workflow
from ....infrastructure.adapters.agui_event_sender import agui_event_sender

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/fact-check", tags=["fact-check"])

class FactCheckRequest(BaseModel):
    """事实核查请求模型"""
    text: str = Field(..., description="待核查的文本", min_length=10, max_length=10000)
    options: Dict[str, Any] = Field(default_factory=dict, description="核查选项")

class FactCheckResponse(BaseModel):
    """事实核查响应模型"""
    task_id: str
    status: str
    message: str

@router.post("/start", response_model=FactCheckResponse)
async def start_fact_check(
    request: FactCheckRequest,
    background_tasks: BackgroundTasks
):
    """启动事实核查任务"""
    
    # 生成任务ID
    import time
    task_id = f"fact_check_{int(time.time() * 1000)}"
    
    logger.info(f"收到事实核查请求，任务ID: {task_id}, 文本长度: {len(request.text)}")
    
    # 验证选项
    default_options = {
        "enable_financial_verification": True,
        "enable_corporate_verification": True,
        "enable_news_verification": True,
        "deep_research_mode": False
    }
    options = {**default_options, **request.options}
    
    # 在后台运行事实核查
    background_tasks.add_task(
        _run_fact_check_background,
        task_id,
        request.text,
        options
    )
    
    return FactCheckResponse(
        task_id=task_id,
        status="started",
        message="事实核查任务已启动，请通过WebSocket或SSE监听进度更新"
    )

async def _run_fact_check_background(
    task_id: str,
    text: str,
    options: Dict[str, Any]
):
    """后台运行事实核查任务"""
    try:
        logger.info(f"开始后台执行事实核查，任务ID: {task_id}")
        
        # 运行事实核查工作流
        workflow = get_fact_check_workflow()
        final_state = await workflow.run_fact_check(
            task_id=task_id,
            text=text,
            options=options
        )
        
        logger.info(f"事实核查任务完成，任务ID: {task_id}")
        
    except Exception as e:
        error_msg = f"事实核查任务执行失败: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        # 发送错误事件
        await agui_event_sender.send_error(
            task_id=task_id,
            error_message=error_msg
        )

@router.post("/cancel/{task_id}")
async def cancel_fact_check(task_id: str):
    """取消事实核查任务"""
    
    logger.info(f"收到取消请求，任务ID: {task_id}")
    
    # TODO: 实现任务取消逻辑
    # 这需要在工作流中添加取消机制
    
    return {"task_id": task_id, "status": "cancelled", "message": "任务取消请求已提交"}

@router.get("/status/{task_id}")
async def get_fact_check_status(task_id: str):
    """获取事实核查任务状态"""
    
    logger.info(f"查询任务状态，任务ID: {task_id}")
    
    # TODO: 实现状态查询逻辑
    # 这需要从工作流状态存储中获取状态
    
    return {
        "task_id": task_id,
        "status": "unknown",
        "message": "状态查询功能开发中"
    }

@router.get("/history")
async def get_fact_check_history(limit: int = 10, offset: int = 0):
    """获取事实核查历史记录"""
    
    logger.info(f"查询历史记录，limit: {limit}, offset: {offset}")
    
    # TODO: 实现历史记录查询
    # 这需要持久化存储历史任务
    
    return {
        "total": 0,
        "items": [],
        "message": "历史记录功能开发中"
    }

# 示例端点：测试事实核查功能
@router.post("/test")
async def test_fact_check():
    """测试事实核查功能的示例端点"""
    
    test_text = """
    据报道，苹果公司在2024年第四季度的营收达到了946亿美元，同比增长6%。
    公司计划在2025年春季发布新款MacBook Pro，搭载M4 Pro芯片。
    分析师预计苹果股价将在未来6个月内上涨15%。
    """
    
    task_id = f"test_fact_check_{int(asyncio.get_event_loop().time() * 1000)}"
    
    logger.info(f"启动测试事实核查，任务ID: {task_id}")
    
    try:
        # 直接运行工作流（非后台）
        workflow = get_fact_check_workflow()
        final_state = await workflow.run_fact_check(
            task_id=task_id,
            text=test_text,
            options={
                "enable_financial_verification": True,
                "enable_corporate_verification": False,
                "enable_news_verification": False,
                "deep_research_mode": False
            }
        )
        
        return {
            "task_id": task_id,
            "status": "completed",
            "result": final_state.get("final_report"),
            "message": "测试完成"
        }
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")