"""聊天API端点 - 重构后的版本化API"""
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator
from dependency_injector.wiring import inject, Provide

from ....application.use_cases.chat_facade import ChatFacade
from ....application.dtos.chat_dtos import (
    CreateChatSessionRequest,
    ChatMessageRequest,
    SessionInfo
)
from ....core.container import MainContainer
from ....core.exceptions import ApplicationException
from ....infrastructure.services.logging import get_logger
from ..deps import get_current_user

router = APIRouter(prefix="/chat", tags=["chat"])
logger = get_logger(__name__)


@router.post("/sessions", response_model=SessionInfo)
@inject
async def create_chat_session(
    request: CreateChatSessionRequest,
    chat_use_case: ChatFacade = Depends(Provide[MainContainer.application.chat_use_case]),
    current_user: str = Depends(get_current_user)
):
    """创建聊天会话"""
    try:
        # 设置用户ID
        if not request.user_id:
            request.user_id = current_user
        
        session_info = await chat_use_case.create_chat_session(request)
        
        logger.info(
            "Chat session created",
            session_id=session_info.session_id,
            user_id=request.user_id
        )
        
        return session_info
        
    except ApplicationException as e:
        logger.error("Failed to create chat session", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in create_chat_session", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/sessions/{session_id}/messages/stream")
@inject
async def chat_stream(
    session_id: str,
    request: ChatMessageRequest,
    chat_use_case: ChatFacade = Depends(Provide[MainContainer.application.chat_use_case]),
    current_user: str = Depends(get_current_user)
):
    """流式聊天端点"""
    try:
        # 验证会话ID匹配
        if request.session_id != session_id:
            raise HTTPException(status_code=400, detail="Session ID mismatch")
        
        async def event_generator() -> AsyncGenerator[str, None]:
            async for event in chat_use_case.stream_chat_response(
                session_id=session_id,
                message=request.message,
                user_id=current_user,
                metadata=request.metadata
            ):
                yield f"data: {event}\n\n"
        
        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Session-ID": session_id,
            }
        )
        
    except ApplicationException as e:
        logger.error("Failed to process chat stream", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in chat_stream", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/sessions/{session_id}")
@inject
async def get_session_history(
    session_id: str,
    chat_use_case: ChatFacade = Depends(Provide[MainContainer.application.chat_use_case]),
    current_user: str = Depends(get_current_user)
):
    """获取会话历史"""
    try:
        history = await chat_use_case.get_session_history(
            session_id=session_id,
            user_id=current_user
        )
        
        return history
        
    except ApplicationException as e:
        logger.error("Failed to get session history", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in get_session_history", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/sessions/{session_id}/pause", response_model=SessionInfo)
@inject
async def pause_session(
    session_id: str,
    chat_use_case: ChatFacade = Depends(Provide[MainContainer.application.chat_use_case]),
    current_user: str = Depends(get_current_user)
):
    """暂停会话"""
    try:
        session_info = await chat_use_case.pause_session(session_id)
        
        logger.info(
            "Chat session paused",
            session_id=session_id,
            user_id=current_user
        )
        
        return session_info
        
    except ApplicationException as e:
        logger.error("Failed to pause session", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in pause_session", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/sessions/{session_id}/resume", response_model=SessionInfo)
@inject
async def resume_session(
    session_id: str,
    chat_use_case: ChatFacade = Depends(Provide[MainContainer.application.chat_use_case]),
    current_user: str = Depends(get_current_user)
):
    """恢复会话"""
    try:
        session_info = await chat_use_case.resume_session(session_id)
        
        logger.info(
            "Chat session resumed",
            session_id=session_id,
            user_id=current_user
        )
        
        return session_info
        
    except ApplicationException as e:
        logger.error("Failed to resume session", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in resume_session", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")