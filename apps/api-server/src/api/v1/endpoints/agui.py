"""
AGUI WebSocket端点 - 支持AG-UI客户端连接
"""
import json
import asyncio
from typing import Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import StreamingResponse
import logging

from ....domain.fact_check.workflow import get_fact_check_workflow
from ....infrastructure.adapters.agui_event_sender import agui_event_sender

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/agui", tags=["agui"])

class AGUIConnectionManager:
    """AGUI连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"AGUI客户端连接: {client_id}")
    
    def disconnect(self, client_id: str):
        """断开连接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"AGUI客户端断开: {client_id}")
    
    async def send_message(self, client_id: str, message: dict):
        """向特定客户端发送消息"""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"发送消息失败: {e}")
                self.disconnect(client_id)

# 全局连接管理器
connection_manager = AGUIConnectionManager()

@router.websocket("/ws/{client_id}")
async def agui_websocket_endpoint(websocket: WebSocket, client_id: str):
    """AGUI WebSocket端点"""
    await connection_manager.connect(websocket, client_id)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            logger.info(f"收到AGUI消息: {message}")
            
            # 处理不同类型的消息
            action = message.get("action")
            
            if action == "fact_check":
                await handle_fact_check_message(client_id, message)
            elif action == "cancel_fact_check":
                await handle_cancel_fact_check(client_id, message)
            else:
                logger.warning(f"未知的AGUI动作: {action}")
                
    except WebSocketDisconnect:
        connection_manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        connection_manager.disconnect(client_id)

async def handle_fact_check_message(client_id: str, message: Dict[str, Any]):
    """处理事实核查消息"""
    try:
        task_id = message.get("taskId")
        text = message.get("text")
        options = message.get("options", {})
        
        if not task_id or not text:
            await connection_manager.send_message(client_id, {
                "type": "error",
                "message": "缺少必要参数: taskId 或 text"
            })
            return
        
        # 发送任务开始事件
        await connection_manager.send_message(client_id, {
            "type": "fact_check_task_started",
            "taskId": task_id,
            "message": "事实核查任务已启动"
        })
        
        # 在后台运行事实核查
        asyncio.create_task(_run_fact_check_agui(client_id, task_id, text, options))
        
    except Exception as e:
        logger.error(f"处理事实核查消息失败: {e}")
        await connection_manager.send_message(client_id, {
            "type": "error",
            "message": f"处理失败: {str(e)}"
        })

async def handle_cancel_fact_check(client_id: str, message: Dict[str, Any]):
    """处理取消事实核查消息"""
    task_id = message.get("taskId")
    
    await connection_manager.send_message(client_id, {
        "type": "fact_check_cancelled",
        "taskId": task_id,
        "message": "事实核查任务已取消"
    })

async def _run_fact_check_agui(client_id: str, task_id: str, text: str, options: Dict[str, Any]):
    """在后台运行事实核查并通过WebSocket发送事件"""
    try:
        logger.info(f"开始AGUI事实核查，任务ID: {task_id}")
        
        # 添加WebSocket连接作为事件处理器
        async def websocket_event_handler(event_data: Dict[str, Any]):
            await connection_manager.send_message(client_id, event_data)
        
        # 临时添加事件处理器
        agui_event_sender.add_event_handler(websocket_event_handler)
        
        try:
            # 运行事实核查工作流
            workflow = get_fact_check_workflow()
            final_state = await workflow.run_fact_check(
                task_id=task_id,
                text=text,
                options={
                    "enable_financial_verification": options.get("enableFinancialVerification", True),
                    "enable_corporate_verification": options.get("enableCorporateVerification", True),
                    "enable_news_verification": options.get("enableNewsVerification", True),
                    "deep_research_mode": options.get("deepResearchMode", False)
                }
            )
            
            # 发送完成事件
            await connection_manager.send_message(client_id, {
                "type": "fact_check_task_complete",
                "taskId": task_id,
                "result": final_state.get("final_report"),
                "message": "事实核查任务完成"
            })
            
        finally:
            # 移除事件处理器
            agui_event_sender.remove_event_handler(websocket_event_handler)
        
        logger.info(f"AGUI事实核查任务完成，任务ID: {task_id}")
        
    except Exception as e:
        error_msg = f"AGUI事实核查任务执行失败: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        await connection_manager.send_message(client_id, {
            "type": "fact_check_error",
            "taskId": task_id,
            "error": error_msg
        })

# 为了兼容性，也提供一个根路径的POST端点
@router.post("/")
async def agui_http_endpoint():
    """AGUI HTTP端点 - 重定向到WebSocket"""
    return {
        "message": "请使用WebSocket连接进行AGUI通信",
        "websocket_url": "/api/v1/agui/ws/{client_id}",
        "example": "ws://localhost:8000/api/v1/agui/ws/your_client_id"
    }
