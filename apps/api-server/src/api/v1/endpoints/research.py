"""研究分析API端点 - 重构后的版本化API"""
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator, Optional
from dependency_injector.wiring import inject, Provide

from ....application.use_cases.research_facade import ResearchFacade
from ....application.dtos.research_dtos import (
    CreateResearchPipelineRequest,
    AddTaskRequest,
    PipelineInfo,
    TaskInfo,
    StartTaskRequest,
    CompleteTaskRequest,
    TaskResult
)
from ....core.container import MainContainer
from ....core.exceptions import ApplicationException
from ....infrastructure.services.logging import get_logger
from ..deps import get_current_user

router = APIRouter(prefix="/research", tags=["research"])
logger = get_logger(__name__)


@router.post("/pipelines", response_model=PipelineInfo)
@inject
async def create_research_pipeline(
    request: CreateResearchPipelineRequest,
    research_use_case: ResearchFacade = Depends(Provide[MainContainer.application.research_use_case]),
    current_user: str = Depends(get_current_user)
):
    """创建研究流水线"""
    try:
        # 设置用户ID
        request.user_id = current_user
        
        pipeline_info = await research_use_case.create_research_pipeline(request)
        
        logger.info(
            "Research pipeline created",
            pipeline_id=pipeline_info.pipeline_id,
            user_id=current_user
        )
        
        return pipeline_info
        
    except ApplicationException as e:
        logger.error("Failed to create research pipeline", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in create_research_pipeline", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/pipelines/{pipeline_id}/tasks", response_model=TaskInfo)
@inject
async def add_task_to_pipeline(
    pipeline_id: str,
    request: AddTaskRequest,
    research_use_case: ResearchFacade = Depends(Provide[MainContainer.application.research_use_case]),
    current_user: str = Depends(get_current_user)
):
    """向流水线添加任务"""
    try:
        # 验证流水线ID匹配
        if request.pipeline_id != pipeline_id:
            raise HTTPException(status_code=400, detail="Pipeline ID mismatch")
        
        task_info = await research_use_case.add_task_to_pipeline(request)
        
        logger.info(
            "Task added to pipeline",
            pipeline_id=pipeline_id,
            task_id=task_info.task_id,
            user_id=current_user
        )
        
        return task_info
        
    except ApplicationException as e:
        logger.error("Failed to add task to pipeline", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in add_task_to_pipeline", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/pipelines/{pipeline_id}/execute/stream")
@inject
async def execute_research_stream(
    pipeline_id: str,
    query: str,
    context: Optional[dict] = None,
    research_use_case: ResearchFacade = Depends(Provide[MainContainer.application.research_use_case]),
    current_user: str = Depends(get_current_user)
):
    """执行研究分析流式处理"""
    try:
        async def event_generator() -> AsyncGenerator[str, None]:
            async for event in research_use_case.execute_research_stream(
                pipeline_id=pipeline_id,
                query=query,
                context=context
            ):
                yield f"data: {event}\n\n"
        
        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Pipeline-ID": pipeline_id,
            }
        )
        
    except ApplicationException as e:
        logger.error("Failed to execute research stream", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in execute_research_stream", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/pipelines/{pipeline_id}/start", response_model=Optional[TaskInfo])
@inject
async def start_next_task(
    pipeline_id: str,
    research_use_case: ResearchFacade = Depends(Provide[MainContainer.application.research_use_case]),
    current_user: str = Depends(get_current_user)
):
    """开始下一个任务"""
    try:
        request = StartTaskRequest(pipeline_id=pipeline_id)
        task_info = await research_use_case.start_next_task(request)
        
        if task_info:
            logger.info(
                "Task started",
                pipeline_id=pipeline_id,
                task_id=task_info.task_id,
                user_id=current_user
            )
        
        return task_info
        
    except ApplicationException as e:
        logger.error("Failed to start next task", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in start_next_task", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/tasks/{task_id}/complete", response_model=TaskResult)
@inject
async def complete_task(
    task_id: str,
    request: CompleteTaskRequest,
    research_use_case: ResearchFacade = Depends(Provide[MainContainer.application.research_use_case]),
    current_user: str = Depends(get_current_user)
):
    """完成任务"""
    try:
        # 验证任务ID匹配
        if request.task_id != task_id:
            raise HTTPException(status_code=400, detail="Task ID mismatch")
        
        task_result = await research_use_case.complete_task(request)
        
        logger.info(
            "Task completed",
            task_id=task_id,
            user_id=current_user
        )
        
        return task_result
        
    except ApplicationException as e:
        logger.error("Failed to complete task", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in complete_task", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/pipelines/{pipeline_id}", response_model=PipelineInfo)
@inject
async def get_pipeline_status(
    pipeline_id: str,
    research_use_case: ResearchFacade = Depends(Provide[MainContainer.application.research_use_case]),
    current_user: str = Depends(get_current_user)
):
    """获取流水线状态"""
    try:
        pipeline_info = await research_use_case.get_pipeline_status(
            pipeline_id=pipeline_id,
            user_id=current_user
        )
        
        return pipeline_info
        
    except ApplicationException as e:
        logger.error("Failed to get pipeline status", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in get_pipeline_status", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/tasks/{task_id}/result")
@inject
async def get_task_result(
    task_id: str,
    research_use_case: ResearchFacade = Depends(Provide[MainContainer.application.research_use_case]),
    current_user: str = Depends(get_current_user)
):
    """获取任务结果"""
    try:
        result = await research_use_case.get_task_result(task_id)
        
        if result is None:
            raise HTTPException(status_code=404, detail="Task result not found")
        
        return result
        
    except ApplicationException as e:
        logger.error("Failed to get task result", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in get_task_result", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")