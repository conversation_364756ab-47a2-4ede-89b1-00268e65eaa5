"""API层依赖注入 - 重构后的依赖管理"""
from fastapi import Depends, HTTPException, Head<PERSON>
from typing import Optional
from dependency_injector.wiring import inject, Provide

from ...core.container import MainContainer
from ...core.security import security_manager, api_key_manager, rate_limiter
from ...core.exceptions import UnauthorizedError, ForbiddenError
from ...infrastructure.services.logging import get_logger

logger = get_logger(__name__)


async def get_current_user(
    authorization: Optional[str] = Header(None),
    x_api_key: Optional[str] = Header(None)
) -> str:
    """获取当前用户 - 支持多种认证方式"""
    
    # API Key认证
    if x_api_key:
        if api_key_manager.is_valid_api_key(x_api_key):
            return f"api_user_{x_api_key[:8]}"
        else:
            logger.warning("Invalid API key provided", key_prefix=x_api_key[:8])
            raise HTTPException(status_code=401, detail="Invalid API key")
    
    # JWT Token认证
    if authorization:
        try:
            if not authorization.startswith("Bearer "):
                raise UnauthorizedError("Invalid authorization header format")
            
            token = authorization.split(" ")[1]
            payload = security_manager.verify_token(token)
            user_id = payload.get("sub")
            
            if not user_id:
                raise UnauthorizedError("Invalid token payload")
            
            return user_id
            
        except UnauthorizedError:
            logger.warning("Invalid JWT token provided")
            raise HTTPException(status_code=401, detail="Invalid or expired token")
        except Exception as e:
            logger.error("Token verification failed", error=str(e))
            raise HTTPException(status_code=401, detail="Authentication failed")
    
    # 匿名用户（开发环境允许）
    from ...core.settings import settings
    if settings.is_development:
        return "anonymous_user"
    
    raise HTTPException(status_code=401, detail="Authentication required")


async def check_rate_limit(
    user_id: str = Depends(get_current_user),
    x_forwarded_for: Optional[str] = Header(None)
) -> bool:
    """检查速率限制"""
    try:
        # 使用用户ID或IP地址作为限制标识符
        identifier = user_id if user_id != "anonymous_user" else x_forwarded_for or "unknown"
        
        # 检查速率限制
        is_allowed = rate_limiter.is_allowed(
            identifier=identifier,
            max_requests=100,  # 每小时100次请求
            window_minutes=60
        )
        
        if not is_allowed:
            logger.warning("Rate limit exceeded", identifier=identifier)
            raise HTTPException(
                status_code=429, 
                detail="Rate limit exceeded. Please try again later."
            )
        
        return True
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Rate limit check failed", error=str(e))
        # 失败时允许通过，避免影响服务可用性
        return True


async def validate_content_type(
    content_type: Optional[str] = Header(None)
) -> bool:
    """验证内容类型"""
    if content_type and not content_type.startswith("application/json"):
        raise HTTPException(
            status_code=415,
            detail="Unsupported Media Type. Use application/json."
        )
    return True


# 通用依赖组合
CommonDeps = [
    Depends(get_current_user),
    Depends(check_rate_limit),
    Depends(validate_content_type)
]


# 便捷的依赖注入函数
def get_authenticated_user() -> str:
    """获取认证用户的便捷函数"""
    return Depends(get_current_user)


def require_rate_limit() -> bool:
    """需要速率限制的便捷函数"""
    return Depends(check_rate_limit)


# 权限检查依赖
async def require_admin_role(
    current_user: str = Depends(get_current_user)
) -> str:
    """需要管理员角色"""
    # 简单的角色检查逻辑，实际应用中应从数据库或配置中获取
    admin_users = ["admin", "root", "administrator"]
    
    if current_user not in admin_users and not current_user.startswith("api_user_"):
        logger.warning("Access denied for non-admin user", user=current_user)
        raise HTTPException(status_code=403, detail="Admin role required")
    
    return current_user


async def require_user_context(
    current_user: str = Depends(get_current_user)
) -> dict:
    """获取用户上下文信息"""
    return {
        "user_id": current_user,
        "is_authenticated": current_user != "anonymous_user",
        "is_admin": current_user.startswith("admin") or current_user.startswith("api_user_"),
        "permissions": ["read", "write"] if current_user != "anonymous_user" else ["read"]
    }