# -*- coding: utf-8 -*-
"""
投资研究分析代理
基于 AGUIAdapter 和 LangGraph 的实现
"""

import logging
import uuid
from typing import Optional

from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from .agui_adapter import AGUIAdapter
from .models import Task
from ..demo.graph import graph
from ..demo.configuration import get_configuration

# 设置日志
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="投资研究分析代理",
    description="基于 AG-UI 协议的投资研究分析服务",
    version="2.0.0"
)


class ResearchRequest(BaseModel):
    """研究请求模型"""
    
    query: str = Field(
        ...,
        description="投资研究查询内容",
        min_length=1,
        max_length=10000,
        example="请分析特斯拉(TSLA)的投资价值"
    )
    thread_id: Optional[str] = Field(
        None,
        description="对话线程ID，用于多轮对话上下文追踪",
        example="thread_abc123"
    )


# 全局变量：投资分析图和适配器
_analysis_graph = None
_agui_adapter = None


def get_analysis_graph():
    """获取投资分析图实例（单例模式）"""
    global _analysis_graph
    if _analysis_graph is None:
        try:
            _analysis_graph = graph
            logger.info("Investment analysis graph created successfully")
        except Exception as e:
            logger.error(f"Failed to create investment analysis graph: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to initialize analysis graph: {str(e)}"
            )
    return _analysis_graph


def get_agui_adapter():
    """获取 AGUI 适配器实例（单例模式）"""
    global _agui_adapter
    if _agui_adapter is None:
        try:
            graph = get_analysis_graph()
            _agui_adapter = AGUIAdapter(graph)
            logger.info("AGUI adapter created successfully")
        except Exception as e:
            logger.error(f"Failed to create AGUI adapter: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to initialize AGUI adapter: {str(e)}"
            )
    return _agui_adapter


@app.post("/research/stream")
async def research_stream(request: ResearchRequest):
    """
    投资研究分析流式端点
    
    接收投资研究查询，返回实时分析结果流
    """
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建任务对象
        task = Task(
            id=task_id,
            query=request.query,
            thread_id=request.thread_id
        )
        
        logger.info(
            f"Starting research analysis for task {task_id}: {request.query[:100]}..."
        )
        
        # 获取适配器实例
        adapter = get_agui_adapter()
        
        # 创建流式响应
        async def event_stream():
            try:
                async for event_data in adapter.create_official_stream(task):
                    yield event_data
            except Exception as e:
                logger.exception(f"Error in event stream for task {task_id}: {e}")
                # 发送错误事件
                error_event = f'data: {{"type": "RUN_ERROR", "message": "{str(e)}"}}\n\n'
                yield error_event
        
        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )
        
    except Exception as e:
        logger.exception(f"Failed to start research analysis: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start research analysis: {str(e)}"
        )


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查分析图是否可用
        graph = get_analysis_graph()
        adapter = get_agui_adapter()
        
        return {
            "status": "healthy",
            "service": "investment-research-agent",
            "version": "2.0.0",
            "graph_type": type(graph).__name__,
            "adapter_type": type(adapter).__name__
        }
    except Exception as e:
        logger.exception(f"Health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Service unhealthy: {str(e)}"
        )


@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "投资研究分析代理服务",
        "description": "基于 LangGraph 和 AG-UI 协议的投资研究分析服务",
        "version": "2.0.0",
        "endpoints": {
            "research_stream": "/research/stream",
            "health": "/health"
        }
    }


if __name__ == "__main__":
    import uvicorn
    
    logger.info("Starting Investment Research Agent...")
    uvicorn.run(
        "research_agent:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )