"""配置管理模块 - 基于 yai-loguru-sinks"""
import os
from pathlib import Path
from dotenv import load_dotenv

def load_environment():
    """加载环境变量配置"""
    # 加载 .env 文件
    env_path = Path(__file__).parent.parent / ".env"
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✅ 已加载环境变量文件: {env_path}")
    else:
        print(f"⚠️ 环境变量文件不存在: {env_path}")
    
    # 验证必要的环境变量
    required_vars = [
        "SLS_ACCESS_KEY_ID",
        "SLS_ACCESS_KEY_SECRET", 
        "SLS_PROJECT",
        "SLS_LOGSTORE"
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        print(f"⚠️ 缺少环境变量: {', '.join(missing_vars)}")
        return False
    
    return True

def setup_logging():
    """设置日志配置"""
    from yai_loguru_sinks import register_protocol_parsers, create_config_from_file
    
    # 注册协议解析器
    register_protocol_parsers()
    
    # 加载日志配置
    config_path = Path(__file__).parent.parent / "logging.yaml"
    create_config_from_file(str(config_path))
    
    print("✅ 日志系统初始化完成")

def check_sls_config():
    """检查 SLS 配置是否完整"""
    sls_enabled = os.getenv("SLS_ENABLED", "false").lower() == "true"
    if not sls_enabled:
        return False
    
    required_vars = [
        "SLS_ACCESS_KEY_ID",
        "SLS_ACCESS_KEY_SECRET",
        "SLS_PROJECT", 
        "SLS_LOGSTORE",
        "SLS_REGION"
    ]
    
    return all(os.getenv(var) for var in required_vars)
