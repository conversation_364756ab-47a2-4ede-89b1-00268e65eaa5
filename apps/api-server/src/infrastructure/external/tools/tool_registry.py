"""工具注册中心 - 管理所有外部工具"""
from typing import Dict, List, Type
from langchain.tools import BaseTool

from .financials.financials_tool import FinancialsTool
from .news.news_search_tool import NewsSearchTool


class ToolRegistry:
    """工具注册中心"""
    
    def __init__(self):
        self._tools: Dict[str, Type[BaseTool]] = {}
        self._tool_instances: Dict[str, BaseTool] = {}
        self._initialize_default_tools()
    
    def _initialize_default_tools(self) -> None:
        """初始化默认工具"""
        self.register_tool("financials", FinancialsTool)
        self.register_tool("news_search", NewsSearchTool)
    
    def register_tool(self, name: str, tool_class: Type[BaseTool]) -> None:
        """注册工具类"""
        self._tools[name] = tool_class
        # 清除已缓存的实例，下次获取时重新创建
        if name in self._tool_instances:
            del self._tool_instances[name]
    
    def get_tool(self, name: str) -> BaseTool:
        """获取工具实例（单例模式）"""
        if name not in self._tool_instances:
            if name not in self._tools:
                raise ValueError(f"Tool '{name}' not registered")
            
            tool_class = self._tools[name]
            self._tool_instances[name] = tool_class()
        
        return self._tool_instances[name]
    
    def get_all_tools(self) -> List[BaseTool]:
        """获取所有工具实例"""
        return [self.get_tool(name) for name in self._tools.keys()]
    
    def get_tool_names(self) -> List[str]:
        """获取所有注册的工具名称"""
        return list(self._tools.keys())
    
    def unregister_tool(self, name: str) -> bool:
        """注销工具"""
        if name in self._tools:
            del self._tools[name]
            if name in self._tool_instances:
                del self._tool_instances[name]
            return True
        return False
    
    def clear_all(self) -> None:
        """清除所有工具"""
        self._tools.clear()
        self._tool_instances.clear()
    
    def get_tool_info(self, name: str) -> Dict[str, str]:
        """获取工具信息"""
        if name not in self._tools:
            raise ValueError(f"Tool '{name}' not registered")
        
        tool_instance = self.get_tool(name)
        return {
            "name": tool_instance.name,
            "description": tool_instance.description,
            "class": self._tools[name].__name__
        }
    
    def get_all_tools_info(self) -> List[Dict[str, str]]:
        """获取所有工具信息"""
        return [self.get_tool_info(name) for name in self._tools.keys()]


# 全局工具注册中心实例
tool_registry = ToolRegistry()