"""重构后的新闻搜索工具核心"""
import os
from langchain.tools import BaseTool
from .search_provider import NewsSearchProvider
from .formatter import NewsFormatter


class NewsSearchTool(BaseTool):
    """搜索投资相关新闻的工具"""
    
    name: str = "news_search"
    description: str = """
    搜索投资相关新闻和市场动态。
    
    参数:
    - query: 搜索关键词 (公司名、股票代码、行业等)
    - market: 市场类型 (stock, crypto, forex, commodity)
    - time_filter: 时间筛选 (day, week, month)
    - count: 返回结果数量 (默认5条)
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.bing_api_key = os.getenv("BING_SEARCH_API_KEY")
        self.search_provider = NewsSearchProvider(self.bing_api_key)
        self.formatter = NewsFormatter()
    
    def _run(
        self, 
        query: str,
        market: str = "stock",
        time_filter: str = "week",
        count: int = 5
    ) -> str:
        """执行新闻搜索"""
        try:
            # 使用搜索提供者获取新闻数据
            news_data = self.search_provider.search_news(query, market, time_filter, count)
            
            # 使用格式化器格式化响应
            return self.formatter.format_response(news_data, query, market)
            
        except Exception as e:
            return f"搜索新闻时出错: {str(e)}"