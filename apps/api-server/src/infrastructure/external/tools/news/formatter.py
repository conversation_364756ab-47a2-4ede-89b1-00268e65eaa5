"""新闻数据格式化器"""
import json
from typing import Dict, Any, List
from datetime import datetime


class NewsFormatter:
    """新闻数据格式化器"""
    
    def format_response(self, news_data: Dict[str, Any], query: str, market: str) -> str:
        """格式化新闻搜索响应"""
        articles = news_data.get("value", [])
        
        result = {
            "query": query,
            "market": market,
            "total_results": news_data.get("totalEstimatedMatches", len(articles)),
            "search_url": news_data.get("webSearchUrl", ""),
            "articles": []
        }
        
        for article in articles:
            formatted_article = self._format_single_article(article)
            result["articles"].append(formatted_article)
        
        # 添加新闻分析摘要
        result["analysis"] = self._generate_news_analysis(articles, query, market)
        
        return json.dumps(result, ensure_ascii=False, indent=2)
    
    def _format_single_article(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """格式化单篇新闻文章"""
        return {
            "title": article.get("name", ""),
            "description": article.get("description", ""),
            "url": article.get("url", ""),
            "published_time": article.get("datePublished", ""),
            "provider": article.get("provider", [{}])[0].get("name", "") if article.get("provider") else "",
            "image_url": article.get("image", {}).get("thumbnail", {}).get("contentUrl", "") if article.get("image") else "",
            "category": article.get("category", ""),
            "sentiment": self._analyze_sentiment(article.get("name", "") + " " + article.get("description", ""))
        }
    
    def _analyze_sentiment(self, text: str) -> str:
        """简单的情感分析"""
        text_lower = text.lower()
        
        positive_keywords = ["上涨", "增长", "利好", "突破", "创新", "成功", "盈利", "收益"]
        negative_keywords = ["下跌", "亏损", "风险", "危机", "失败", "问题", "困难", "担忧"]
        
        positive_count = sum(1 for keyword in positive_keywords if keyword in text_lower)
        negative_count = sum(1 for keyword in negative_keywords if keyword in text_lower)
        
        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"
    
    def _generate_news_analysis(self, articles: List[Dict[str, Any]], query: str, market: str) -> Dict[str, Any]:
        """生成新闻分析摘要"""
        if not articles:
            return {"sentiment": "neutral", "key_themes": [], "summary": "暂无相关新闻"}
        
        # 统计情感倾向
        sentiments = []
        for article in articles:
            text = article.get("name", "") + " " + article.get("description", "")
            sentiment = self._analyze_sentiment(text)
            sentiments.append(sentiment)
        
        positive_count = sentiments.count("positive")
        negative_count = sentiments.count("negative")
        neutral_count = sentiments.count("neutral")
        
        overall_sentiment = "neutral"
        if positive_count > negative_count:
            overall_sentiment = "positive"
        elif negative_count > positive_count:
            overall_sentiment = "negative"
        
        # 提取关键主题
        key_themes = self._extract_key_themes(articles, market)
        
        # 生成摘要
        summary = self._generate_summary(overall_sentiment, len(articles), key_themes)
        
        return {
            "overall_sentiment": overall_sentiment,
            "sentiment_distribution": {
                "positive": positive_count,
                "negative": negative_count,
                "neutral": neutral_count
            },
            "key_themes": key_themes,
            "summary": summary,
            "article_count": len(articles)
        }
    
    def _extract_key_themes(self, articles: List[Dict[str, Any]], market: str) -> List[str]:
        """提取关键主题"""
        themes = []
        
        # 基于市场类型和文章内容提取主题
        all_text = " ".join([
            article.get("name", "") + " " + article.get("description", "")
            for article in articles
        ]).lower()
        
        if market == "stock":
            stock_themes = [
                ("财报", "财报业绩"),
                ("收购", "并购重组"),
                ("新产品", "产品发布"),
                ("政策", "政策影响"),
                ("竞争", "市场竞争")
            ]
            for keyword, theme in stock_themes:
                if keyword in all_text:
                    themes.append(theme)
        
        elif market == "crypto":
            crypto_themes = [
                ("监管", "监管政策"),
                ("挖矿", "挖矿动态"),
                ("交易所", "交易平台"),
                ("defi", "去中心化金融"),
                ("nft", "数字藏品")
            ]
            for keyword, theme in crypto_themes:
                if keyword in all_text:
                    themes.append(theme)
        
        return themes[:3]  # 返回前3个主要主题
    
    def _generate_summary(self, sentiment: str, article_count: int, themes: List[str]) -> str:
        """生成新闻分析摘要"""
        sentiment_desc = {
            "positive": "积极",
            "negative": "消极", 
            "neutral": "中性"
        }
        
        summary = f"共发现 {article_count} 条相关新闻，整体情感倾向为{sentiment_desc[sentiment]}。"
        
        if themes:
            summary += f" 主要关注的话题包括：{', '.join(themes)}。"
        
        return summary