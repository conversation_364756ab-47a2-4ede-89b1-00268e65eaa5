"""新闻搜索提供者"""
import requests
from typing import Dict, Any, List
from .mock_news_data import MOCK_NEWS_DATA


class NewsSearchProvider:
    """新闻搜索提供者"""
    
    def __init__(self, bing_api_key: str = None):
        self.bing_api_key = bing_api_key
        self.use_bing = bool(bing_api_key)
    
    def search_news(self, query: str, market: str, time_filter: str, count: int) -> Dict[str, Any]:
        """搜索新闻数据"""
        if self.use_bing:
            return self._search_with_bing(query, market, time_filter, count)
        else:
            return self._search_mock_news(query, market, time_filter, count)
    
    def _search_with_bing(self, query: str, market: str, time_filter: str, count: int) -> Dict[str, Any]:
        """使用Bing Search API搜索新闻"""
        endpoint = "https://api.bing.microsoft.com/v7.0/news/search"
        
        # 构建搜索查询
        search_terms = [query]
        if market == "stock":
            search_terms.extend(["股票", "投资", "财报", "业绩"])
        elif market == "crypto":
            search_terms.extend(["加密货币", "比特币", "区块链"])
        elif market == "forex":
            search_terms.extend(["外汇", "汇率", "央行"])
        elif market == "commodity":
            search_terms.extend(["大宗商品", "期货", "原油", "黄金"])
        
        search_query = " ".join(search_terms)
        
        headers = {
            "Ocp-Apim-Subscription-Key": self.bing_api_key,
            "Accept": "application/json"
        }
        
        params = {
            "q": search_query,
            "count": count,
            "mkt": "zh-CN",
            "category": "Business",
            "sortBy": "Date",
            "freshness": self._time_filter_to_freshness(time_filter)
        }
        
        response = requests.get(endpoint, headers=headers, params=params)
        response.raise_for_status()
        
        return response.json()
    
    def _search_mock_news(self, query: str, market: str, time_filter: str, count: int) -> Dict[str, Any]:
        """搜索模拟新闻数据"""
        # 基于查询词匹配模拟数据
        matched_articles = []
        
        for category, articles in MOCK_NEWS_DATA.items():
            if self._query_matches_category(query, category):
                matched_articles.extend(articles[:count])
        
        # 如果没有匹配的类别，返回通用新闻
        if not matched_articles:
            matched_articles = MOCK_NEWS_DATA.get("general", [])[:count]
        
        return {
            "value": matched_articles[:count],
            "totalEstimatedMatches": len(matched_articles),
            "webSearchUrl": f"https://www.bing.com/news/search?q={query}"
        }
    
    def _query_matches_category(self, query: str, category: str) -> bool:
        """检查查询是否匹配分类"""
        query_lower = query.lower()
        
        if category == "tesla":
            return any(keyword in query_lower for keyword in ["特斯拉", "tesla", "tsla"])
        elif category == "ai":
            return any(keyword in query_lower for keyword in ["人工智能", "ai", "chatgpt", "智能"])
        elif category == "crypto":
            return any(keyword in query_lower for keyword in ["比特币", "crypto", "bitcoin", "加密"])
        elif category == "tech":
            return any(keyword in query_lower for keyword in ["科技", "tech", "technology", "互联网"])
        
        return False
    
    def _time_filter_to_freshness(self, time_filter: str) -> str:
        """将时间筛选转换为Bing API的freshness参数"""
        mapping = {
            "day": "Day",
            "week": "Week", 
            "month": "Month"
        }
        return mapping.get(time_filter, "Week")