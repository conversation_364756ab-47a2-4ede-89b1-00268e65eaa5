"""模拟新闻数据"""
from datetime import datetime, timedelta

# 生成最近几天的时间戳
now = datetime.now()
yesterday = now - timedelta(days=1)
week_ago = now - timedelta(days=7)

MOCK_NEWS_DATA = {
    "tesla": [
        {
            "name": "特斯拉Q4财报超预期 电动车销量创历史新高",
            "description": "特斯拉公布2024年第四季度财报，营收和利润均超过分析师预期。全年电动车交付量达到180万辆，同比增长35%。",
            "url": "https://example.com/tesla-q4-earnings",
            "datePublished": now.isoformat(),
            "provider": [{"name": "财经新闻网"}],
            "category": "Business",
            "image": {"thumbnail": {"contentUrl": "https://example.com/tesla-image.jpg"}}
        },
        {
            "name": "特斯拉上海工厂产能再次提升 年产能达到100万辆",
            "description": "特斯拉上海超级工厂宣布产能升级完成，年产能从75万辆提升至100万辆，将进一步巩固其在中国市场的领先地位。",
            "url": "https://example.com/tesla-shanghai-capacity",
            "datePublished": yesterday.isoformat(),
            "provider": [{"name": "汽车之家"}],
            "category": "Business"
        }
    ],
    
    "ai": [
        {
            "name": "ChatGPT-5即将发布 AI能力再次突破",
            "description": "OpenAI宣布即将发布ChatGPT-5，新版本在推理能力和多模态处理方面有显著提升，预计将推动AI行业新一轮发展。",
            "url": "https://example.com/chatgpt-5-release",
            "datePublished": now.isoformat(),
            "provider": [{"name": "科技日报"}],
            "category": "Technology"
        },
        {
            "name": "百度文心一言用户突破1亿 国产AI模型崛起",
            "description": "百度宣布文心一言用户数突破1亿大关，成为国内最受欢迎的AI助手之一，显示出国产AI模型的快速发展。",
            "url": "https://example.com/wenxin-100m-users",
            "datePublished": yesterday.isoformat(),
            "provider": [{"name": "36氪"}],
            "category": "Technology"
        }
    ],
    
    "crypto": [
        {
            "name": "比特币突破7万美元 机构投资者大量入场",
            "description": "比特币价格突破7万美元大关，创下历史新高。分析师认为机构投资者的大量入场是推动价格上涨的主要因素。",
            "url": "https://example.com/bitcoin-70k",
            "datePublished": now.isoformat(),
            "provider": [{"name": "币圈快讯"}],
            "category": "Finance"
        },
        {
            "name": "以太坊2.0升级完成 网络性能大幅提升",
            "description": "以太坊完成最新的网络升级，交易速度提升3倍，手续费降低60%，为DeFi和NFT应用提供更好的基础设施。",
            "url": "https://example.com/ethereum-upgrade",
            "datePublished": week_ago.isoformat(),
            "provider": [{"name": "区块链资讯"}],
            "category": "Technology"
        }
    ],
    
    "tech": [
        {
            "name": "苹果发布Vision Pro 2代 AR技术再次升级",
            "description": "苹果正式发布Vision Pro 2代产品，重量减轻30%，续航时间延长至8小时，售价下调至2999美元。",
            "url": "https://example.com/apple-vision-pro-2",
            "datePublished": now.isoformat(),
            "provider": [{"name": "科技前沿"}],
            "category": "Technology"
        },
        {
            "name": "英伟达GPU短缺缓解 AI芯片供应链恢复正常",
            "description": "英伟达宣布GPU产能大幅提升，AI芯片短缺问题得到有效缓解，预计将推动AI行业发展速度加快。",
            "url": "https://example.com/nvidia-gpu-supply",
            "datePublished": yesterday.isoformat(),
            "provider": [{"name": "芯片观察"}],
            "category": "Technology"
        }
    ],
    
    "general": [
        {
            "name": "美联储维持利率不变 市场反应积极",
            "description": "美联储宣布维持当前利率水平不变，符合市场预期。会后声明显示通胀压力有所缓解，经济增长保持稳定。",
            "url": "https://example.com/fed-rate-decision",
            "datePublished": now.isoformat(),
            "provider": [{"name": "华尔街日报"}],
            "category": "Finance"
        },
        {
            "name": "中国GDP增长超预期 经济复苏势头强劲",
            "description": "国家统计局公布最新GDP数据，同比增长5.2%，超过预期的5.0%。消费和投资双双回暖，经济复苏势头良好。",
            "url": "https://example.com/china-gdp-growth",
            "datePublished": yesterday.isoformat(),
            "provider": [{"name": "经济参考报"}],
            "category": "Finance"
        }
    ]
}