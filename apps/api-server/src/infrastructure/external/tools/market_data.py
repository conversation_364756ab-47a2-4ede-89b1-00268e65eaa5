"""股票市场数据工具"""
import yfinance as yf
from typing import Dict, Any
from datetime import datetime
from langchain_core.tools import tool

@tool
def get_market_data(
    ticker: str,
    period: str = "1mo", 
    info_type: str = "all"
) -> str:
    """获取股票市场数据，包括实时价格、历史数据、财务指标等。
    
    Args:
        ticker: 股票代码 (如: AAPL, TSLA, MSFT)
        period: 时间周期 (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
        info_type: 信息类型 (price, history, info, stats, all)
        
    Returns:
        格式化的股票市场数据分析结果
    """
    try:
        # 清理ticker符号
        ticker = ticker.upper().strip()
        stock = yf.Ticker(ticker)
        
        result = {
            "ticker": ticker,
            "timestamp": datetime.now().isoformat(),
            "data": {}
        }
        
        if info_type in ["all", "info"]:
            # 获取公司基本信息
            info = stock.info
            result["data"]["company_info"] = {
                "name": info.get("longName", "N/A"),
                "sector": info.get("sector", "N/A"),
                "industry": info.get("industry", "N/A"),
                "country": info.get("country", "N/A"),
                "website": info.get("website", "N/A"),
                "summary": info.get("longBusinessSummary", "N/A")[:500] + "..." if info.get("longBusinessSummary") else "N/A"
            }
        
        if info_type in ["all", "price"]:
            # 获取当前价格信息
            info = stock.info
            result["data"]["current_price"] = {
                "price": info.get("currentPrice"),
                "previous_close": info.get("previousClose"),
                "open": info.get("open"),
                "day_high": info.get("dayHigh"),
                "day_low": info.get("dayLow"),
                "volume": info.get("volume"),
                "market_cap": info.get("marketCap"),
                "currency": info.get("currency", "USD")
            }
            
            # 计算涨跌幅
            current = info.get("currentPrice")
            previous = info.get("previousClose")
            if current and previous:
                change = current - previous
                change_percent = (change / previous) * 100
                result["data"]["current_price"]["change"] = round(change, 2)
                result["data"]["current_price"]["change_percent"] = round(change_percent, 2)
        
        if info_type in ["all", "stats"]:
            # 获取财务统计数据
            info = stock.info
            result["data"]["financial_stats"] = {
                "pe_ratio": info.get("trailingPE"),
                "forward_pe": info.get("forwardPE"),
                "peg_ratio": info.get("pegRatio"),
                "price_to_book": info.get("priceToBook"),
                "debt_to_equity": info.get("debtToEquity"),
                "return_on_equity": info.get("returnOnEquity"),
                "profit_margin": info.get("profitMargins"),
                "dividend_yield": info.get("dividendYield"),
                "beta": info.get("beta"),
                "52_week_high": info.get("fiftyTwoWeekHigh"),
                "52_week_low": info.get("fiftyTwoWeekLow")
            }
        
        if info_type in ["all", "history"]:
            # 获取历史价格数据
            hist = stock.history(period=period)
            if not hist.empty:
                # 只返回最近10个交易日的数据
                recent_hist = hist.tail(10)
                result["data"]["price_history"] = []
                
                for date, row in recent_hist.iterrows():
                    result["data"]["price_history"].append({
                        "date": date.strftime("%Y-%m-%d"),
                        "open": round(row["Open"], 2),
                        "high": round(row["High"], 2),
                        "low": round(row["Low"], 2),
                        "close": round(row["Close"], 2),
                        "volume": int(row["Volume"])
                    })
        
        # 格式化返回结果
        return _format_market_data_response(result)
        
    except Exception as e:
        return f"获取 {ticker} 市场数据时出错: {str(e)}"

def _format_market_data_response(data: Dict[str, Any]) -> str:
    """格式化市场数据响应"""
    ticker = data["ticker"]
    result_parts = [f"📊 **{ticker} 市场数据分析**\n"]
    
    # 公司信息
    if "company_info" in data["data"]:
        info = data["data"]["company_info"]
        result_parts.append(f"**🏢 公司信息**")
        result_parts.append(f"• 公司名称: {info['name']}")
        result_parts.append(f"• 行业: {info['sector']} - {info['industry']}")
        result_parts.append(f"• 国家: {info['country']}")
        if info['summary'] != "N/A":
            result_parts.append(f"• 简介: {info['summary']}")
        result_parts.append("")
    
    # 当前价格
    if "current_price" in data["data"]:
        price = data["data"]["current_price"]
        result_parts.append(f"**💰 当前价格 ({price.get('currency', 'USD')})**")
        result_parts.append(f"• 当前价格: ${price.get('price', 'N/A')}")
        
        if price.get('change') is not None:
            change_emoji = "📈" if price['change'] >= 0 else "📉"
            result_parts.append(f"• 涨跌: {change_emoji} ${price['change']} ({price['change_percent']:+.2f}%)")
        
        result_parts.append(f"• 今日范围: ${price.get('day_low', 'N/A')} - ${price.get('day_high', 'N/A')}")
        result_parts.append(f"• 成交量: {price.get('volume', 'N/A'):,}" if price.get('volume') else "• 成交量: N/A")
        
        if price.get('market_cap'):
            market_cap_b = price['market_cap'] / 1e9
            result_parts.append(f"• 市值: ${market_cap_b:.1f}B")
        result_parts.append("")
    
    # 财务统计
    if "financial_stats" in data["data"]:
        stats = data["data"]["financial_stats"]
        result_parts.append(f"**📈 关键财务指标**")
        
        if stats.get('pe_ratio'):
            result_parts.append(f"• 市盈率 (P/E): {stats['pe_ratio']:.2f}")
        if stats.get('price_to_book'):
            result_parts.append(f"• 市净率 (P/B): {stats['price_to_book']:.2f}")
        if stats.get('dividend_yield'):
            result_parts.append(f"• 股息收益率: {stats['dividend_yield']*100:.2f}%")
        if stats.get('beta'):
            result_parts.append(f"• Beta系数: {stats['beta']:.2f}")
        if stats.get('52_week_high') and stats.get('52_week_low'):
            result_parts.append(f"• 52周范围: ${stats['52_week_low']:.2f} - ${stats['52_week_high']:.2f}")
        result_parts.append("")
    
    # 价格历史
    if "price_history" in data["data"] and data["data"]["price_history"]:
        result_parts.append(f"**📊 近期价格走势**")
        history = data["data"]["price_history"]
        
        # 显示最近5个交易日
        for day in history[-5:]:
            date = day["date"]
            close = day["close"]
            volume_k = day["volume"] / 1000
            result_parts.append(f"• {date}: ${close} (成交量: {volume_k:.0f}K)")
        result_parts.append("")
    
    result_parts.append("---")
    result_parts.append("💡 *数据来源: Yahoo Finance，仅供参考，投资有风险*")
    
    return "\n".join(result_parts)