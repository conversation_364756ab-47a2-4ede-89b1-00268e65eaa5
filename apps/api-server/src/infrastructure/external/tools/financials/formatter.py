"""财务数据格式化器"""
from typing import Dict, Any
import json


class FinancialsFormatter:
    """财务数据格式化器"""
    
    def format_response(
        self, 
        data: Dict[str, Any], 
        ticker: str, 
        company_name: str, 
        statement_type: str, 
        period: str
    ) -> str:
        """格式化财务数据响应"""
        
        result = {
            "ticker": ticker,
            "company_name": company_name,
            "period": period,
            "data_source": "mock_data",
            "financial_data": {}
        }
        
        # 根据请求的报表类型过滤数据
        if statement_type == "all":
            result["financial_data"] = data
        elif statement_type == "income":
            result["financial_data"]["income_statement"] = data.get("income_statement", {})
        elif statement_type == "balance":
            result["financial_data"]["balance_sheet"] = data.get("balance_sheet", {})
        elif statement_type == "cashflow":
            result["financial_data"]["cash_flow"] = data.get("cash_flow", {})
        
        # 添加分析总结
        result["analysis"] = self._generate_analysis(data, ticker, company_name)
        
        return json.dumps(result, ensure_ascii=False, indent=2)
    
    def _generate_analysis(self, data: Dict[str, Any], ticker: str, company_name: str) -> Dict[str, Any]:
        """生成简单的财务分析"""
        analysis = {
            "revenue_trend": self._analyze_revenue_trend(data),
            "profitability": self._analyze_profitability(data),
            "financial_health": self._analyze_financial_health(data)
        }
        
        return analysis
    
    def _analyze_revenue_trend(self, data: Dict[str, Any]) -> str:
        """分析营收趋势"""
        income_data = data.get("income_statement", {})
        if len(income_data) < 2:
            return "数据不足，无法分析趋势"
        
        years = sorted(income_data.keys(), reverse=True)
        latest_revenue = income_data[years[0]].get("revenue", 0)
        previous_revenue = income_data[years[1]].get("revenue", 0)
        
        if previous_revenue > 0:
            growth_rate = ((latest_revenue - previous_revenue) / previous_revenue) * 100
            if growth_rate > 10:
                return f"营收强劲增长 ({growth_rate:.1f}%)"
            elif growth_rate > 0:
                return f"营收温和增长 ({growth_rate:.1f}%)"
            else:
                return f"营收下降 ({growth_rate:.1f}%)"
        
        return "营收趋势分析不可用"
    
    def _analyze_profitability(self, data: Dict[str, Any]) -> str:
        """分析盈利能力"""
        income_data = data.get("income_statement", {})
        if not income_data:
            return "盈利能力数据不可用"
        
        latest_year = max(income_data.keys())
        latest_data = income_data[latest_year]
        
        revenue = latest_data.get("revenue", 0)
        net_income = latest_data.get("net_income", 0)
        
        if revenue > 0:
            profit_margin = (net_income / revenue) * 100
            if profit_margin > 20:
                return f"盈利能力优秀 (净利率: {profit_margin:.1f}%)"
            elif profit_margin > 10:
                return f"盈利能力良好 (净利率: {profit_margin:.1f}%)"
            elif profit_margin > 0:
                return f"盈利能力一般 (净利率: {profit_margin:.1f}%)"
            else:
                return "处于亏损状态"
        
        return "盈利能力分析不可用"
    
    def _analyze_financial_health(self, data: Dict[str, Any]) -> str:
        """分析财务健康状况"""
        balance_data = data.get("balance_sheet", {})
        if not balance_data:
            return "财务健康数据不可用"
        
        latest_year = max(balance_data.keys())
        latest_data = balance_data[latest_year]
        
        total_assets = latest_data.get("total_assets", 0)
        total_debt = latest_data.get("total_debt", 0)
        cash = latest_data.get("cash_and_equivalents", 0)
        
        if total_assets > 0:
            debt_ratio = (total_debt / total_assets) * 100
            cash_ratio = (cash / total_assets) * 100
            
            if debt_ratio < 30 and cash_ratio > 10:
                return f"财务状况健康 (负债率: {debt_ratio:.1f}%, 现金比率: {cash_ratio:.1f}%)"
            elif debt_ratio < 50:
                return f"财务状况良好 (负债率: {debt_ratio:.1f}%, 现金比率: {cash_ratio:.1f}%)"
            else:
                return f"财务杠杆较高 (负债率: {debt_ratio:.1f}%, 现金比率: {cash_ratio:.1f}%)"
        
        return "财务健康分析不可用"