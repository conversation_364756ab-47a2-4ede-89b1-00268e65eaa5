"""模拟财务数据提供者"""
from typing import Dict, Any, <PERSON><PERSON>
from .mock_data import MOCK_FINANCIAL_DATA


class MockDataProvider:
    """财务模拟数据提供者"""
    
    def get_financial_data(self, ticker: str, period: str, years: int) -> Tuple[str, Dict[str, Any]]:
        """获取指定公司的财务数据
        
        Returns:
            Tuple[公司名称, 财务数据]
        """
        # 基于ticker获取对应的模拟数据
        if ticker in MOCK_FINANCIAL_DATA:
            company_data = MOCK_FINANCIAL_DATA[ticker]
            company_name = company_data["company_name"]
            mock_data = company_data["financial_data"]
        else:
            company_name = f"{ticker} 公司"
            mock_data = self._get_generic_mock_data(ticker, period, years)
        
        return company_name, mock_data
    
    def _get_generic_mock_data(self, ticker: str, period: str, years: int) -> Dict[str, Any]:
        """为未知ticker生成通用模拟数据"""
        base_revenue = **********  # 10亿基准
        
        # 基于ticker字符生成种子
        seed = sum(ord(c) for c in ticker) % 100
        multiplier = 1 + (seed / 100)
        
        generic_data = {
            "income_statement": {},
            "balance_sheet": {},
            "cash_flow": {}
        }
        
        # 生成最近几年的数据
        for i in range(years):
            year = str(2023 - i)
            year_multiplier = 1 + (i * 0.05)  # 每年增长5%
            
            revenue = int(base_revenue * multiplier * year_multiplier)
            
            generic_data["income_statement"][year] = {
                "revenue": revenue,
                "gross_profit": int(revenue * 0.4),
                "operating_income": int(revenue * 0.25),
                "net_income": int(revenue * 0.15),
                "eps": round((revenue * 0.15) / **********, 2)
            }
            
            generic_data["balance_sheet"][year] = {
                "total_assets": int(revenue * 1.5),
                "total_liabilities": int(revenue * 0.8),
                "total_equity": int(revenue * 0.7),
                "cash_and_equivalents": int(revenue * 0.2),
                "total_debt": int(revenue * 0.3)
            }
            
            generic_data["cash_flow"][year] = {
                "operating_cash_flow": int(revenue * 0.2),
                "investing_cash_flow": int(revenue * -0.1),
                "financing_cash_flow": int(revenue * -0.05),
                "free_cash_flow": int(revenue * 0.15)
            }
        
        return generic_data