"""重构后的财务工具核心"""
from typing import Dict, Any
from langchain.tools import BaseTool
from .mock_data_provider import MockDataProvider
from .formatter import FinancialsFormatter


class FinancialsTool(BaseTool):
    """获取公司财务报表数据的工具"""
    
    name: str = "financials"
    description: str = """
    获取公司财务报表数据，包括损益表、资产负债表、现金流量表等。
    
    参数:
    - ticker: 股票代码 (如: AAPL, TSLA, MSFT)
    - statement_type: 报表类型 (income, balance, cashflow, all)
    - period: 时间周期 (annual, quarterly)
    - years: 获取年份数量 (默认3年)
    """
    
    def __init__(self):
        super().__init__()
        self.data_provider = MockDataProvider()
        self.formatter = FinancialsFormatter()
    
    def _run(
        self, 
        ticker: str,
        statement_type: str = "all",
        period: str = "annual",
        years: int = 3
    ) -> str:
        """执行财务数据获取"""
        try:
            # 清理ticker符号
            ticker = ticker.upper().strip()
            
            # 获取模拟财务数据
            company_name, mock_data = self.data_provider.get_financial_data(ticker, period, years)
            
            # 格式化响应
            return self.formatter.format_response(
                mock_data, ticker, company_name, statement_type, period
            )
            
        except Exception as e:
            return f"获取 {ticker} 财务数据时出错: {str(e)}"