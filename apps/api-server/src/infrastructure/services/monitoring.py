"""监控和追踪配置模块"""
import os
from typing import Optional
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter

# 全局tracer实例
_tracer: Optional[trace.Tracer] = None

def configure_tracing(service_name: str = "investor-insight") -> trace.Tracer:
    """配置分布式追踪"""
    global _tracer
    
    if _tracer is not None:
        return _tracer
    
    # 设置TracerProvider
    trace.set_tracer_provider(TracerProvider())
    tracer_provider = trace.get_tracer_provider()
    
    # 配置导出器 (开发环境使用控制台导出)
    if os.getenv("ENVIRONMENT") == "development":
        console_exporter = ConsoleSpanExporter()
        span_processor = BatchSpanProcessor(console_exporter)
        tracer_provider.add_span_processor(span_processor)
    
    # 可选：配置Jaeger导出器 (生产环境)
    jaeger_host = os.getenv("JAEGER_AGENT_HOST")
    jaeger_port = os.getenv("JAEGER_AGENT_PORT")
    
    if jaeger_host and jaeger_port:
        try:
            from opentelemetry.exporter.jaeger.thrift import JaegerExporter
            jaeger_exporter = JaegerExporter(
                agent_host_name=jaeger_host,
                agent_port=int(jaeger_port)
            )
            jaeger_processor = BatchSpanProcessor(jaeger_exporter)
            tracer_provider.add_span_processor(jaeger_processor)
        except ImportError:
            # Jaeger导出器未安装，忽略
            pass
    
    _tracer = trace.get_tracer(service_name)
    return _tracer

def get_tracer() -> trace.Tracer:
    """获取tracer实例"""
    if _tracer is None:
        return configure_tracing()
    return _tracer