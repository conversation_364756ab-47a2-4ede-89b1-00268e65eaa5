"""监控日志模块 - 基于 yai-nexus-logger 的增强版本"""
from yai_nexus_logger import init_logging, get_logger as get_nexus_logger, LoggerConfigurator
import os
from typing import Optional

from ...core.settings import settings


def configure_logging(log_level: str = None) -> None:
    """配置结构化日志 - 使用 yai-nexus-logger"""
    level = log_level or settings.monitoring.log_level
    app_name = "investor-insight"

    # 设置环境变量以便 yai-nexus-logger 使用
    os.environ.setdefault("LOG_APP_NAME", app_name)
    os.environ.setdefault("LOG_LEVEL", level)
    os.environ.setdefault("LOG_CONSOLE_ENABLED", "true")
    os.environ.setdefault("LOG_UVICORN_INTEGRATION_ENABLED", "true")

    # 根据环境设置文件日志
    if settings.environment == "production":
        os.environ.setdefault("LOG_FILE_ENABLED", "true")
        os.environ.setdefault("LOG_FILE_PATH", f"logs/{app_name}.log")

    # 使用环境变量配置初始化
    init_logging()


def get_logger(name: str):
    """获取结构化日志器 - 兼容原有接口"""
    return get_nexus_logger(name)


# 在模块导入时自动配置日志
configure_logging()