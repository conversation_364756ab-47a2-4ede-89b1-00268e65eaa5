import os
from typing import Optional, Dict, Any
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain_core.language_models import BaseLanguageModel
from langchain_core.callbacks import BaseCallbackHandler
from ..adapters.cost_tracker import cost_tracker

load_dotenv()

class CostTrackingCallbackHandler(BaseCallbackHandler):
    """成本追踪回调处理器"""
    
    def __init__(self, model_name: str, task_type: Optional[str] = None):
        self.model_name = model_name
        self.task_type = task_type
        self.input_tokens = 0
        self.output_tokens = 0
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: list[str], **kwargs) -> None:
        # 估算输入token数（粗略估算：1个字符≈0.25个token）
        total_chars = sum(len(prompt) for prompt in prompts)
        self.input_tokens = int(total_chars * 0.25)
    
    def on_llm_end(self, response, **kwargs) -> None:
        # 估算输出token数
        if hasattr(response, 'generations') and response.generations:
            output_text = "".join(
                gen.text for generation in response.generations 
                for gen in generation
            )
            self.output_tokens = int(len(output_text) * 0.25)
        
        # 记录成本
        cost_tracker.record_call(
            model=self.model_name,
            input_tokens=self.input_tokens,
            output_tokens=self.output_tokens,
            task_type=self.task_type
        )

class LLMFactory:
    """LLM工厂类，用于创建和管理不同的语言模型"""
    
    # OpenRouter支持的Claude模型
    OPENROUTER_CLAUDE_MODELS = {
        "claude-4": "anthropic/claude-3-5-sonnet",
        "claude-3.5-sonnet": "anthropic/claude-3-5-sonnet",
        "claude-3.5-haiku": "anthropic/claude-3-5-haiku",
        "claude-3-opus": "anthropic/claude-3-opus"
    }
    
    @staticmethod
    def create_openai_llm(
        model: str = "gpt-4o-mini",
        temperature: float = 0.3,
        max_tokens: Optional[int] = None
    ) -> ChatOpenAI:
        """创建OpenAI LLM实例"""
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is not set")
        
        return ChatOpenAI(
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            api_key=api_key,
            streaming=True  # 启用流式响应
        )
    
    @staticmethod
    def create_anthropic_llm(
        model: str = "claude-3-5-sonnet-20241022",
        temperature: float = 0.3,
        max_tokens: Optional[int] = None
    ) -> ChatAnthropic:
        """创建Anthropic LLM实例"""
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if not api_key:
            raise ValueError("ANTHROPIC_API_KEY environment variable is not set")
        
        return ChatAnthropic(
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            api_key=api_key,
            streaming=True  # 启用流式响应
        )
    
    @staticmethod
    def create_openrouter_llm(
        model: str = "anthropic/claude-3-5-sonnet",
        temperature: float = 0.1,
        max_tokens: Optional[int] = 4000,
        task_type: Optional[str] = None
    ) -> ChatOpenAI:
        """创建OpenRouter LLM实例（使用ChatOpenAI with custom base_url）"""
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY environment variable is not set")
        
        llm = ChatOpenAI(
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            api_key=api_key,
            base_url="https://openrouter.ai/api/v1",
            streaming=True,
            model_kwargs={
                "extra_headers": {
                    "HTTP-Referer": "https://yai-investor-insight.com",  # 替换为实际域名
                    "X-Title": "YAI Investor Insight Platform"
                }
            },
            callbacks=[CostTrackingCallbackHandler(model, task_type)]
        )
        return llm
    
    @staticmethod
    def create_claude4_llm(
        temperature: float = 0.1,
        max_tokens: Optional[int] = 4000,
        task_type: Optional[str] = None
    ) -> ChatOpenAI:
        """创建Claude 4 LLM实例（通过OpenRouter）"""
        return LLMFactory.create_openrouter_llm(
            model="anthropic/claude-3-5-sonnet",  # OpenRouter上最新的Claude模型
            temperature=temperature,
            max_tokens=max_tokens,
            task_type=task_type
        )
    
    @staticmethod
    def create_default_llm() -> BaseLanguageModel:
        """创建默认LLM实例（优先使用OpenRouter Claude 4，fallback到其他）"""
        try:
            # 优先使用OpenRouter Claude 4（成本更低）
            return LLMFactory.create_claude4_llm()
        except ValueError:
            try:
                return LLMFactory.create_openai_llm()
            except ValueError:
                try:
                    return LLMFactory.create_anthropic_llm()
                except ValueError:
                    raise ValueError(
                        "No LLM API keys found. Please set OPENROUTER_API_KEY, OPENAI_API_KEY, or ANTHROPIC_API_KEY"
                    )
    
    @staticmethod
    def get_available_models() -> dict:
        """获取可用的模型列表"""
        return {
            "openrouter": [
                "anthropic/claude-3-5-sonnet",
                "anthropic/claude-3-5-haiku", 
                "anthropic/claude-3-opus",
                "openai/gpt-4o",
                "openai/gpt-4o-mini"
            ],
            "openai": [
                "gpt-4o",
                "gpt-4o-mini", 
                "gpt-4-turbo",
                "gpt-3.5-turbo"
            ],
            "anthropic": [
                "claude-3-5-sonnet-20241022",
                "claude-3-5-haiku-20241022",
                "claude-3-opus-20240229"
            ]
        }