"""日志配置模块 - 基于 yai-loguru-sinks"""
from loguru import logger
from src.config import load_environment, setup_logging, check_sls_config
import os

def configure_logging(log_level: str = "INFO") -> None:
    """配置结构化日志 - 使用 yai-loguru-sinks"""
    # 加载环境变量
    if not load_environment():
        print("❌ 环境变量配置有误，使用默认日志配置")

    # 设置日志系统
    setup_logging()

    # 检查 SLS 配置
    sls_available = check_sls_config()
    if sls_available:
        logger.info("SLS 功能已启用，DEBUG 级别日志将上报到阿里云")
        logger.info("PackId 功能已启用 - 相关日志将自动分组")
    else:
        logger.info("SLS 功能未启用，仅使用本地日志")

def get_logger(name: str):
    """获取结构化日志器 - 兼容原有接口"""
    # 返回 loguru logger，它会自动使用配置的 handlers
    return logger

def configure_tracing(service_name: str) -> None:
    """配置分布式追踪"""
    logger.info("Tracing configured", extra={"service": service_name})