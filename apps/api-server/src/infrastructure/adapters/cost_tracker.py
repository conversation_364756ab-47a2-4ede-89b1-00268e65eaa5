"""
成本追踪和优化模块
用于监控LLM调用成本并提供优化建议
"""
import time
from typing import Dict, Optional, List
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ModelProvider(Enum):
    OPENROUTER = "openrouter"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"

@dataclass
class ModelCost:
    """模型成本配置"""
    input_cost_per_1k: float  # 每1k输入token的成本（USD）
    output_cost_per_1k: float  # 每1k输出token的成本（USD）
    provider: ModelProvider

@dataclass 
class CallRecord:
    """单次调用记录"""
    model: str
    provider: ModelProvider
    input_tokens: int
    output_tokens: int
    cost: float
    timestamp: float = field(default_factory=time.time)
    task_type: Optional[str] = None

class CostTracker:
    """成本追踪器"""
    
    # 模型成本配置（2025年1月价格）
    MODEL_COSTS = {
        # OpenRouter价格（通常比直接API便宜20-30%）
        "anthropic/claude-3-5-sonnet": ModelCost(3.0/1000, 15.0/1000, ModelProvider.OPENROUTER),
        "anthropic/claude-3-5-haiku": ModelCost(1.0/1000, 5.0/1000, ModelProvider.OPENROUTER),
        "anthropic/claude-3-opus": ModelCost(15.0/1000, 75.0/1000, ModelProvider.OPENROUTER),
        "openai/gpt-4o": ModelCost(5.0/1000, 15.0/1000, ModelProvider.OPENROUTER),
        "openai/gpt-4o-mini": ModelCost(0.15/1000, 0.6/1000, ModelProvider.OPENROUTER),
        
        # 直接API价格
        "gpt-4o": ModelCost(5.0/1000, 15.0/1000, ModelProvider.OPENAI),
        "gpt-4o-mini": ModelCost(0.15/1000, 0.6/1000, ModelProvider.OPENAI),
        "claude-3-5-sonnet-20241022": ModelCost(3.0/1000, 15.0/1000, ModelProvider.ANTHROPIC),
        "claude-3-5-haiku-20241022": ModelCost(1.0/1000, 5.0/1000, ModelProvider.ANTHROPIC),
    }
    
    def __init__(self):
        self.call_history: List[CallRecord] = []
        self.daily_budget = 50.0  # 每日预算$50
        
    def record_call(
        self, 
        model: str, 
        input_tokens: int, 
        output_tokens: int, 
        task_type: Optional[str] = None
    ) -> float:
        """记录一次模型调用并返回成本"""
        if model not in self.MODEL_COSTS:
            logger.warning(f"Unknown model {model}, using default cost estimation")
            cost_config = ModelCost(3.0/1000, 15.0/1000, ModelProvider.OPENROUTER)
        else:
            cost_config = self.MODEL_COSTS[model]
        
        # 计算成本
        input_cost = (input_tokens / 1000) * cost_config.input_cost_per_1k
        output_cost = (output_tokens / 1000) * cost_config.output_cost_per_1k
        total_cost = input_cost + output_cost
        
        # 记录调用
        record = CallRecord(
            model=model,
            provider=cost_config.provider,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            cost=total_cost,
            task_type=task_type
        )
        self.call_history.append(record)
        
        # 检查预算
        daily_cost = self.get_daily_cost()
        if daily_cost > self.daily_budget:
            logger.warning(f"Daily budget exceeded: ${daily_cost:.4f} > ${self.daily_budget}")
        
        logger.info(
            f"Model call recorded: {model}, "
            f"tokens: {input_tokens}+{output_tokens}, "
            f"cost: ${total_cost:.4f}, "
            f"daily total: ${daily_cost:.4f}"
        )
        
        return total_cost
    
    def get_daily_cost(self) -> float:
        """获取今日总成本"""
        today_start = time.time() - (time.time() % 86400)  # 今天00:00
        return sum(
            record.cost 
            for record in self.call_history 
            if record.timestamp >= today_start
        )
    
    def get_cost_by_task_type(self, task_type: str) -> float:
        """获取特定任务类型的成本"""
        return sum(
            record.cost 
            for record in self.call_history 
            if record.task_type == task_type
        )
    
    def get_cost_optimization_suggestions(self) -> List[str]:
        """获取成本优化建议"""
        suggestions = []
        
        # 分析最近的调用
        recent_calls = [r for r in self.call_history[-100:]]  # 最近100次调用
        if not recent_calls:
            return suggestions
        
        # 检查是否在使用昂贵的模型
        expensive_calls = [
            r for r in recent_calls 
            if r.model in ["anthropic/claude-3-opus", "claude-3-opus-20240229"]
        ]
        if expensive_calls:
            suggestions.append(
                f"发现{len(expensive_calls)}次Opus调用，考虑对简单任务使用Claude 3.5 Sonnet"
            )
        
        # 检查token使用效率
        avg_output_tokens = sum(r.output_tokens for r in recent_calls) / len(recent_calls)
        if avg_output_tokens > 2000:
            suggestions.append(
                f"平均输出token数较高({avg_output_tokens:.0f})，考虑优化prompt减少输出长度"
            )
        
        # 检查是否使用了OpenRouter
        openrouter_calls = [r for r in recent_calls if r.provider == ModelProvider.OPENROUTER]
        if len(openrouter_calls) < len(recent_calls) * 0.8:
            suggestions.append(
                "建议更多使用OpenRouter降低成本（通常便宜20-30%）"
            )
        
        return suggestions
    
    def get_stats(self) -> Dict:
        """获取成本统计信息"""
        if not self.call_history:
            return {"total_calls": 0, "total_cost": 0}
        
        total_cost = sum(r.cost for r in self.call_history)
        daily_cost = self.get_daily_cost()
        
        # 按模型统计
        model_stats = {}
        for record in self.call_history:
            if record.model not in model_stats:
                model_stats[record.model] = {"calls": 0, "cost": 0}
            model_stats[record.model]["calls"] += 1
            model_stats[record.model]["cost"] += record.cost
        
        return {
            "total_calls": len(self.call_history),
            "total_cost": total_cost,
            "daily_cost": daily_cost,
            "daily_budget": self.daily_budget,
            "budget_usage_pct": (daily_cost / self.daily_budget) * 100,
            "model_stats": model_stats,
            "optimization_suggestions": self.get_cost_optimization_suggestions()
        }

# 全局实例
cost_tracker = CostTracker()