"""
AGUI CustomEvent发送器
用于将事实核查Agent状态发送到前端
"""
import asyncio
import json
import time
from typing import Any, Dict, Optional, Union
from enum import Enum
import logging
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

class FactCheckEventType(Enum):
    """事实核查事件类型"""
    TASK_STARTED = "fact_check_task_started"
    CLAIM_EXTRACTED = "fact_check_claim_extracted"
    AGENT_PROGRESS = "fact_check_agent_progress"
    VERIFICATION_COMPLETE = "fact_check_verification_complete"
    DEBATE_STARTED = "fact_check_debate_started"
    DEBATE_MESSAGE = "fact_check_debate_message"
    DEBATE_CONCLUDED = "fact_check_debate_concluded"
    TASK_COMPLETE = "fact_check_task_complete"
    COST_UPDATE = "fact_check_cost_update"
    ERROR = "fact_check_error"

class AgentRole(Enum):
    """Agent角色"""
    ORCHESTRATOR = "orchestrator"
    CLAIM_EXTRACTOR = "claim_extractor"
    FINANCIAL_VERIFIER = "financial_verifier"
    CORPORATE_VERIFIER = "corporate_verifier"
    NEWS_VERIFIER = "news_verifier"
    DEBATE_MODERATOR = "debate_moderator"
    SYNTHESIS_AGENT = "synthesis_agent"

@dataclass
class FactCheckEventPayload:
    """事实核查事件载荷"""
    task_id: str
    agent_id: Optional[str] = None
    agent_role: Optional[AgentRole] = None
    status: Optional[str] = None
    progress: Optional[int] = None
    message: Optional[str] = None
    data: Optional[Any] = None
    cost: Optional[float] = None
    timestamp: float = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class AGUIEventSender:
    """AGUI事件发送器"""
    
    def __init__(self):
        self.event_handlers = []  # 存储事件处理器的列表
    
    def add_event_handler(self, handler):
        """添加事件处理器（通常是SSE连接）"""
        self.event_handlers.append(handler)
        logger.info(f"添加事件处理器，当前处理器数量: {len(self.event_handlers)}")
    
    def remove_event_handler(self, handler):
        """移除事件处理器"""
        if handler in self.event_handlers:
            self.event_handlers.remove(handler)
            logger.info(f"移除事件处理器，当前处理器数量: {len(self.event_handlers)}")
    
    async def send_event(
        self,
        event_type: FactCheckEventType,
        payload: FactCheckEventPayload,
        thread_id: Optional[str] = None,
        run_id: Optional[str] = None
    ):
        """发送AGUI CustomEvent"""
        
        # 构造AGUI兼容的事件结构
        event_data = {
            "type": "custom",  # AGUI标准事件类型
            "messageId": f"msg_{int(time.time() * 1000)}",
            "threadId": thread_id or payload.task_id,
            "runId": run_id or f"run_{payload.task_id}",
            "custom": {
                "type": event_type.value,
                "payload": asdict(payload)
            },
            "timestamp": payload.timestamp
        }
        
        logger.debug(f"发送AGUI事件: {event_type.value}", extra={
            "task_id": payload.task_id,
            "agent_role": payload.agent_role.value if payload.agent_role else None,
            "event_data": event_data
        })
        
        # 向所有连接的客户端发送事件
        if self.event_handlers:
            await asyncio.gather(*[
                self._send_to_handler(handler, event_data)
                for handler in self.event_handlers
            ], return_exceptions=True)
        else:
            logger.warning("没有可用的事件处理器", extra={
                "event_type": event_type.value,
                "task_id": payload.task_id
            })
    
    async def _send_to_handler(self, handler, event_data: Dict[str, Any]):
        """向单个处理器发送事件"""
        try:
            if hasattr(handler, 'send_json'):
                await handler.send_json(event_data)
            elif hasattr(handler, 'put'):
                await handler.put(json.dumps(event_data))
            elif callable(handler):
                await handler(event_data)
            else:
                logger.warning("不支持的事件处理器类型", extra={
                    "handler_type": type(handler).__name__
                })
        except Exception as e:
            logger.error("发送事件到处理器失败", extra={
                "error": str(e),
                "handler_type": type(handler).__name__
            })
    
    # 便捷方法：发送特定类型的事件
    
    async def send_task_started(self, task_id: str, task_data: Dict[str, Any]):
        """发送任务开始事件"""
        payload = FactCheckEventPayload(
            task_id=task_id,
            status="started",
            message="事实核查任务已开始",
            data=task_data
        )
        await self.send_event(FactCheckEventType.TASK_STARTED, payload)
    
    async def send_claim_extracted(self, task_id: str, claims: list):
        """发送声明提取完成事件"""
        payload = FactCheckEventPayload(
            task_id=task_id,
            agent_role=AgentRole.CLAIM_EXTRACTOR,
            status="completed",
            message=f"成功提取{len(claims)}个声明",
            data=claims
        )
        await self.send_event(FactCheckEventType.CLAIM_EXTRACTED, payload)
    
    async def send_agent_progress(
        self, 
        task_id: str, 
        agent_role: AgentRole,
        progress: int,
        message: str,
        agent_id: Optional[str] = None
    ):
        """发送Agent进度更新事件"""
        payload = FactCheckEventPayload(
            task_id=task_id,
            agent_id=agent_id or f"{agent_role.value}_{task_id}",
            agent_role=agent_role,
            progress=progress,
            message=message,
            status="in_progress" if progress < 100 else "completed"
        )
        await self.send_event(FactCheckEventType.AGENT_PROGRESS, payload)
    
    async def send_verification_complete(
        self, 
        task_id: str, 
        claim_data: Dict[str, Any],
        agent_role: AgentRole
    ):
        """发送验证完成事件"""
        payload = FactCheckEventPayload(
            task_id=task_id,
            agent_role=agent_role,
            status="verified",
            message=f"声明验证完成: {claim_data.get('status', 'unknown')}",
            data=claim_data
        )
        await self.send_event(FactCheckEventType.VERIFICATION_COMPLETE, payload)
    
    async def send_debate_started(
        self, 
        task_id: str, 
        debate_data: Dict[str, Any]
    ):
        """发送辩论开始事件"""
        payload = FactCheckEventPayload(
            task_id=task_id,
            agent_role=AgentRole.DEBATE_MODERATOR,
            status="debate_started",
            message=f"开始辩论: {debate_data.get('topic', 'Unknown topic')}",
            data=debate_data
        )
        await self.send_event(FactCheckEventType.DEBATE_STARTED, payload)
    
    async def send_debate_message(
        self, 
        task_id: str, 
        debate_message: Dict[str, Any]
    ):
        """发送辩论消息事件"""
        payload = FactCheckEventPayload(
            task_id=task_id,
            agent_role=AgentRole.DEBATE_MODERATOR,
            status="debate_message",
            message="收到辩论消息",
            data=debate_message
        )
        await self.send_event(FactCheckEventType.DEBATE_MESSAGE, payload)
    
    async def send_debate_concluded(
        self, 
        task_id: str, 
        conclusion_data: Dict[str, Any]
    ):
        """发送辩论结束事件"""
        payload = FactCheckEventPayload(
            task_id=task_id,
            agent_role=AgentRole.DEBATE_MODERATOR,
            status="debate_concluded",
            message="辩论已结束",
            data=conclusion_data
        )
        await self.send_event(FactCheckEventType.DEBATE_CONCLUDED, payload)
    
    async def send_task_complete(
        self, 
        task_id: str, 
        final_result: Dict[str, Any],
        total_cost: float
    ):
        """发送任务完成事件"""
        payload = FactCheckEventPayload(
            task_id=task_id,
            status="completed",
            message="事实核查任务已完成",
            data=final_result,
            cost=total_cost
        )
        await self.send_event(FactCheckEventType.TASK_COMPLETE, payload)
    
    async def send_cost_update(self, task_id: str, cost: float):
        """发送成本更新事件"""
        payload = FactCheckEventPayload(
            task_id=task_id,
            status="cost_update",
            message=f"成本更新: ${cost:.4f}",
            cost=cost
        )
        await self.send_event(FactCheckEventType.COST_UPDATE, payload)
    
    async def send_error(
        self, 
        task_id: str, 
        error_message: str,
        agent_role: Optional[AgentRole] = None
    ):
        """发送错误事件"""
        payload = FactCheckEventPayload(
            task_id=task_id,
            agent_role=agent_role,
            status="error",
            message=error_message
        )
        await self.send_event(FactCheckEventType.ERROR, payload)

# 全局事件发送器实例
agui_event_sender = AGUIEventSender()