"""聊天用例门面 - 提供统一的聊天功能入口"""
from typing import Optional, Dict, Any, AsyncGenerator, List

from .chat.session_management_use_case import SessionManagementUseCase
from .chat.message_processing_use_case import MessageProcessingUseCase
from ..dtos.chat_dtos import (
    CreateChatSessionRequest,
    ChatMessageRequest,
    UpdateSessionRequest,
    ChatResponse,
    SessionInfo,
    MessageInfo
)


class ChatFacade:
    """聊天用例门面 - 组合和协调各个聊天用例"""
    
    def __init__(
        self,
        session_management: SessionManagementUseCase,
        message_processing: MessageProcessingUseCase
    ):
        self._session_management = session_management
        self._message_processing = message_processing
    
    # 会话管理相关方法
    async def create_chat_session(
        self,
        request: CreateChatSessionRequest
    ) -> SessionInfo:
        """创建聊天会话"""
        return await self._session_management.create_chat_session(request)
    
    async def get_session_info(
        self,
        session_id: str
    ) -> SessionInfo:
        """获取会话信息"""
        return await self._session_management.get_session_info(session_id)
    
    async def update_session(
        self,
        session_id: str,
        request: UpdateSessionRequest
    ) -> SessionInfo:
        """更新会话信息"""
        return await self._session_management.update_session(session_id, request)
    
    async def list_user_sessions(
        self,
        user_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[SessionInfo]:
        """获取用户的会话列表"""
        return await self._session_management.list_user_sessions(user_id, limit, offset)
    
    async def delete_session(
        self,
        session_id: str
    ) -> bool:
        """删除会话"""
        return await self._session_management.delete_session(session_id)
    
    # 消息处理相关方法
    async def send_message(
        self,
        request: ChatMessageRequest
    ) -> ChatResponse:
        """发送聊天消息"""
        return await self._message_processing.send_message(request)
    
    async def send_message_stream(
        self,
        request: ChatMessageRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """发送聊天消息并流式返回响应"""
        async for chunk in self._message_processing.send_message_stream(request):
            yield chunk
    
    async def get_session_messages(
        self,
        session_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[MessageInfo]:
        """获取会话消息列表"""
        return await self._message_processing.get_session_messages(session_id, limit, offset)