"""研究流水线管理用例"""
from typing import Dict, Any

from ....domain.entities.research_task import ResearchTask, ResearchPipeline
from ....domain.services.research_domain_service import ResearchDomainService
from ....domain.repositories.research_repository import (
    ResearchPipelineRepository,
    ResearchTaskRepository
)
from ....core.exceptions import ApplicationException, ValidationError
from ...dtos.research_dtos import (
    CreateResearchPipelineRequest,
    PipelineInfo,
    AddTaskRequest,
    TaskInfo
)


class PipelineManagementUseCase:
    """研究流水线管理用例 - 处理流水线创建和任务管理"""
    
    def __init__(
        self,
        research_domain_service: ResearchDomainService,
        pipeline_repository: ResearchPipelineRepository,
        task_repository: ResearchTaskRepository
    ):
        self._research_service = research_domain_service
        self._pipeline_repository = pipeline_repository
        self._task_repository = task_repository
    
    async def create_research_pipeline(
        self,
        request: CreateResearchPipelineRequest
    ) -> PipelineInfo:
        """创建研究流水线"""
        try:
            await self._validate_create_pipeline_request(request)
            
            pipeline = await self._research_service.create_research_pipeline(
                title=request.title,
                description=request.description,
                analysis_type=request.analysis_type,
                tasks=request.tasks or []
            )
            
            return PipelineInfo(
                pipeline_id=pipeline.id,
                title=pipeline.title,
                description=pipeline.description,
                status=pipeline.status,
                total_tasks=pipeline.total_tasks,
                completed_tasks=pipeline.completed_tasks,
                created_at=pipeline.created_at,
                updated_at=pipeline.updated_at
            )
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to create research pipeline: {str(e)}"
            )
    
    async def add_task_to_pipeline(
        self,
        request: AddTaskRequest
    ) -> TaskInfo:
        """向流水线添加任务"""
        try:
            await self._validate_add_task_request(request)
            
            task = await self._research_service.add_task_to_pipeline(
                pipeline_id=request.pipeline_id,
                title=request.title,
                task_type=request.task_type,
                config=request.config or {}
            )
            
            return TaskInfo(
                task_id=task.id,
                title=task.title,
                task_type=task.task_type,
                status=task.status,
                order=task.order,
                created_at=task.created_at,
                updated_at=task.updated_at,
                duration=task.duration
            )
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to add task to pipeline: {str(e)}"
            )
    
    async def get_pipeline_info(
        self,
        pipeline_id: str
    ) -> PipelineInfo:
        """获取流水线信息"""
        try:
            pipeline = await self._pipeline_repository.get_pipeline_with_tasks(pipeline_id)
            if not pipeline:
                raise ApplicationException(f"Pipeline {pipeline_id} not found")
            
            return PipelineInfo(
                pipeline_id=pipeline.id,
                title=pipeline.title,
                description=pipeline.description,
                status=pipeline.status,
                total_tasks=pipeline.total_tasks,
                completed_tasks=pipeline.completed_tasks,
                created_at=pipeline.created_at,
                updated_at=pipeline.updated_at
            )
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to get pipeline info: {str(e)}"
            )
    
    async def _validate_create_pipeline_request(
        self,
        request: CreateResearchPipelineRequest
    ) -> None:
        """验证创建流水线请求"""
        if not request.title or not request.title.strip():
            raise ValidationError("Pipeline title is required")
        
        if len(request.title) > 200:
            raise ValidationError("Pipeline title too long (max 200 characters)")
        
        if request.description and len(request.description) > 1000:
            raise ValidationError("Pipeline description too long (max 1000 characters)")
    
    async def _validate_add_task_request(
        self,
        request: AddTaskRequest
    ) -> None:
        """验证添加任务请求"""
        if not request.pipeline_id:
            raise ValidationError("Pipeline ID is required")
        
        if not request.title or not request.title.strip():
            raise ValidationError("Task title is required")
        
        if len(request.title) > 200:
            raise ValidationError("Task title too long (max 200 characters)")
        
        pipeline = await self._pipeline_repository.get_by_id(request.pipeline_id)
        if not pipeline:
            raise ValidationError(f"Pipeline {request.pipeline_id} not found")