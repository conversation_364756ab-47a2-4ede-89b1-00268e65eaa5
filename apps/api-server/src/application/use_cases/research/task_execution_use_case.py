"""研究任务执行用例"""
from typing import Optional, Dict, Any, AsyncGenerator

from ....domain.entities.research_task import ResearchTask
from ....domain.services.research_domain_service import ResearchDomainService
from ....domain.repositories.research_repository import (
    ResearchTaskRepository,
    ResearchPipelineRepository,
    ResearchResultRepository
)
from ....infrastructure.external.tools.tool_registry import tool_registry
from ....core.exceptions import ApplicationException
from ...dtos.research_dtos import (
    StartTaskRequest,
    CompleteTaskRequest,
    TaskInfo,
    TaskResult
)


class TaskExecutionUseCase:
    """研究任务执行用例 - 处理任务的执行和完成"""
    
    def __init__(
        self,
        research_domain_service: ResearchDomainService,
        task_repository: ResearchTaskRepository,
        pipeline_repository: ResearchPipelineRepository,
        result_repository: ResearchResultRepository
    ):
        self._research_service = research_domain_service
        self._task_repository = task_repository
        self._pipeline_repository = pipeline_repository
        self._result_repository = result_repository
    
    async def start_next_task(
        self,
        request: StartTaskRequest
    ) -> Optional[TaskInfo]:
        """开始下一个任务"""
        try:
            task = await self._research_service.start_next_task(request.pipeline_id)
            
            if not task:
                return None
            
            return TaskInfo(
                task_id=task.id,
                title=task.title,
                task_type=task.task_type,
                status=task.status,
                order=task.order,
                created_at=task.created_at,
                updated_at=task.updated_at,
                duration=task.duration
            )
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to start next task: {str(e)}"
            )
    
    async def complete_task(
        self,
        request: CompleteTaskRequest
    ) -> TaskResult:
        """完成任务"""
        try:
            task = await self._research_service.complete_task(
                request.task_id,
                request.result
            )
            
            return TaskResult(
                task_id=task.id,
                result=request.result,
                status=task.status,
                duration=task.duration,
                completed_at=task.updated_at
            )
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to complete task: {str(e)}"
            )
    
    async def execute_research_stream(
        self,
        pipeline_id: str,
        query: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """执行研究分析流式处理"""
        try:
            pipeline = await self._pipeline_repository.get_pipeline_with_tasks(pipeline_id)
            if not pipeline:
                raise ApplicationException(f"Pipeline {pipeline_id} not found")
            
            yield {
                "type": "start",
                "data": {
                    "pipeline_id": pipeline_id,
                    "total_tasks": pipeline.total_tasks,
                    "query": query
                }
            }
            
            for task_order in range(pipeline.total_tasks):
                current_task = await self._research_service.start_next_task(pipeline_id)
                
                if not current_task:
                    break
                
                yield {
                    "type": "task_start",
                    "data": {
                        "task_id": current_task.id,
                        "task_type": current_task.task_type.value,
                        "title": current_task.title,
                        "order": current_task.order
                    }
                }
                
                task_result = await self._execute_single_task(
                    current_task,
                    query,
                    context or {}
                )
                
                completed_task = await self._research_service.complete_task(
                    current_task.id,
                    task_result
                )
                
                yield {
                    "type": "task_complete",
                    "data": {
                        "task_id": completed_task.id,
                        "result": task_result,
                        "duration": completed_task.duration
                    }
                }
            
            yield {
                "type": "finish",
                "data": {
                    "pipeline_id": pipeline_id,
                    "message": "Research analysis completed"
                }
            }
            
        except Exception as e:
            yield {
                "type": "error",
                "data": {
                    "pipeline_id": pipeline_id,
                    "error": str(e)
                }
            }
    
    async def _execute_single_task(
        self,
        task: ResearchTask,
        query: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行单个任务"""
        try:
            tools = await tool_registry.get_tools_for_task_type(task.task_type)
            
            if not tools:
                return {
                    "success": False,
                    "error": f"No tools available for task type: {task.task_type.value}",
                    "task_type": task.task_type.value,
                    "query": query
                }
            
            primary_tool = tools[0]
            
            tool_input = {
                "query": query,
                "task_config": task.config,
                "context": context
            }
            
            result = await primary_tool.execute(**tool_input)
            
            return {
                "success": True,
                "data": result,
                "task_type": task.task_type.value,
                "tool_used": primary_tool.get_name(),
                "query": query
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "task_type": task.task_type.value,
                "query": query
            }