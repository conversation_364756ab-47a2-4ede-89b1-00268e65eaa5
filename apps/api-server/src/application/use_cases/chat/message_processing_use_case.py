"""聊天消息处理用例"""
from typing import Optional, Dict, Any, AsyncGenerator, List
from datetime import datetime

from ....domain.entities.chat_session import Message, MessageRole
from ....domain.services.chat_domain_service import ChatDomainService
from ....domain.repositories.chat_repository import ChatSessionRepository, MessageRepository
from ....infrastructure.external.tools.tool_registry import tool_registry
from ....core.exceptions import ApplicationException, ValidationError
from ...dtos.chat_dtos import (
    ChatMessageRequest,
    ChatResponse,
    MessageInfo
)


class MessageProcessingUseCase:
    """聊天消息处理用例 - 处理消息的发送、接收和流式处理"""
    
    def __init__(
        self,
        chat_domain_service: ChatDomainService,
        session_repository: ChatSessionRepository,
        message_repository: MessageRepository
    ):
        self._chat_service = chat_domain_service
        self._session_repository = session_repository
        self._message_repository = message_repository
    
    async def send_message(
        self,
        request: ChatMessageRequest
    ) -> ChatResponse:
        """发送聊天消息"""
        try:
            await self._validate_message_request(request)
            
            message = await self._chat_service.add_message(
                session_id=request.session_id,
                content=request.content,
                role=MessageRole.USER,
                metadata=request.metadata or {}
            )
            
            response = await self._generate_response(
                request.session_id,
                message.content,
                request.context or {}
            )
            
            return ChatResponse(
                session_id=request.session_id,
                message_id=message.id,
                response=response,
                timestamp=datetime.utcnow(),
                metadata={"processing_time": "calculated_time"}
            )
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to send message: {str(e)}"
            )
    
    async def send_message_stream(
        self,
        request: ChatMessageRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """发送聊天消息并流式返回响应"""
        try:
            await self._validate_message_request(request)
            
            # 添加用户消息
            user_message = await self._chat_service.add_message(
                session_id=request.session_id,
                content=request.content,
                role=MessageRole.USER,
                metadata=request.metadata or {}
            )
            
            yield {
                "type": "user_message",
                "data": {
                    "message_id": user_message.id,
                    "content": user_message.content,
                    "timestamp": user_message.created_at.isoformat()
                }
            }
            
            # 流式生成AI响应
            response_content = ""
            async for chunk in self._generate_response_stream(
                request.session_id,
                user_message.content,
                request.context or {}
            ):
                response_content += chunk.get("content", "")
                yield chunk
            
            # 保存AI响应消息
            ai_message = await self._chat_service.add_message(
                session_id=request.session_id,
                content=response_content,
                role=MessageRole.ASSISTANT,
                metadata={"generated": True}
            )
            
            yield {
                "type": "assistant_message_complete",
                "data": {
                    "message_id": ai_message.id,
                    "session_id": request.session_id,
                    "timestamp": ai_message.created_at.isoformat()
                }
            }
            
        except Exception as e:
            yield {
                "type": "error",
                "data": {
                    "error": str(e),
                    "session_id": request.session_id
                }
            }
    
    async def get_session_messages(
        self,
        session_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[MessageInfo]:
        """获取会话消息列表"""
        try:
            messages = await self._message_repository.get_by_session_id(
                session_id, limit, offset
            )
            
            return [
                MessageInfo(
                    message_id=msg.id,
                    session_id=msg.session_id,
                    content=msg.content,
                    role=msg.role,
                    created_at=msg.created_at,
                    metadata=msg.metadata
                )
                for msg in messages
            ]
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to get session messages: {str(e)}"
            )
    
    async def _generate_response(
        self,
        session_id: str,
        user_input: str,
        context: Dict[str, Any]
    ) -> str:
        """生成聊天响应"""
        try:
            # 获取会话历史
            session = await self._session_repository.get_by_id(session_id)
            if not session:
                raise ApplicationException(f"Session {session_id} not found")
            
            # 模拟AI响应生成
            return f"这是对'{user_input}'的回复。基于会话历史和上下文生成。"
            
        except Exception as e:
            raise ApplicationException(f"Failed to generate response: {str(e)}")
    
    async def _generate_response_stream(
        self,
        session_id: str,
        user_input: str,
        context: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """流式生成聊天响应"""
        try:
            # 模拟流式响应
            response_parts = [
                "这是", "对您问题的", "详细回答。", "我会根据", "上下文信息", 
                "为您提供", "最准确的", "分析和建议。"
            ]
            
            for i, part in enumerate(response_parts):
                yield {
                    "type": "response_chunk",
                    "data": {
                        "content": part + (" " if i < len(response_parts) - 1 else ""),
                        "chunk_index": i,
                        "session_id": session_id
                    }
                }
                
                # 模拟延迟
                import asyncio
                await asyncio.sleep(0.1)
            
        except Exception as e:
            yield {
                "type": "error",
                "data": {
                    "error": f"Stream generation failed: {str(e)}",
                    "session_id": session_id
                }
            }
    
    async def _validate_message_request(
        self,
        request: ChatMessageRequest
    ) -> None:
        """验证聊天消息请求"""
        if not request.session_id or not request.session_id.strip():
            raise ValidationError("Session ID is required")
        
        if not request.content or not request.content.strip():
            raise ValidationError("Message content is required")
        
        if len(request.content) > 10000:
            raise ValidationError("Message content too long (max 10000 characters)")
        
        # 验证会话存在
        session = await self._session_repository.get_by_id(request.session_id)
        if not session:
            raise ValidationError(f"Session {request.session_id} not found")