"""聊天会话管理用例"""
from typing import List, Dict, Any
from datetime import datetime

from ....domain.entities.chat_session import ChatSession
from ....domain.services.chat_domain_service import ChatDomainService
from ....domain.repositories.chat_repository import ChatSessionRepository, MessageRepository
from ....core.exceptions import ApplicationException, ValidationError
from ...dtos.chat_dtos import (
    CreateChatSessionRequest,
    SessionInfo,
    UpdateSessionRequest
)


class SessionManagementUseCase:
    """聊天会话管理用例 - 处理会话的创建、更新和查询"""
    
    def __init__(
        self,
        chat_domain_service: ChatDomainService,
        session_repository: ChatSessionRepository,
        message_repository: MessageRepository
    ):
        self._chat_service = chat_domain_service
        self._session_repository = session_repository
        self._message_repository = message_repository
    
    async def create_chat_session(
        self,
        request: CreateChatSessionRequest
    ) -> SessionInfo:
        """创建聊天会话"""
        try:
            await self._validate_create_session_request(request)
            
            session = await self._chat_service.create_chat_session(
                user_id=request.user_id,
                initial_message=request.initial_message
            )
            
            return SessionInfo(
                session_id=session.id,
                title=session.title,
                status=session.status,
                created_at=session.created_at,
                updated_at=session.updated_at,
                message_count=session.message_count
            )
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to create chat session: {str(e)}"
            )
    
    async def get_session_info(
        self,
        session_id: str
    ) -> SessionInfo:
        """获取会话信息"""
        try:
            session = await self._session_repository.get_by_id(session_id)
            if not session:
                raise ApplicationException(f"Session {session_id} not found")
            
            return SessionInfo(
                session_id=session.id,
                title=session.title,
                status=session.status,
                created_at=session.created_at,
                updated_at=session.updated_at,
                message_count=session.message_count
            )
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to get session info: {str(e)}"
            )
    
    async def update_session(
        self,
        session_id: str,
        request: UpdateSessionRequest
    ) -> SessionInfo:
        """更新会话信息"""
        try:
            await self._validate_update_session_request(request)
            
            session = await self._chat_service.update_chat_session(
                session_id=session_id,
                title=request.title,
                status=request.status
            )
            
            return SessionInfo(
                session_id=session.id,
                title=session.title,
                status=session.status,
                created_at=session.created_at,
                updated_at=session.updated_at,
                message_count=session.message_count
            )
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to update session: {str(e)}"
            )
    
    async def list_user_sessions(
        self,
        user_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[SessionInfo]:
        """获取用户的会话列表"""
        try:
            sessions = await self._session_repository.get_by_user_id(
                user_id, limit, offset
            )
            
            return [
                SessionInfo(
                    session_id=session.id,
                    title=session.title,
                    status=session.status,
                    created_at=session.created_at,
                    updated_at=session.updated_at,
                    message_count=session.message_count
                )
                for session in sessions
            ]
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to list user sessions: {str(e)}"
            )
    
    async def delete_session(
        self,
        session_id: str
    ) -> bool:
        """删除会话"""
        try:
            success = await self._chat_service.delete_chat_session(session_id)
            return success
            
        except Exception as e:
            raise ApplicationException(
                f"Failed to delete session: {str(e)}"
            )
    
    async def _validate_create_session_request(
        self,
        request: CreateChatSessionRequest
    ) -> None:
        """验证创建会话请求"""
        if not request.user_id or not request.user_id.strip():
            raise ValidationError("User ID is required")
        
        if request.initial_message and len(request.initial_message) > 5000:
            raise ValidationError("Initial message too long (max 5000 characters)")
    
    async def _validate_update_session_request(
        self,
        request: UpdateSessionRequest
    ) -> None:
        """验证更新会话请求"""
        if request.title and len(request.title) > 200:
            raise ValidationError("Session title too long (max 200 characters)")
        
        if request.title and not request.title.strip():
            raise ValidationError("Session title cannot be empty")