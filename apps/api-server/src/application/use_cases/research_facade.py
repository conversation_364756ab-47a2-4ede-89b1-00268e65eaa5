"""研究用例门面 - 提供统一的研究功能入口"""
from typing import Optional, Dict, Any, AsyncGenerator

from .research.pipeline_management_use_case import PipelineManagementUseCase
from .research.task_execution_use_case import TaskExecutionUseCase
from ..dtos.research_dtos import (
    CreateResearchPipelineRequest,
    AddTaskRequest,
    StartTaskRequest,
    CompleteTaskRequest,
    PipelineInfo,
    TaskInfo,
    TaskResult
)


class ResearchFacade:
    """研究用例门面 - 组合和协调各个研究用例"""
    
    def __init__(
        self,
        pipeline_management: PipelineManagementUseCase,
        task_execution: TaskExecutionUseCase
    ):
        self._pipeline_management = pipeline_management
        self._task_execution = task_execution
    
    async def create_research_pipeline(
        self,
        request: CreateResearchPipelineRequest
    ) -> PipelineInfo:
        """创建研究流水线"""
        return await self._pipeline_management.create_research_pipeline(request)
    
    async def add_task_to_pipeline(
        self,
        request: AddTaskRequest
    ) -> TaskInfo:
        """向流水线添加任务"""
        return await self._pipeline_management.add_task_to_pipeline(request)
    
    async def get_pipeline_info(
        self,
        pipeline_id: str
    ) -> PipelineInfo:
        """获取流水线信息"""
        return await self._pipeline_management.get_pipeline_info(pipeline_id)
    
    async def start_next_task(
        self,
        request: StartTaskRequest
    ) -> Optional[TaskInfo]:
        """开始下一个任务"""
        return await self._task_execution.start_next_task(request)
    
    async def complete_task(
        self,
        request: CompleteTaskRequest
    ) -> TaskResult:
        """完成任务"""
        return await self._task_execution.complete_task(request)
    
    async def execute_research_stream(
        self,
        pipeline_id: str,
        query: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """执行研究分析流式处理"""
        async for result in self._task_execution.execute_research_stream(
            pipeline_id, query, context
        ):
            yield result