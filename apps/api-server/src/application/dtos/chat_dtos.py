"""聊天相关的数据传输对象"""
from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

from ...domain.entities.chat_session import SessionStatus, MessageRole


class CreateChatSessionRequest(BaseModel):
    """创建聊天会话请求"""
    user_id: Optional[str] = Field(None, description="用户ID")
    initial_message: Optional[str] = Field(None, description="初始消息")


class UpdateSessionRequest(BaseModel):
    """更新会话请求"""
    title: Optional[str] = Field(None, description="会话标题")
    status: Optional[SessionStatus] = Field(None, description="会话状态")


class ChatMessageRequest(BaseModel):
    """聊天消息请求"""
    session_id: str = Field(..., description="会话ID")
    message: str = Field(..., min_length=1, max_length=10000, description="消息内容")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")


class ChatResponse(BaseModel):
    """聊天响应"""
    message_id: str = Field(..., description="消息ID")
    session_id: str = Field(..., description="会话ID")
    status: str = Field(..., description="处理状态")
    context_length: Optional[int] = Field(None, description="上下文长度")


class SessionInfo(BaseModel):
    """会话信息"""
    session_id: str = Field(..., description="会话ID")
    title: str = Field(..., description="会话标题")
    status: SessionStatus = Field(..., description="会话状态")
    created_at: datetime = Field(..., description="创建时间")
    message_count: int = Field(..., description="消息数量")


class MessageInfo(BaseModel):
    """消息信息"""
    message_id: str = Field(..., description="消息ID")
    role: MessageRole = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")
    created_at: datetime = Field(..., description="创建时间")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")