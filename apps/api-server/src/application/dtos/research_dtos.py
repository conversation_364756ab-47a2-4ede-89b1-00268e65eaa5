"""研究相关的数据传输对象"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field

from ...domain.value_objects.task_status import TaskStatus, ResearchTaskType


class CreateResearchPipelineRequest(BaseModel):
    """创建研究流水线请求"""
    name: str = Field(..., min_length=1, max_length=200, description="流水线名称")
    description: str = Field(..., max_length=1000, description="流水线描述")
    user_id: str = Field(..., description="用户ID")
    thread_id: Optional[str] = Field(None, description="线程ID")


class AddTaskRequest(BaseModel):
    """添加任务请求"""
    pipeline_id: str = Field(..., description="流水线ID")
    task_type: ResearchTaskType = Field(..., description="任务类型")
    title: str = Field(..., min_length=1, max_length=200, description="任务标题")
    description: str = Field(..., max_length=1000, description="任务描述")
    context: Optional[Dict[str, Any]] = Field(None, description="任务上下文")


class TaskInfo(BaseModel):
    """任务信息"""
    task_id: str = Field(..., description="任务ID")
    title: str = Field(..., description="任务标题")
    task_type: ResearchTaskType = Field(..., description="任务类型")
    status: TaskStatus = Field(..., description="任务状态")
    order: int = Field(..., description="执行顺序")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    duration: Optional[float] = Field(None, description="执行时长（秒）")


class PipelineInfo(BaseModel):
    """流水线信息"""
    pipeline_id: str = Field(..., description="流水线ID")
    name: str = Field(..., description="流水线名称")
    description: str = Field(..., description="流水线描述")
    total_tasks: int = Field(..., description="总任务数")
    completed_tasks: int = Field(..., description="已完成任务数")
    failed_tasks: int = Field(..., description="失败任务数")
    progress_percentage: float = Field(..., description="进度百分比")
    is_completed: bool = Field(..., description="是否已完成")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    tasks: List[TaskInfo] = Field(default_factory=list, description="任务列表")


class StartTaskRequest(BaseModel):
    """开始任务请求"""
    pipeline_id: str = Field(..., description="流水线ID")


class CompleteTaskRequest(BaseModel):
    """完成任务请求"""
    task_id: str = Field(..., description="任务ID")
    result: Dict[str, Any] = Field(..., description="任务结果")


class TaskResult(BaseModel):
    """任务结果"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    result: Optional[Dict[str, Any]] = Field(None, description="任务结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    completed_at: Optional[datetime] = Field(None, description="完成时间")