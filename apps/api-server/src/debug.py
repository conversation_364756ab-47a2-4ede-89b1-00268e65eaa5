"""调试工具 - 用于测试日志配置和 SLS 连接"""
import os
import time
from loguru import logger
from dotenv import load_dotenv

def debug_environment():
    """调试环境变量配置"""
    load_dotenv()
    
    env_vars = [
        "SLS_ENABLED", "SLS_ENDPOINT", "SLS_ACCESS_KEY_ID", 
        "SLS_ACCESS_KEY_SECRET", "SLS_PROJECT", "SLS_LOGSTORE", 
        "SLS_REGION", "SLS_TOPIC"
    ]
    
    print("🔍 环境变量检查:")
    for var in env_vars:
        value = os.getenv(var)
        if var in ["SLS_ACCESS_KEY_SECRET"]:
            # 隐藏敏感信息
            display_value = f"{value[:8]}..." if value else "未设置"
        else:
            display_value = value or "未设置"
        print(f"  {var}: {display_value}")

def test_sls_connection():
    """测试 SLS 连接"""
    import sys
    from pathlib import Path

    # 添加项目根目录到 Python 路径
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))

    from src.config import setup_logging
    from yai_loguru_sinks import register_protocol_parsers
    
    # 注册协议解析器
    register_protocol_parsers()
    
    # 设置日志
    setup_logging()
    
    # 发送测试日志
    test_message = f"SLS 连接测试 - {time.time()}"
    logger.debug(test_message)
    logger.info(test_message)
    logger.warning(test_message)
    logger.error(test_message)
    
    # 结构化日志测试
    logger.info("SLS 结构化日志测试", extra={
        "test_id": f"test_{int(time.time())}",
        "service": "investor-insight",
        "environment": "development",
        "test_data": {
            "string_field": "测试字符串",
            "number_field": 12345,
            "boolean_field": True,
            "array_field": ["item1", "item2", "item3"]
        }
    })
    
    print(f"✅ 测试日志已发送: {test_message}")
    print("请在 SLS 控制台检查日志是否正常接收")
    print("SLS 控制台地址: https://sls.console.aliyun.com/")
    print("项目: yai-log-test")
    print("日志库: app-log")

def test_pack_id_functionality():
    """测试 PackId 功能"""
    import sys
    from pathlib import Path

    # 添加项目根目录到 Python 路径
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))

    from src.config import setup_logging
    from yai_loguru_sinks import register_protocol_parsers
    
    # 注册协议解析器
    register_protocol_parsers()
    
    # 设置日志
    setup_logging()
    
    # 模拟业务流程 - 这些日志应该被 PackId 自动关联
    business_id = f"BUSINESS_{int(time.time())}"
    
    logger.info("业务流程开始", extra={"business_id": business_id})
    
    # 步骤1
    logger.debug("执行步骤1: 数据验证", extra={
        "business_id": business_id,
        "step": "validation",
        "details": "验证用户输入数据"
    })
    
    # 步骤2
    logger.debug("执行步骤2: 业务处理", extra={
        "business_id": business_id,
        "step": "processing",
        "details": "执行核心业务逻辑"
    })
    
    # 步骤3
    logger.debug("执行步骤3: 结果保存", extra={
        "business_id": business_id,
        "step": "saving",
        "details": "保存处理结果"
    })
    
    logger.info("业务流程完成", extra={
        "business_id": business_id,
        "status": "success",
        "total_steps": 3
    })
    
    print(f"✅ PackId 测试完成，业务ID: {business_id}")
    print("在 SLS 控制台中搜索该业务ID，应该能看到所有相关日志被自动关联")

if __name__ == "__main__":
    print("🚀 投资洞察 API 日志调试工具")
    print("=" * 50)
    
    # 1. 环境变量检查
    debug_environment()
    print()
    
    # 2. SLS 连接测试
    print("📡 测试 SLS 连接...")
    test_sls_connection()
    print()
    
    # 3. PackId 功能测试
    print("🔗 测试 PackId 功能...")
    test_pack_id_functionality()
    print()
    
    print("🎉 调试测试完成！")
