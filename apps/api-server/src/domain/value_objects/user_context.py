"""用户上下文值对象"""
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass(frozen=True)
class UserContext:
    """用户上下文值对象"""
    
    user_id: str
    session_id: Optional[str] = None
    preferences: Dict[str, Any] = None
    metadata: Dict[str, Any] = None
    created_at: datetime = None
    
    def __post_init__(self):
        # 由于frozen=True，需要使用object.__setattr__
        if self.preferences is None:
            object.__setattr__(self, 'preferences', {})
        if self.metadata is None:
            object.__setattr__(self, 'metadata', {})
        if self.created_at is None:
            object.__setattr__(self, 'created_at', datetime.now())
    
    def get_preference(self, key: str, default: Any = None) -> Any:
        """获取用户偏好"""
        return self.preferences.get(key, default)
    
    def has_preference(self, key: str) -> bool:
        """检查是否有特定偏好"""
        return key in self.preferences
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """获取元数据"""
        return self.metadata.get(key, default)
    
    @property
    def is_anonymous(self) -> bool:
        """判断是否为匿名用户"""
        return self.user_id == "anonymous" or self.user_id.startswith("anon_")