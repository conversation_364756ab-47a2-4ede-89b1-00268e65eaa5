"""任务状态值对象"""
from enum import Enum


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    
    @property
    def is_terminal(self) -> bool:
        """判断是否为终态"""
        return self in [self.COMPLETED, self.FAILED]
    
    @property
    def is_active(self) -> bool:
        """判断是否为活跃状态"""
        return self == self.IN_PROGRESS
    
    @property
    def can_start(self) -> bool:
        """判断是否可以开始"""
        return self == self.PENDING
    
    @property
    def can_complete(self) -> bool:
        """判断是否可以完成"""
        return self == self.IN_PROGRESS
    
    @property
    def can_fail(self) -> bool:
        """判断是否可以失败"""
        return self in [self.PENDING, self.IN_PROGRESS]


class ResearchTaskType(str, Enum):
    """研究任务类型枚举"""
    TRUTH_VERIFICATION = "truth-verification"
    IMPACT_SIMULATION = "impact-simulation"
    THESIS_GENERATION = "thesis-generation"
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        display_names = {
            self.TRUTH_VERIFICATION: "真实性验证",
            self.IMPACT_SIMULATION: "影响模拟",
            self.THESIS_GENERATION: "投资论文生成"
        }
        return display_names.get(self, self.value)
    
    @property
    def description(self) -> str:
        """任务描述"""
        descriptions = {
            self.TRUTH_VERIFICATION: "验证投资信息的真实性和可靠性",
            self.IMPACT_SIMULATION: "模拟投资决策对市场的影响",
            self.THESIS_GENERATION: "生成详细的投资分析报告"
        }
        return descriptions.get(self, "")