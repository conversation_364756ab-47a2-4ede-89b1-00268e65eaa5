"""LangGraph Agent 构建器"""
from langgraph.graph import StateGraph, END, START
from langgraph.prebuilt import ToolNode, tools_condition
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from ..tools import get_all_tools
from .state import AgentState
from ...infrastructure.logging import get_logger

logger = get_logger(__name__)

def build_investment_agent() -> StateGraph:
    """构建投资研究Agent - 简化为工厂函数，遵循CTO建议"""
    logger.info("Building investment research agent...")
    
    tools = get_all_tools()
    
    workflow = StateGraph(AgentState)
    
    # 配置LLM
    llm = ChatOpenAI(model="gpt-4o", streaming=True, temperature=0.1)
    llm_with_tools = llm.bind_tools(tools)
    
    # 系统提示词
    system_prompt = """你是一个专业的AI投资研究助手，拥有以下工具来帮助分析：

🛠️ **可用工具**：
1. get_market_data - 获取股票实时价格、历史数据、财务指标
2. search_financial_news - 搜索投资相关新闻和市场动态
3. get_financial_statements - 获取公司财务报表数据（损益表、资产负债表、现金流量表）

🎯 **核心能力**：
1. 股票分析：提供深入的公司和股票分析
2. 市场趋势：解读宏观经济和市场动态  
3. 投资建议：基于数据提供客观的投资见解
4. 风险评估：识别和评估投资风险
5. 金融教育：解释复杂的金融概念

📋 **分析框架**：
- 基本面分析：财务数据、业务模式、竞争优势
- 技术面分析：价格走势、技术指标、市场情绪
- 宏观分析：经济环境、政策影响、行业趋势

🔍 **工作流程**：
当用户询问特定公司或股票时，你应该：
1. 使用 get_market_data 工具获取当前股价和基本信息
2. 使用 search_financial_news 工具搜索相关新闻动态
3. 使用 get_financial_statements 工具获取财务报表数据
4. 综合分析所有数据，提供专业见解

⚠️ **重要提醒**：
- 所有分析仅供参考，不构成投资建议
- 投资有风险，请根据自身情况谨慎决策
- 建议分散投资，合理控制风险
- 始终基于获取的数据进行分析，避免臆测

请用专业、客观、易懂的方式回答用户问题，必要时主动使用工具获取数据。"""
    
    def agent_node(state: AgentState):
        """Agent推理节点"""
        messages = state["messages"]
        
        # 如果是第一条消息，添加系统提示
        if len(messages) == 1 and isinstance(messages[0], HumanMessage):
            messages = [
                SystemMessage(content=system_prompt),
                *messages
            ]
        
        response = llm_with_tools.invoke(messages)
        return {"messages": [response], "iterations": state.get("iterations", 0) + 1}
    
    # 使用LangGraph 0.5.2的ToolNode
    tool_node = ToolNode(tools)
    
    def should_continue(state: AgentState):
        """判断是否继续执行工具"""
        last_message = state["messages"][-1]
        
        # 检查迭代次数限制
        if state.get("iterations", 0) >= 5:
            logger.warning("Maximum iterations reached", iterations=state.get("iterations"))
            return "end"
        
        # 检查是否有工具调用
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "continue"
        else:
            return "end"
    
    # 定义节点
    workflow.add_node("agent", agent_node)
    workflow.add_node("tools", tool_node)
    
    # 定义边 - LangGraph 0.5.3+ 语法
    workflow.add_edge(START, "agent")
    
    # 使用新的tools_condition或者自定义条件
    workflow.add_conditional_edges(
        "agent",
        should_continue,
        {
            "continue": "tools",
            "end": END,
        },
    )
    workflow.add_edge("tools", "agent")
    
    logger.info("Investment research agent built successfully")
    return workflow.compile()