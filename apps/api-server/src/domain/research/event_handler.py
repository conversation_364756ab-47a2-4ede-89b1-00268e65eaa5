"""AG-UI 事件处理器"""
import json
import time
import uuid
from typing import Dict, Any, Optional
from .models import AGUIEvent

class AGUIEventHandler:
    """AG-UI 事件处理和格式化"""
    
    @staticmethod
    def create_event(
        event_type: str,
        data: Dict[str, Any],
        thread_id: str,
        run_id: str,
        message_id: str = None,
        sub_type: str = None
    ) -> str:
        """创建符合 AG-UI 协议的事件"""
        
        if not message_id:
            message_id = f"msg_{uuid.uuid4()}"
        
        # 构建事件
        event = AGUIEvent(
            type=event_type,
            name=event_type.lower(),
            value=data,
            thread_id=thread_id,
            run_id=run_id,
            message_id=message_id,
            role="assistant",
            timestamp=int(time.time() * 1000)
        )
        
        # 对于自定义事件，添加 subType
        if event_type == "CUSTOM" and sub_type:
            event.value["subType"] = sub_type
        
        return f"data: {json.dumps(event.dict(), ensure_ascii=False)}\n\n"
    
    @staticmethod
    def format_text_message_start(
        message: str,
        thread_id: str,
        run_id: str,
        message_id: str = None
    ) -> str:
        """格式化文本消息开始事件"""
        return AGUIEventHandler.create_event(
            event_type="TEXT_MESSAGE_START",
            data={"message": message},
            thread_id=thread_id,
            run_id=run_id,
            message_id=message_id
        )
    
    @staticmethod
    def format_text_message_end(
        message: str,
        thread_id: str,
        run_id: str,
        message_id: str = None
    ) -> str:
        """格式化文本消息结束事件"""
        return AGUIEventHandler.create_event(
            event_type="TEXT_MESSAGE_END",
            data={"message": message},
            thread_id=thread_id,
            run_id=run_id,
            message_id=message_id
        )
    
    @staticmethod
    def format_custom_event(
        sub_type: str,
        data: Dict[str, Any],
        thread_id: str,
        run_id: str
    ) -> str:
        """格式化自定义事件"""
        return AGUIEventHandler.create_event(
            event_type="CUSTOM",
            data=data,
            thread_id=thread_id,
            run_id=run_id,
            sub_type=sub_type
        )
    
    @staticmethod
    def format_task_status_update(
        task_id: str,
        status: str,
        message: str,
        thread_id: str,
        run_id: str
    ) -> str:
        """格式化任务状态更新事件"""
        return AGUIEventHandler.format_custom_event(
            sub_type="TASK_STATUS_UPDATE",
            data={
                "taskId": task_id,
                "status": status,
                "message": message
            },
            thread_id=thread_id,
            run_id=run_id
        )
    
    @staticmethod
    def format_content_area_update(
        active_task: str,
        task_info: Dict[str, Any],
        status: str,
        data: Optional[Dict[str, Any]],
        thread_id: str,
        run_id: str
    ) -> str:
        """格式化内容区域更新事件"""
        event_data = {
            "activeTask": active_task,
            "taskInfo": task_info,
            "status": status
        }
        if data:
            event_data["data"] = data
            
        return AGUIEventHandler.format_custom_event(
            sub_type="CONTENT_AREA_UPDATE",
            data=event_data,
            thread_id=thread_id,
            run_id=run_id
        )
    
    @staticmethod
    def format_task_data_chunk(
        task_id: str,
        section: str,
        data: Dict[str, Any],
        thread_id: str,
        run_id: str
    ) -> str:
        """格式化任务数据块事件"""
        return AGUIEventHandler.format_custom_event(
            sub_type="TASK_DATA_CHUNK",
            data={
                "taskId": task_id,
                "section": section,
                "data": data
            },
            thread_id=thread_id,
            run_id=run_id
        )