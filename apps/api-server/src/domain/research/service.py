"""重构后的投资研究分析服务"""
from typing import AsyncGenerator, Dict, Any
from .components import TaskManager, ResearchDataProvider, StreamGenerator
from ...infrastructure.logging import get_logger

logger = get_logger(__name__)


class ResearchAnalysisService:
    """投资研究分析服务 - 重构版本"""
    
    def __init__(self):
        self.task_manager = TaskManager()
        self.data_provider = ResearchDataProvider()
        self.stream_generator = StreamGenerator(self.task_manager, self.data_provider)
    
    async def analyze_investment_research(
        self, 
        query: str, 
        thread_id: str, 
        run_id: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """执行投资研究分析 - 流式响应"""
        logger.info("Starting investment research analysis", query=query)
        
        try:
            async for event in self.stream_generator.generate_research_stream(
                query, thread_id, run_id
            ):
                yield event
                
        except Exception as e:
            logger.error("Research analysis failed", error=str(e))
            # 发送错误事件
            yield {
                "type": "ERROR",
                "name": "error",
                "value": {
                    "message": f"研究分析过程中出现错误: {str(e)}"
                },
                "thread_id": thread_id,
                "run_id": run_id
            }
    
    def get_available_tasks(self) -> Dict[str, Any]:
        """获取可用的研究任务列表"""
        return {
            "tasks": self.task_manager.get_ordered_tasks(),
            "total": self.task_manager.get_task_count()
        }
    
    def get_task_data(self, task_id: str) -> Dict[str, Any]:
        """获取指定任务的数据"""
        task_info = self.task_manager.get_task_by_id(task_id)
        task_data = self.data_provider.get_task_data(task_id)
        
        return {
            "task_info": task_info,
            "data": task_data
        }