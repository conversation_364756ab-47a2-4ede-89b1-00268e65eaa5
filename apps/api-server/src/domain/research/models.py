"""投资研究分析相关模型定义"""
from typing import Dict, Any, List, Optional
from enum import Enum
from pydantic import BaseModel, Field
from datetime import datetime

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class ResearchTaskType(str, Enum):
    """研究任务类型枚举"""
    TRUTH_VERIFICATION = "truth-verification"
    IMPACT_SIMULATION = "impact-simulation" 
    THESIS_GENERATION = "thesis-generation"

class ResearchTask(BaseModel):
    """投资研究任务模型"""
    id: str = Field(..., description="任务ID")
    title: str = Field(..., description="任务标题")
    description: str = Field(..., description="任务描述")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    order: int = Field(..., description="执行顺序")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

class ResearchRequest(BaseModel):
    """投资研究分析请求"""
    thread_id: Optional[str] = Field(None, description="会话线程ID")
    run_id: Optional[str] = Field(None, description="运行ID")
    messages: List[Dict[str, Any]] = Field(default_factory=list, description="消息列表")
    tools: List[Dict[str, Any]] = Field(default_factory=list, description="工具列表")
    context: List[Dict[str, Any]] = Field(default_factory=list, description="上下文信息")
    state: Dict[str, Any] = Field(default_factory=dict, description="状态数据")
    forwarded_props: Dict[str, Any] = Field(default_factory=dict, description="转发的属性")

class ResearchData(BaseModel):
    """研究分析数据"""
    truth_verification: Optional[Dict[str, Any]] = Field(None, description="真实性验证结果")
    impact_simulation: Optional[Dict[str, Any]] = Field(None, description="影响模拟分析结果")
    thesis_generation: Optional[Dict[str, Any]] = Field(None, description="投资论文生成结果")

class AGUIEvent(BaseModel):
    """AG-UI 事件模型"""
    type: str = Field(..., description="事件类型")
    name: Optional[str] = Field(None, description="事件名称")
    value: Optional[Dict[str, Any]] = Field(None, description="事件数据")
    thread_id: Optional[str] = Field(None, description="线程ID")
    run_id: Optional[str] = Field(None, description="运行ID")
    message_id: Optional[str] = Field(None, description="消息ID")
    role: Optional[str] = Field(None, description="角色")
    timestamp: Optional[int] = Field(None, description="时间戳")