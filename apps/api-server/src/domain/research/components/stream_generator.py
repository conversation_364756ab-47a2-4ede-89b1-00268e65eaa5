"""流式响应生成器"""
import asyncio
import uuid
from typing import AsyncGenerator, Dict, Any
from ..models import TaskStatus
from ...infrastructure.logging import get_logger

logger = get_logger(__name__)


class StreamGenerator:
    """流式响应生成器"""
    
    def __init__(self, task_manager, data_provider):
        self.task_manager = task_manager
        self.data_provider = data_provider
    
    async def generate_research_stream(
        self, 
        query: str, 
        thread_id: str, 
        run_id: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成研究分析流式响应"""
        logger.info("Starting research analysis stream", query=query)
        
        # 1. 开始消息
        yield {
            "type": "TEXT_MESSAGE_START",
            "name": "text_message_start",
            "value": {
                "message": f"正在分析: {query}"
            },
            "thread_id": thread_id,
            "run_id": run_id,
            "message_id": f"msg_{uuid.uuid4()}",
            "role": "assistant"
        }
        
        # 2. 初始化研究任务
        yield {
            "type": "CUSTOM",
            "name": "custom_event",
            "value": {
                "subType": "RESEARCH_ANALYSIS_START",
                "message": "开始投资研究分析",
                "tasks": list(self.task_manager.get_all_tasks().values()),
                "total": self.task_manager.get_task_count()
            },
            "thread_id": thread_id,
            "run_id": run_id
        }
        
        await asyncio.sleep(1)
        
        # 3. 逐个执行任务
        for task_info in self.task_manager.get_ordered_tasks():
            yield from self._process_single_task(task_info, thread_id, run_id)
        
        # 4. 所有任务完成
        yield {
            "type": "CUSTOM",
            "name": "custom_event",
            "value": {
                "subType": "RESEARCH_ANALYSIS_COMPLETE",
                "message": "投资研究分析已全部完成",
                "summary": {
                    "totalTasks": self.task_manager.get_task_count(),
                    "completedTasks": self.task_manager.get_task_count(),
                    "duration": "约 10 秒"
                }
            },
            "thread_id": thread_id,
            "run_id": run_id
        }
        
        # 5. 结束消息
        yield {
            "type": "TEXT_MESSAGE_END",
            "name": "text_message_end",
            "value": {
                "message": "研究分析报告已生成完毕，可以查看详细结果。"
            },
            "thread_id": thread_id,
            "run_id": run_id,
            "message_id": f"msg_{uuid.uuid4()}",
            "role": "assistant"
        }
    
    async def _process_single_task(
        self, 
        task_info: Dict[str, Any], 
        thread_id: str, 
        run_id: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理单个任务"""
        task_id = task_info["id"]
        
        # 任务开始
        yield {
            "type": "CUSTOM",
            "name": "custom_event", 
            "value": {
                "subType": "TASK_STATUS_UPDATE",
                "taskId": task_id,
                "status": TaskStatus.IN_PROGRESS,
                "message": f"正在执行 {task_info['title']}..."
            },
            "thread_id": thread_id,
            "run_id": run_id
        }
        
        # 更新右侧内容区域显示当前任务
        yield {
            "type": "CUSTOM",
            "name": "custom_event",
            "value": {
                "subType": "CONTENT_AREA_UPDATE",
                "activeTask": task_id,
                "taskInfo": task_info,
                "status": TaskStatus.IN_PROGRESS
            },
            "thread_id": thread_id,
            "run_id": run_id
        }
        
        await asyncio.sleep(2)  # 模拟处理时间
        
        # 发送任务具体数据
        if self.data_provider.has_task_data(task_id):
            task_data = self.data_provider.get_task_data(task_id)
            
            # 分块发送数据，模拟实时生成
            for section_key, section_data in task_data.items():
                yield {
                    "type": "CUSTOM",
                    "name": "custom_event",
                    "value": {
                        "subType": "TASK_DATA_CHUNK",
                        "taskId": task_id,
                        "section": section_key,
                        "data": section_data
                    },
                    "thread_id": thread_id,
                    "run_id": run_id
                }
                await asyncio.sleep(0.5)  # 分块间隔
        
        # 任务完成
        yield {
            "type": "CUSTOM",
            "name": "custom_event",
            "value": {
                "subType": "TASK_STATUS_UPDATE",
                "taskId": task_id,
                "status": TaskStatus.COMPLETED,
                "message": f"{task_info['title']} 已完成"
            },
            "thread_id": thread_id,
            "run_id": run_id
        }
        
        await asyncio.sleep(1)