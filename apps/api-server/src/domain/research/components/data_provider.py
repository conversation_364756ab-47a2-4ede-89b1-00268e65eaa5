"""研究分析数据提供者"""
from typing import Dict, Any, Optional


class ResearchDataProvider:
    """研究分析数据提供者"""
    
    # 模拟的分析数据
    ANALYSIS_DATA = {
        "truth-verification": {
            "verification_items": [
                {"icon": "✓", "text": "官方确认: 特斯拉官方微博已发布降价公告", "color": "text-green-600"},
                {"icon": "✓", "text": "数据核实: Model 3/Y 降价幅度 18-22%", "color": "text-green-600"},
                {"icon": "✓", "text": "市场反应: 盘前交易量增加 340%", "color": "text-green-600"}
            ],
            "official_info": [
                "特斯拉官方微博于2024年1月12日10:30发布降价公告",
                "Model 3起售价从29.09万元降至26.59万元，降幅8.6%",
                "Model Y起售价从34.79万元降至28.89万元，降幅17.0%",
                "降价立即生效，全国范围内执行统一价格"
            ],
            "market_analysis": [
                "降价消息发布后30分钟内，特斯拉概念股集体异动",
                "蔚来汽车跌幅达-5.2%，理想汽车下跌-3.8%",
                "传统车企股价普遍下行：比亚迪-2.1%，广汽集团-4.3%",
                "电池供应商逆势上涨：宁德时代+2.8%，国轩高科+1.9%"
            ],
            "credibility_scores": {
                "authority": {"score": 5, "desc": "官方发布"},
                "timeliness": {"score": 5, "desc": "实时信息"},
                "accuracy": {"score": 4, "desc": "部分地区价格存在差异"},
                "impact_scope": {"score": 5, "desc": "全国性政策调整"}
            }
        },
        "impact-simulation": {
            "market_impact": {
                "immediate": {
                    "stock_changes": [
                        {"symbol": "TSLA", "change": "+5.2%", "volume": "+340%"},
                        {"symbol": "NIO", "change": "-5.2%", "volume": "+180%"},
                        {"symbol": "LI", "change": "-3.8%", "volume": "+120%"}
                    ],
                    "sector_rotation": [
                        {"sector": "新能源汽车", "impact": "分化明显"},
                        {"sector": "传统汽车", "impact": "承压下行"},
                        {"sector": "电池产业链", "impact": "逆势上涨"}
                    ]
                },
                "medium_term": {
                    "competitive_landscape": [
                        "价格战可能蔓延至整个新能源汽车行业",
                        "中低端市场竞争将更加激烈",
                        "技术领先企业议价能力增强"
                    ],
                    "industry_consolidation": [
                        "资金实力较弱的新势力面临洗牌风险",
                        "头部企业市场集中度进一步提升",
                        "供应链整合速度加快"
                    ]
                }
            },
            "financial_projections": {
                "tesla": {
                    "revenue_impact": "短期营收可能下降5-8%，但销量预计增长15-20%",
                    "margin_pressure": "毛利率预计下降2-3个百分点",
                    "market_share": "中国市场份额有望从12%提升至15%"
                },
                "competitors": {
                    "nio": "面临更大价格压力，可能被迫跟进降价",
                    "byd": "凭借成本优势，预计影响相对有限",
                    "traditional": "燃油车向新能源转型压力加大"
                }
            }
        },
        "thesis-generation": {
            "investment_thesis": {
                "core_argument": "特斯拉降价策略体现其战略转型：从追求高毛利向规模效应转变",
                "supporting_points": [
                    "技术成熟度提升，成本下降空间释放",
                    "市场竞争加剧，需要价格竞争力",
                    "产能利用率有待提升，规模效应待释放"
                ],
                "risk_factors": [
                    "毛利率压缩影响短期盈利能力",
                    "品牌价值可能受到冲击",
                    "竞争对手跟进可能引发价格战"
                ]
            },
            "recommendation": {
                "rating": "Buy",
                "target_price": "$220-240",
                "time_horizon": "12个月",
                "rationale": "短期承压，长期受益于市场份额提升和成本优化"
            },
            "scenario_analysis": {
                "bull_case": "成功获得市场份额，成本持续下降，目标价$260",
                "base_case": "稳定增长，适度盈利改善，目标价$230", 
                "bear_case": "价格战升级，盈利能力恶化，目标价$180"
            }
        }
    }
    
    def get_task_data(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取指定任务的分析数据"""
        return self.ANALYSIS_DATA.get(task_id)
    
    def has_task_data(self, task_id: str) -> bool:
        """检查是否有指定任务的数据"""
        return task_id in self.ANALYSIS_DATA
    
    def get_all_data(self) -> Dict[str, Dict[str, Any]]:
        """获取所有分析数据"""
        return self.ANALYSIS_DATA