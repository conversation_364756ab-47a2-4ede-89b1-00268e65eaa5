"""研究任务管理器"""
from typing import Dict, Any, List
from ..models import ResearchTaskType


class TaskManager:
    """研究任务管理器"""
    
    # 研究任务定义
    RESEARCH_TASKS = {
        ResearchTaskType.TRUTH_VERIFICATION: {
            "id": "truth-verification",
            "title": "Truth Verification",
            "description": "调查核实事件真假",
            "order": 1
        },
        ResearchTaskType.IMPACT_SIMULATION: {
            "id": "impact-simulation", 
            "title": "Impact Simulation",
            "description": "影响模拟分析",
            "order": 2
        },
        ResearchTaskType.THESIS_GENERATION: {
            "id": "thesis-generation",
            "title": "Thesis Generation", 
            "description": "投资论文生成",
            "order": 3
        }
    }
    
    def get_all_tasks(self) -> Dict[ResearchTaskType, Dict[str, Any]]:
        """获取所有研究任务"""
        return self.RESEARCH_TASKS
    
    def get_task_by_id(self, task_id: str) -> Dict[str, Any]:
        """根据ID获取任务信息"""
        for task_info in self.RESEARCH_TASKS.values():
            if task_info["id"] == task_id:
                return task_info
        raise ValueError(f"Task not found: {task_id}")
    
    def get_ordered_tasks(self) -> List[Dict[str, Any]]:
        """获取按顺序排列的任务列表"""
        tasks = list(self.RESEARCH_TASKS.values())
        return sorted(tasks, key=lambda x: x["order"])
    
    def get_task_count(self) -> int:
        """获取任务总数"""
        return len(self.RESEARCH_TASKS)