from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional


class FinancialDataProvider(ABC):
    """财务数据提供者接口"""
    
    @abstractmethod
    async def get_financial_data(
        self, 
        symbol: str, 
        data_type: str = "all"
    ) -> Dict[str, Any]:
        """获取财务数据"""
        pass


class MarketDataProvider(ABC):
    """市场数据提供者接口"""
    
    @abstractmethod
    async def get_market_data(
        self, 
        symbol: str, 
        period: str = "1d"
    ) -> Dict[str, Any]:
        """获取市场数据"""
        pass


class NewsSearchProvider(ABC):
    """新闻搜索提供者接口"""
    
    @abstractmethod
    async def search_news(
        self, 
        query: str, 
        limit: int = 10,
        date_range: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """搜索新闻"""
        pass


class ResearchTool(ABC):
    """研究工具基础接口"""
    
    @abstractmethod
    def get_name(self) -> str:
        """获取工具名称"""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """获取工具描述"""
        pass
    
    @abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """执行工具"""
        pass