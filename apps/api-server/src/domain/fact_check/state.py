"""
事实核查系统的状态模型
定义LangGraph状态机中使用的数据结构
"""
from typing import Dict, List, Optional, Any, Union
from typing_extensions import TypedDict, Annotated
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import uuid

class VerificationStatus(Enum):
    """验证状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    VERIFIED = "verified"
    CONTRADICTED = "contradicted"
    CONFLICTED = "conflicted"
    UNVERIFIED = "unverified"
    ERROR = "error"

class ClaimType(Enum):
    """声明类型枚举"""
    FINANCIAL_DATA = "financial_data"
    CORPORATE_STATEMENT = "corporate_statement"
    MARKET_PREDICTION = "market_prediction" 
    NEWS_FACT = "news_fact"
    GENERAL = "general"

class AgentRole(Enum):
    """Agent角色枚举"""
    ORCHESTRATOR = "orchestrator"
    CLAIM_EXTRACTOR = "claim_extractor"
    FINANCIAL_VERIFIER = "financial_verifier"
    CORPORATE_VERIFIER = "corporate_verifier"
    NEWS_VERIFIER = "news_verifier"
    DEBATE_MODERATOR = "debate_moderator"
    SYNTHESIS_AGENT = "synthesis_agent"

@dataclass
class DataSource:
    """数据源信息"""
    name: str
    url: Optional[str] = None
    reliability_score: float = 0.0  # 0-1
    access_date: Optional[datetime] = None
    api_cost: float = 0.0

@dataclass 
class Claim:
    """待核查的声明"""
    id: str
    text: str
    claim_type: ClaimType
    status: VerificationStatus = VerificationStatus.PENDING
    confidence: float = 0.0  # 0-1
    sources: List[DataSource] = None
    verification_result: Optional[Dict[str, Any]] = None
    assigned_agents: List[AgentRole] = None
    
    def __post_init__(self):
        if self.sources is None:
            self.sources = []
        if self.assigned_agents is None:
            self.assigned_agents = []
        if not self.id:
            self.id = str(uuid.uuid4())

@dataclass
class DebateMessage:
    """辩论消息"""
    agent_role: AgentRole
    message: str
    evidence: List[str] = None
    support_level: float = 0.0  # 0-1，对争议点的支持程度
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.evidence is None:
            self.evidence = []
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class DebateRecord:
    """辩论记录"""
    id: str
    claim_id: str
    topic: str
    participants: List[AgentRole]
    messages: List[DebateMessage] = None
    conclusion: Optional[str] = None
    confidence: float = 0.0
    status: str = "active"  # active, concluded
    
    def __post_init__(self):
        if self.messages is None:
            self.messages = []
        if not self.id:
            self.id = str(uuid.uuid4())

@dataclass
class AgentStatus:
    """Agent状态"""
    role: AgentRole
    status: str = "idle"  # idle, working, completed, error
    progress: float = 0.0  # 0-1
    current_task: Optional[str] = None
    last_update: datetime = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.last_update is None:
            self.last_update = datetime.now()

# LangGraph状态类型定义
class FactCheckState(TypedDict):
    """事实核查的完整状态"""
    # 任务基本信息
    task_id: str
    original_text: str
    
    # 声明和验证结果
    claims: List[Claim]
    
    # Agent状态
    agents: Dict[str, AgentStatus]
    
    # 辩论记录
    debates: List[DebateRecord]
    
    # 执行状态
    current_step: str
    is_completed: bool
    has_conflicts: bool
    
    # 成本追踪
    total_cost: float
    cost_breakdown: Dict[str, float]  # 按Agent角色分组的成本
    
    # 配置选项
    options: Dict[str, Any]
    
    # 最终结果
    final_report: Optional[Dict[str, Any]]
    
    # 错误信息
    errors: List[str]
    
    # 时间戳
    start_time: datetime
    end_time: Optional[datetime]

def create_initial_state(
    task_id: str,
    original_text: str,
    options: Optional[Dict[str, Any]] = None
) -> FactCheckState:
    """创建初始状态"""
    if options is None:
        options = {
            "enable_financial_verification": True,
            "enable_corporate_verification": True,
            "enable_news_verification": True,
            "deep_research_mode": False
        }
    
    return FactCheckState(
        task_id=task_id,
        original_text=original_text,
        claims=[],
        agents={
            role.value: AgentStatus(role=role)
            for role in AgentRole
        },
        debates=[],
        current_step="initialization",
        is_completed=False,
        has_conflicts=False,
        total_cost=0.0,
        cost_breakdown={role.value: 0.0 for role in AgentRole},
        options=options,
        final_report=None,
        errors=[],
        start_time=datetime.now(),
        end_time=None
    )