"""
事实核查LangGraph工作流
编排整个事实核查流程的状态机
"""
import logging
from typing import Dict, Any
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .state import FactCheckState, create_initial_state, AgentRole
from .extractors.claim_extractor_factory import ClaimExtractorFactory
from .verifiers.financial_verifier_orchestrator import FinancialVerifierOrchestrator
from ...infrastructure.adapters.agui_event_sender import agui_event_sender

logger = logging.getLogger(__name__)

class FactCheckWorkflow:
    """事实核查工作流"""
    
    def __init__(self):
        self.claim_extractor = ClaimExtractorFactory()
        self.financial_verifier = FinancialVerifierOrchestrator()
        
        # 创建状态图
        self.workflow = self._create_workflow()
        
        # 添加检查点保存器（可选，用于持久化状态）
        self.checkpointer = MemorySaver()
        
        # 编译工作流
        self.app = self.workflow.compile(checkpointer=self.checkpointer)
    
    def _create_workflow(self) -> StateGraph:
        """创建LangGraph工作流"""
        
        # 创建状态图
        workflow = StateGraph(FactCheckState)
        
        # 添加节点
        workflow.add_node("extract_claims", self._extract_claims_node)
        workflow.add_node("verify_financial", self._verify_financial_node)
        workflow.add_node("check_conflicts", self._check_conflicts_node)
        workflow.add_node("generate_report", self._generate_report_node)
        
        # 定义工作流程
        workflow.set_entry_point("extract_claims")
        
        # 声明提取 -> 财务验证
        workflow.add_edge("extract_claims", "verify_financial")
        
        # 财务验证 -> 冲突检查
        workflow.add_edge("verify_financial", "check_conflicts")
        
        # 冲突检查 -> 生成报告
        workflow.add_edge("check_conflicts", "generate_report")
        
        # 生成报告 -> 结束
        workflow.add_edge("generate_report", END)
        
        return workflow
    
    async def _extract_claims_node(self, state: FactCheckState) -> FactCheckState:
        """声明提取节点"""
        logger.info(f"执行声明提取节点，任务ID: {state['task_id']}")
        
        state["current_step"] = "extract_claims"
        
        # 发送任务开始事件
        await agui_event_sender.send_task_started(
            task_id=state["task_id"],
            task_data={
                "original_text": state["original_text"],
                "options": state["options"],
                "start_time": state["start_time"].isoformat()
            }
        )
        
        # 执行声明提取
        claims = await self.claim_extractor.extract_all_claims(
            text=state["original_text"],
            context={"options": state["options"]}
        )
        state["claims"] = claims
        
        return state
    
    async def _verify_financial_node(self, state: FactCheckState) -> FactCheckState:
        """财务验证节点"""
        logger.info(f"执行财务验证节点，任务ID: {state['task_id']}")
        
        state["current_step"] = "verify_financial"
        
        # 只有启用财务验证时才执行
        if state["options"].get("enable_financial_verification", True):
            verification_results = await self.financial_verifier.verify_claims(
                claims=state["claims"],
                context={"options": state["options"]}
            )
            # 更新声明的验证结果
            for i, result in enumerate(verification_results):
                if i < len(state["claims"]):
                    state["claims"][i].verification_result = result
            return state
        else:
            logger.info("财务验证已禁用，跳过")
            return state
    
    async def _check_conflicts_node(self, state: FactCheckState) -> FactCheckState:
        """冲突检查节点"""
        logger.info(f"执行冲突检查节点，任务ID: {state['task_id']}")
        
        state["current_step"] = "check_conflicts"
        
        # 简单的冲突检测逻辑
        has_conflicts = False
        conflicted_claims = []
        
        for claim in state["claims"]:
            if claim.status.value == "contradicted":
                has_conflicts = True
                conflicted_claims.append(claim)
        
        state["has_conflicts"] = has_conflicts
        
        if has_conflicts:
            logger.info(f"发现 {len(conflicted_claims)} 个冲突声明")
            # TODO: 在这里可以启动辩论模块
        
        return state
    
    async def _generate_report_node(self, state: FactCheckState) -> FactCheckState:
        """生成报告节点"""
        logger.info(f"执行报告生成节点，任务ID: {state['task_id']}")
        
        state["current_step"] = "generate_report"
        state["end_time"] = datetime.now()
        
        # 生成最终报告
        final_report = {
            "task_id": state["task_id"],
            "original_text": state["original_text"],
            "claims_summary": {
                "total_claims": len(state["claims"]),
                "verified": len([c for c in state["claims"] if c.status.value == "verified"]),
                "contradicted": len([c for c in state["claims"] if c.status.value == "contradicted"]),
                "unverified": len([c for c in state["claims"] if c.status.value == "unverified"]),
                "errors": len([c for c in state["claims"] if c.status.value == "error"])
            },
            "claims": [
                {
                    "id": claim.id,
                    "text": claim.text,
                    "type": claim.claim_type.value,
                    "status": claim.status.value,
                    "confidence": claim.confidence,
                    "verification_result": claim.verification_result,
                    "sources": [
                        {
                            "name": source.name,
                            "reliability_score": source.reliability_score,
                            "url": source.url
                        } for source in claim.sources
                    ]
                } for claim in state["claims"]
            ],
            "has_conflicts": state["has_conflicts"],
            "total_cost": state["total_cost"],
            "processing_time": (state["end_time"] - state["start_time"]).total_seconds(),
            "timestamp": datetime.now().isoformat()
        }
        
        state["final_report"] = final_report
        state["is_completed"] = True
        
        # 发送任务完成事件
        await agui_event_sender.send_task_complete(
            task_id=state["task_id"],
            final_result=final_report,
            total_cost=state["total_cost"]
        )
        
        logger.info(f"事实核查任务完成，任务ID: {state['task_id']}")
        
        return state
    
    async def run_fact_check(
        self,
        task_id: str,
        text: str,
        options: Dict[str, Any] = None
    ) -> FactCheckState:
        """运行事实核查工作流"""
        
        logger.info(f"启动事实核查工作流，任务ID: {task_id}")
        
        # 创建初始状态
        initial_state = create_initial_state(task_id, text, options)
        
        try:
            # 运行工作流
            config = {"configurable": {"thread_id": task_id}}
            
            # 使用流式执行以便实时更新
            async for chunk in self.app.astream(initial_state, config=config):
                logger.debug(f"工作流状态更新: {chunk}")
                
                # 这里可以处理中间状态更新
                for node_name, node_state in chunk.items():
                    if node_name != "__end__":
                        logger.info(f"节点 {node_name} 执行完成")
            
            # 获取最终状态
            final_state = await self.app.aget_state(config)
            
            return final_state.values
            
        except Exception as e:
            error_msg = f"工作流执行失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            # 发送错误事件
            await agui_event_sender.send_error(
                task_id=task_id,
                error_message=error_msg
            )
            
            raise

# 全局工作流实例（延迟初始化）
fact_check_workflow = None

def get_fact_check_workflow():
    """获取事实核查工作流实例，支持延迟初始化"""
    global fact_check_workflow
    if fact_check_workflow is None:
        fact_check_workflow = FactCheckWorkflow()
    return fact_check_workflow