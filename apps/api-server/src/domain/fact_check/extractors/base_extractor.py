"""基础声明提取器"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any
from pydantic import BaseModel, Field

from ..state import Claim, ClaimType


class ClaimOutput(BaseModel):
    """声明输出模型"""
    text: str = Field(description="提取的声明文本")
    type: str = Field(description="声明类型")
    confidence: float = Field(description="提取置信度 (0-1)", ge=0, le=1)
    context: str = Field(description="声明的上下文信息")


class ClaimsListOutput(BaseModel):
    """声明列表输出模型"""
    claims: List[ClaimOutput] = Field(description="提取的声明列表")
    total_count: int = Field(description="声明总数")


class BaseClaimExtractor(ABC):
    """基础声明提取器抽象类"""
    
    @abstractmethod
    def get_supported_types(self) -> List[ClaimType]:
        """获取支持的声明类型"""
        pass
    
    @abstractmethod
    async def extract_claims(
        self, 
        text: str, 
        context: Dict[str, Any] = None
    ) -> List[Claim]:
        """提取声明"""
        pass
    
    @abstractmethod
    def get_extraction_prompt(self) -> str:
        """获取提取提示词"""
        pass
    
    def _create_claim(
        self, 
        claim_output: ClaimOutput, 
        source_text: str
    ) -> Claim:
        """创建声明对象"""
        claim_type = ClaimType.from_string(claim_output.type)
        return Claim(
            text=claim_output.text,
            type=claim_type,
            confidence=claim_output.confidence,
            context=claim_output.context,
            source_text=source_text
        )