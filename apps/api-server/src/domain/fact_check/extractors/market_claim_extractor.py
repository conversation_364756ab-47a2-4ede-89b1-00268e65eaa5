"""市场预测声明提取器"""
import logging
from typing import List, Dict, Any

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from .base_extractor import BaseClaimExtractor, ClaimsListOutput
from ..state import Claim, ClaimType
from ....infrastructure.services.llm import LLMFactory

logger = logging.getLogger(__name__)


class MarketClaimExtractor(BaseClaimExtractor):
    """市场预测相关声明提取器"""
    
    def __init__(self):
        try:
            self.llm = LLMFactory.create_claude4_llm(
                temperature=0.1,
                task_type="market_claim_extraction"
            )
            self.is_available = True
        except ValueError as e:
            logger.warning(f"MarketClaimExtractor初始化失败: {e}")
            self.llm = None
            self.is_available = False
        
        self.output_parser = JsonOutputParser(pydantic_object=ClaimsListOutput)
    
    def get_supported_types(self) -> List[ClaimType]:
        """获取支持的声明类型"""
        return [ClaimType.MARKET_PREDICTION]
    
    def get_extraction_prompt(self) -> str:
        """获取市场预测声明提取提示词"""
        return """
你是一个专业的市场分析声明提取专家。请从以下文本中提取所有与市场预测相关的可验证声明。

重点关注：
1. 股价预测（目标价、涨跌幅预期）
2. 市场趋势预测（牛熊市、板块轮动）
3. 经济指标预测（GDP、通胀、利率）
4. 投资建议（买入、持有、卖出评级）

提取规则：
- 每个声明应该是明确的预测或建议
- 包含具体的时间框架和预期结果
- 区分事实陈述和预测性声明
- 保留分析师或机构的预测来源

文本内容：
{text}

请按以下JSON格式返回结果：
{format_instructions}
"""
    
    async def extract_claims(
        self, 
        text: str, 
        context: Dict[str, Any] = None
    ) -> List[Claim]:
        """提取市场预测相关声明"""
        if not self.is_available:
            logger.warning("MarketClaimExtractor不可用，跳过提取")
            return []
        
        try:
            prompt_template = ChatPromptTemplate.from_template(
                self.get_extraction_prompt()
            )
            
            chain = prompt_template | self.llm | self.output_parser
            
            result = await chain.ainvoke({
                "text": text,
                "format_instructions": self.output_parser.get_format_instructions()
            })
            
            claims = []
            for claim_output in result.claims:
                # 只处理市场预测相关的声明
                if claim_output.type == "market_prediction":
                    claim = self._create_claim(claim_output, text)
                    claims.append(claim)
            
            logger.info(f"提取到 {len(claims)} 个市场预测声明")
            return claims
            
        except Exception as e:
            logger.error(f"市场预测声明提取失败: {e}")
            return []