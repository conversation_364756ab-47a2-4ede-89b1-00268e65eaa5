"""财务声明提取器"""
import logging
from typing import List, Dict, Any

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from .base_extractor import BaseClaimExtractor, ClaimsListOutput
from ..state import Claim, ClaimType
from ....infrastructure.services.llm import LLMFactory

logger = logging.getLogger(__name__)


class FinancialClaimExtractor(BaseClaimExtractor):
    """财务相关声明提取器"""
    
    def __init__(self):
        try:
            self.llm = LLMFactory.create_claude4_llm(
                temperature=0.1,
                task_type="financial_claim_extraction"
            )
            self.is_available = True
        except ValueError as e:
            logger.warning(f"FinancialClaimExtractor初始化失败: {e}")
            self.llm = None
            self.is_available = False
        
        self.output_parser = JsonOutputParser(pydantic_object=ClaimsListOutput)
    
    def get_supported_types(self) -> List[ClaimType]:
        """获取支持的声明类型"""
        return [ClaimType.FINANCIAL_DATA, ClaimType.CORPORATE_STATEMENT]
    
    def get_extraction_prompt(self) -> str:
        """获取财务声明提取提示词"""
        return """
你是一个专业的财务声明提取专家。请从以下文本中提取所有与财务相关的可验证声明。

重点关注：
1. 财务数据声明（收入、利润、市值、股价等）
2. 公司声明（业务计划、战略、产品发布等）
3. 具有明确数值或可验证事实的声明

提取规则：
- 每个声明应该是独立的、可验证的事实
- 包含具体的数字、时间、公司名称
- 避免主观性评价或模糊表述
- 保持声明的完整性和准确性

文本内容：
{text}

请按以下JSON格式返回结果：
{format_instructions}
"""
    
    async def extract_claims(
        self, 
        text: str, 
        context: Dict[str, Any] = None
    ) -> List[Claim]:
        """提取财务相关声明"""
        if not self.is_available:
            logger.warning("FinancialClaimExtractor不可用，跳过提取")
            return []
        
        try:
            prompt_template = ChatPromptTemplate.from_template(
                self.get_extraction_prompt()
            )
            
            chain = prompt_template | self.llm | self.output_parser
            
            result = await chain.ainvoke({
                "text": text,
                "format_instructions": self.output_parser.get_format_instructions()
            })
            
            claims = []
            for claim_output in result.claims:
                # 只处理财务相关的声明
                if claim_output.type in ["financial_data", "corporate_statement"]:
                    claim = self._create_claim(claim_output, text)
                    claims.append(claim)
            
            logger.info(f"提取到 {len(claims)} 个财务相关声明")
            return claims
            
        except Exception as e:
            logger.error(f"财务声明提取失败: {e}")
            return []