"""新闻事实声明提取器"""
import logging
from typing import List, Dict, Any

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from .base_extractor import BaseClaimExtractor, ClaimsListOutput
from ..state import Claim, ClaimType
from ....infrastructure.services.llm import LLMFactory

logger = logging.getLogger(__name__)


class NewsClaimExtractor(BaseClaimExtractor):
    """新闻事实相关声明提取器"""
    
    def __init__(self):
        try:
            self.llm = LLMFactory.create_claude4_llm(
                temperature=0.1,
                task_type="news_claim_extraction"
            )
            self.is_available = True
        except ValueError as e:
            logger.warning(f"NewsClaimExtractor初始化失败: {e}")
            self.llm = None
            self.is_available = False
        
        self.output_parser = JsonOutputParser(pydantic_object=ClaimsListOutput)
    
    def get_supported_types(self) -> List[ClaimType]:
        """获取支持的声明类型"""
        return [ClaimType.NEWS_FACT, ClaimType.GENERAL]
    
    def get_extraction_prompt(self) -> str:
        """获取新闻事实声明提取提示词"""
        return """
你是一个专业的新闻事实声明提取专家。请从以下文本中提取所有与新闻事实相关的可验证声明。

重点关注：
1. 新闻事实（事件发生、人事变动、政策发布）
2. 一般事实声明（历史数据、公开信息）
3. 具有明确时间和来源的事实陈述
4. 可通过公开渠道验证的信息

提取规则：
- 每个声明应该是客观的事实陈述
- 包含具体的时间、地点、人物
- 区分事实和观点/评论
- 保留新闻来源和发布时间信息

文本内容：
{text}

请按以下JSON格式返回结果：
{format_instructions}
"""
    
    async def extract_claims(
        self, 
        text: str, 
        context: Dict[str, Any] = None
    ) -> List[Claim]:
        """提取新闻事实相关声明"""
        if not self.is_available:
            logger.warning("NewsClaimExtractor不可用，跳过提取")
            return []
        
        try:
            prompt_template = ChatPromptTemplate.from_template(
                self.get_extraction_prompt()
            )
            
            chain = prompt_template | self.llm | self.output_parser
            
            result = await chain.ainvoke({
                "text": text,
                "format_instructions": self.output_parser.get_format_instructions()
            })
            
            claims = []
            for claim_output in result.claims:
                # 只处理新闻事实相关的声明
                if claim_output.type in ["news_fact", "general"]:
                    claim = self._create_claim(claim_output, text)
                    claims.append(claim)
            
            logger.info(f"提取到 {len(claims)} 个新闻事实声明")
            return claims
            
        except Exception as e:
            logger.error(f"新闻事实声明提取失败: {e}")
            return []