"""声明提取器工厂"""
import logging
from typing import List, Dict, Any

from .base_extractor import BaseClaimExtractor
from .financial_claim_extractor import FinancialClaimExtractor
from .market_claim_extractor import MarketClaimExtractor
from .news_claim_extractor import NewsClaimExtractor
from ..state import Claim, ClaimType, AgentRole, AgentStatus

logger = logging.getLogger(__name__)


class ClaimExtractorFactory:
    """声明提取器工厂 - 协调多个专业提取器"""
    
    def __init__(self):
        self.role = AgentRole.CLAIM_EXTRACTOR
        self.status = AgentStatus.IDLE
        self._extractors = {
            'financial': FinancialClaimExtractor(),
            'market': MarketClaimExtractor(), 
            'news': NewsClaimExtractor()
        }
    
    async def extract_all_claims(
        self, 
        text: str, 
        context: Dict[str, Any] = None
    ) -> List[Claim]:
        """使用所有提取器提取声明"""
        self.status = AgentStatus.PROCESSING
        all_claims = []
        
        try:
            # 并行使用所有提取器
            for extractor_type, extractor in self._extractors.items():
                try:
                    claims = await extractor.extract_claims(text, context)
                    all_claims.extend(claims)
                    logger.info(f"{extractor_type}提取器提取到 {len(claims)} 个声明")
                except Exception as e:
                    logger.error(f"{extractor_type}提取器执行失败: {e}")
                    continue
            
            # 去重和排序
            unique_claims = self._deduplicate_claims(all_claims)
            
            self.status = AgentStatus.COMPLETED
            logger.info(f"总共提取到 {len(unique_claims)} 个唯一声明")
            return unique_claims
            
        except Exception as e:
            self.status = AgentStatus.ERROR
            logger.error(f"声明提取过程失败: {e}")
            return []
    
    async def extract_by_type(
        self, 
        text: str, 
        claim_types: List[ClaimType],
        context: Dict[str, Any] = None
    ) -> List[Claim]:
        """按指定类型提取声明"""
        relevant_extractors = []
        
        # 选择相关的提取器
        for extractor in self._extractors.values():
            supported_types = extractor.get_supported_types()
            if any(ct in supported_types for ct in claim_types):
                relevant_extractors.append(extractor)
        
        all_claims = []
        for extractor in relevant_extractors:
            try:
                claims = await extractor.extract_claims(text, context)
                # 过滤只返回指定类型的声明
                filtered_claims = [
                    claim for claim in claims 
                    if claim.type in claim_types
                ]
                all_claims.extend(filtered_claims)
            except Exception as e:
                logger.error(f"提取器执行失败: {e}")
                continue
        
        return self._deduplicate_claims(all_claims)
    
    def _deduplicate_claims(self, claims: List[Claim]) -> List[Claim]:
        """去重声明"""
        seen_texts = set()
        unique_claims = []
        
        for claim in claims:
            # 简单的文本去重
            normalized_text = claim.text.strip().lower()
            if normalized_text not in seen_texts:
                seen_texts.add(normalized_text)
                unique_claims.append(claim)
        
        # 按置信度排序
        return sorted(unique_claims, key=lambda x: x.confidence, reverse=True)
    
    def get_available_extractors(self) -> Dict[str, bool]:
        """获取可用的提取器状态"""
        return {
            name: extractor.is_available 
            for name, extractor in self._extractors.items()
        }