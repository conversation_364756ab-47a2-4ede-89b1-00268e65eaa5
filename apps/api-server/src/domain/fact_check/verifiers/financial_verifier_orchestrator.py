"""财务验证器编排器 - 协调多个验证器的工作"""
import logging
from typing import List, Dict, Any
import asyncio

from .base_verifier import BaseVerifier, VerificationResult
from .data_verifier import DataVerifier
from .source_verifier import SourceVerifier
from .consistency_verifier import ConsistencyVerifier
from ..state import Claim, AgentRole, AgentStatus, VerificationStatus

logger = logging.getLogger(__name__)


class FinancialVerifierOrchestrator:
    """财务验证器编排器 - 组合多个专业验证器提供完整的验证流程"""
    
    def __init__(self):
        self.role = AgentRole.FINANCIAL_VERIFIER
        self.status = AgentStatus.IDLE
        
        # 初始化各个验证器
        self._verifiers = {
            'data': DataVerifier(),
            'source': SourceVerifier(),
            'consistency': ConsistencyVerifier()
        }
    
    async def verify_claims(
        self,
        claims: List[Claim],
        context: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """验证声明列表"""
        self.status = AgentStatus.PROCESSING
        results = []
        
        try:
            for claim in claims:
                claim_result = await self._verify_single_claim(claim, context)
                results.append(claim_result)
            
            # 进行整体一致性检查
            if len(claims) > 1:
                consistency_results = await self._verifiers['consistency'].verify_multiple_claims(
                    claims, context
                )
                
                # 更新结果中的一致性信息
                for i, result in enumerate(results):
                    if i < len(consistency_results):
                        result['consistency_check'] = consistency_results[i].dict()
            
            self.status = AgentStatus.COMPLETED
            logger.info(f"完成 {len(claims)} 个声明的验证")
            return results
            
        except Exception as e:
            self.status = AgentStatus.ERROR
            logger.error(f"验证过程失败: {e}")
            return []
    
    async def _verify_single_claim(
        self,
        claim: Claim,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """验证单个声明"""
        verification_results = {}
        
        # 并行执行不同类型的验证
        verification_tasks = []
        
        for verifier_name, verifier in self._verifiers.items():
            if verifier_name != 'consistency' and verifier.can_verify(claim):
                task = self._run_verifier(verifier, claim, context)
                verification_tasks.append((verifier_name, task))
        
        # 等待所有验证任务完成
        for verifier_name, task in verification_tasks:
            try:
                result = await task
                verification_results[verifier_name] = result.dict()
            except Exception as e:
                logger.error(f"{verifier_name}验证器执行失败: {e}")
                verification_results[verifier_name] = {
                    "status": "unverified",
                    "confidence": 0.0,
                    "verification_details": f"验证器执行失败: {str(e)}"
                }
        
        # 综合分析验证结果
        final_assessment = self._combine_verification_results(verification_results)
        
        return {
            "claim": claim.dict(),
            "verifications": verification_results,
            "final_assessment": final_assessment
        }
    
    async def _run_verifier(
        self,
        verifier: BaseVerifier,
        claim: Claim,
        context: Dict[str, Any] = None
    ) -> VerificationResult:
        """运行单个验证器"""
        try:
            return await verifier.verify_claim(claim, context)
        except Exception as e:
            logger.error(f"验证器 {verifier.get_verification_type()} 执行失败: {e}")
            return VerificationResult(
                status="unverified",
                confidence=0.0,
                verification_details=f"验证执行失败: {str(e)}",
                sources=[]
            )
    
    def _combine_verification_results(
        self,
        verification_results: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """综合分析多个验证结果"""
        total_confidence = 0.0
        verified_count = 0
        contradicted_count = 0
        total_count = len(verification_results)
        
        if total_count == 0:
            return {
                "status": "unverified",
                "confidence": 0.0,
                "summary": "无法进行验证"
            }
        
        for result in verification_results.values():
            confidence = result.get("confidence", 0.0)
            status = result.get("status", "unverified")
            
            total_confidence += confidence
            
            if status == "verified":
                verified_count += 1
            elif status == "contradicted":
                contradicted_count += 1
        
        average_confidence = total_confidence / total_count
        
        # 决定最终状态
        if contradicted_count > 0:
            final_status = "contradicted"
        elif verified_count > total_count * 0.5:
            final_status = "verified"
        else:
            final_status = "unverified"
        
        return {
            "status": final_status,
            "confidence": average_confidence,
            "summary": f"验证器结果: {verified_count}个通过, {contradicted_count}个矛盾, 平均置信度: {average_confidence:.2f}",
            "verified_count": verified_count,
            "contradicted_count": contradicted_count,
            "total_count": total_count
        }
    
    def get_verifier_status(self) -> Dict[str, bool]:
        """获取各个验证器的可用状态"""
        return {
            name: verifier.is_available
            for name, verifier in self._verifiers.items()
        }