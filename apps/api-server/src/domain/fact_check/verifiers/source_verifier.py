"""来源验证器 - 验证信息来源的可靠性"""
import logging
from typing import Dict, Any, List

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from .base_verifier import BaseVerifier, VerificationResult
from ..state import Claim, VerificationStatus
from ....infrastructure.services.llm import LLMFactory

logger = logging.getLogger(__name__)


class SourceVerifier(BaseVerifier):
    """来源验证器 - 专门验证信息来源的权威性和可靠性"""
    
    def __init__(self):
        super().__init__()
        try:
            self.llm = LLMFactory.create_claude4_llm(
                temperature=0.1,
                task_type="source_verification"
            )
            self.is_available = True
        except ValueError as e:
            logger.warning(f"SourceVerifier初始化失败: {e}")
            self.llm = None
        
        self.output_parser = JsonOutputParser(pydantic_object=VerificationResult)
        
        # 权威来源列表
        self._trusted_sources = {
            "financial": [
                "SEC", "财政部", "央行", "证监会", "交易所",
                "Bloomberg", "Reuters", "Wind", "同花顺"
            ],
            "news": [
                "新华社", "人民日报", "央视", "财新", "第一财经",
                "WSJ", "FT", "CNN", "BBC"
            ],
            "corporate": [
                "公司公告", "财报", "投资者关系", "官方网站"
            ]
        }
    
    def get_verification_type(self) -> str:
        """获取验证类型"""
        return "source_verification"
    
    def can_verify(self, claim: Claim) -> bool:
        """判断是否可以验证该声明"""
        # 所有声明都需要来源验证
        return True
    
    async def verify_claim(
        self, 
        claim: Claim, 
        context: Dict[str, Any] = None
    ) -> VerificationResult:
        """验证信息来源"""
        if not self.is_available:
            return self._create_verification_result(
                VerificationStatus.UNVERIFIED,
                0.0,
                "来源验证器不可用"
            )
        
        try:
            # 先进行简单的来源匹配
            source_score = self._analyze_source_reliability(claim)
            
            # 使用LLM进行深度分析
            verification_prompt = self._get_source_verification_prompt()
            
            prompt_template = ChatPromptTemplate.from_template(verification_prompt)
            chain = prompt_template | self.llm | self.output_parser
            
            result = await chain.ainvoke({
                "claim_text": claim.text,
                "claim_context": claim.context,
                "source_score": source_score,
                "trusted_sources": self._trusted_sources,
                "format_instructions": self.output_parser.get_format_instructions()
            })
            
            # 结合简单匹配和LLM分析的结果
            final_confidence = (result.confidence + source_score) / 2
            result.confidence = final_confidence
            
            logger.info(f"来源验证完成，置信度: {final_confidence}")
            return result
            
        except Exception as e:
            logger.error(f"来源验证失败: {e}")
            return self._create_verification_result(
                VerificationStatus.UNVERIFIED,
                0.0,
                f"来源验证过程失败: {str(e)}"
            )
    
    def _analyze_source_reliability(self, claim: Claim) -> float:
        """分析来源可靠性得分"""
        text_lower = claim.text.lower() + " " + claim.context.lower()
        
        max_score = 0.0
        for category, sources in self._trusted_sources.items():
            for source in sources:
                if source.lower() in text_lower:
                    # 不同类别的权威来源有不同的基础分数
                    if category == "financial":
                        max_score = max(max_score, 0.9)
                    elif category == "corporate":
                        max_score = max(max_score, 0.8)
                    elif category == "news":
                        max_score = max(max_score, 0.7)
        
        return max_score
    
    def _get_source_verification_prompt(self) -> str:
        """获取来源验证提示词"""
        return """
你是一个专业的信息来源验证专家。请评估以下声明的信息来源可靠性。

声明内容：{claim_text}
声明上下文：{claim_context}
初始来源评分：{source_score}
权威来源参考：{trusted_sources}

验证要求：
1. 分析声明中提到或暗示的信息来源
2. 评估来源的权威性和可信度
3. 考虑来源是否为一手信息
4. 判断信息是否可通过公开渠道验证

评分标准：
- 官方机构/一手来源：高可信度
- 知名媒体/二手来源：中等可信度
- 未知来源/传言：低可信度
- 明显不可靠来源：不可信

请按以下JSON格式返回结果：
{format_instructions}
"""