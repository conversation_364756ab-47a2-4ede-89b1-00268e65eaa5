"""数据验证器 - 验证财务数据的准确性"""
import logging
from typing import Dict, Any, List
import re

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from .base_verifier import BaseVerifier, VerificationResult
from ..state import Claim, ClaimType, VerificationStatus
from ....infrastructure.services.llm import LLMFactory

logger = logging.getLogger(__name__)


class DataVerifier(BaseVerifier):
    """数据验证器 - 专门验证财务数据数值的准确性"""
    
    def __init__(self):
        super().__init__()
        try:
            self.llm = LLMFactory.create_claude4_llm(
                temperature=0.1,
                task_type="data_verification"
            )
            self.is_available = True
        except ValueError as e:
            logger.warning(f"DataVerifier初始化失败: {e}")
            self.llm = None
        
        self.output_parser = JsonOutputParser(pydantic_object=VerificationResult)
    
    def get_verification_type(self) -> str:
        """获取验证类型"""
        return "data_verification"
    
    def can_verify(self, claim: Claim) -> bool:
        """判断是否可以验证该声明"""
        if claim.type != ClaimType.FINANCIAL_DATA:
            return False
        
        # 检查是否包含数值数据
        return bool(re.search(r'\d+', claim.text))
    
    async def verify_claim(
        self, 
        claim: Claim, 
        context: Dict[str, Any] = None
    ) -> VerificationResult:
        """验证财务数据声明"""
        if not self.is_available:
            return self._create_verification_result(
                VerificationStatus.UNVERIFIED,
                0.0,
                "数据验证器不可用"
            )
        
        try:
            verification_prompt = self._get_data_verification_prompt()
            
            prompt_template = ChatPromptTemplate.from_template(verification_prompt)
            chain = prompt_template | self.llm | self.output_parser
            
            result = await chain.ainvoke({
                "claim_text": claim.text,
                "claim_context": claim.context,
                "additional_context": context or {},
                "format_instructions": self.output_parser.get_format_instructions()
            })
            
            logger.info(f"数据验证完成，状态: {result.status}")
            return result
            
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return self._create_verification_result(
                VerificationStatus.UNVERIFIED,
                0.0,
                f"数据验证过程失败: {str(e)}"
            )
    
    def _get_data_verification_prompt(self) -> str:
        """获取数据验证提示词"""
        return """
你是一个专业的财务数据验证专家。请验证以下财务数据声明的准确性。

声明内容：{claim_text}
声明上下文：{claim_context}
附加上下文：{additional_context}

验证要求：
1. 提取声明中的具体数值（收入、利润、股价、市值等）
2. 基于你的知识库判断这些数值是否合理
3. 考虑时间因素和市场环境
4. 分析数值之间的逻辑关系

注意事项：
- 如果数据明显不合理，标记为contradicted
- 如果数据在合理范围内，标记为verified  
- 如果无法判断，标记为unverified
- 提供详细的验证理由

请按以下JSON格式返回结果：
{format_instructions}
"""