"""一致性验证器 - 验证声明之间的逻辑一致性"""
import logging
from typing import Dict, Any, List

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from .base_verifier import BaseVerifier, VerificationResult
from ..state import Claim, VerificationStatus
from ....infrastructure.services.llm import LLMFactory

logger = logging.getLogger(__name__)


class ConsistencyVerifier(BaseVerifier):
    """一致性验证器 - 专门验证声明之间的逻辑一致性"""
    
    def __init__(self):
        super().__init__()
        try:
            self.llm = LLMFactory.create_claude4_llm(
                temperature=0.1,
                task_type="consistency_verification"
            )
            self.is_available = True
        except ValueError as e:
            logger.warning(f"ConsistencyVerifier初始化失败: {e}")
            self.llm = None
        
        self.output_parser = JsonOutputParser(pydantic_object=VerificationResult)
    
    def get_verification_type(self) -> str:
        """获取验证类型"""
        return "consistency_verification"
    
    def can_verify(self, claim: Claim) -> bool:
        """判断是否可以验证该声明"""
        # 一致性验证需要多个声明才有意义
        return True
    
    async def verify_claim(
        self, 
        claim: Claim, 
        context: Dict[str, Any] = None
    ) -> VerificationResult:
        """验证声明的一致性"""
        if not self.is_available:
            return self._create_verification_result(
                VerificationStatus.UNVERIFIED,
                0.0,
                "一致性验证器不可用"
            )
        
        try:
            # 获取相关的其他声明
            related_claims = context.get("related_claims", []) if context else []
            
            if not related_claims:
                return self._create_verification_result(
                    VerificationStatus.VERIFIED,
                    0.7,
                    "无相关声明进行一致性检查，单独声明逻辑合理"
                )
            
            verification_prompt = self._get_consistency_verification_prompt()
            
            prompt_template = ChatPromptTemplate.from_template(verification_prompt)
            chain = prompt_template | self.llm | self.output_parser
            
            result = await chain.ainvoke({
                "target_claim": claim.text,
                "target_context": claim.context,
                "related_claims": [c.text for c in related_claims],
                "format_instructions": self.output_parser.get_format_instructions()
            })
            
            logger.info(f"一致性验证完成，状态: {result.status}")
            return result
            
        except Exception as e:
            logger.error(f"一致性验证失败: {e}")
            return self._create_verification_result(
                VerificationStatus.UNVERIFIED,
                0.0,
                f"一致性验证过程失败: {str(e)}"
            )
    
    async def verify_multiple_claims(
        self,
        claims: List[Claim],
        context: Dict[str, Any] = None
    ) -> List[VerificationResult]:
        """批量验证多个声明的一致性"""
        if not self.is_available or len(claims) < 2:
            return [
                self._create_verification_result(
                    VerificationStatus.UNVERIFIED,
                    0.0,
                    "一致性验证器不可用或声明数量不足"
                )
                for _ in claims
            ]
        
        try:
            verification_prompt = self._get_batch_consistency_prompt()
            
            prompt_template = ChatPromptTemplate.from_template(verification_prompt)
            chain = prompt_template | self.llm
            
            claims_text = [f"{i+1}. {claim.text}" for i, claim in enumerate(claims)]
            
            result = await chain.ainvoke({
                "claims_list": "\n".join(claims_text),
                "context": context or {}
            })
            
            # 解析批量验证结果
            return self._parse_batch_result(result, claims)
            
        except Exception as e:
            logger.error(f"批量一致性验证失败: {e}")
            return [
                self._create_verification_result(
                    VerificationStatus.UNVERIFIED,
                    0.0,
                    f"批量一致性验证失败: {str(e)}"
                )
                for _ in claims
            ]
    
    def _parse_batch_result(self, result: str, claims: List[Claim]) -> List[VerificationResult]:
        """解析批量验证结果"""
        # 简化实现，实际应该解析LLM的结构化输出
        results = []
        base_confidence = 0.8 if "一致" in result else 0.3
        
        for claim in claims:
            results.append(
                self._create_verification_result(
                    VerificationStatus.VERIFIED if base_confidence > 0.5 else VerificationStatus.CONTRADICTED,
                    base_confidence,
                    f"批量一致性验证结果: {result[:100]}..."
                )
            )
        
        return results
    
    def _get_consistency_verification_prompt(self) -> str:
        """获取一致性验证提示词"""
        return """
你是一个专业的逻辑一致性验证专家。请分析以下声明与相关声明之间的逻辑一致性。

目标声明：{target_claim}
目标上下文：{target_context}
相关声明：{related_claims}

验证要求：
1. 分析目标声明与相关声明是否存在逻辑矛盾
2. 检查数值、时间、因果关系的一致性
3. 识别可能的冲突或支持关系
4. 评估整体逻辑的合理性

判断标准：
- verified: 声明间逻辑一致，相互支持
- contradicted: 存在明显逻辑矛盾
- unverified: 逻辑关系不明确或信息不足

请按以下JSON格式返回结果：
{format_instructions}
"""
    
    def _get_batch_consistency_prompt(self) -> str:
        """获取批量一致性验证提示词"""
        return """
你是一个专业的逻辑一致性验证专家。请分析以下声明列表的整体逻辑一致性。

声明列表：
{claims_list}

上下文信息：{context}

请分析：
1. 声明之间是否存在逻辑矛盾
2. 数值、时间、因果关系是否一致
3. 整体信息是否符合逻辑
4. 识别可能存在问题的声明

请提供详细的一致性分析报告。
"""