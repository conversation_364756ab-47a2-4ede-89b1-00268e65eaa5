"""基础验证器"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

from ..state import Claim, VerificationStatus, DataSource


class VerificationResult(BaseModel):
    """验证结果模型"""
    status: str = Field(description="验证状态: verified, contradicted, unverified")
    confidence: float = Field(description="验证置信度 (0-1)", ge=0, le=1)
    official_value: Optional[str] = Field(description="官方数据值")
    claimed_value: Optional[str] = Field(description="声明中的值")
    variance_explanation: Optional[str] = Field(description="差异说明")
    sources: List[str] = Field(description="数据来源")
    verification_details: str = Field(description="详细验证说明")


class BaseVerifier(ABC):
    """基础验证器抽象类"""
    
    def __init__(self):
        self.is_available = False
    
    @abstractmethod
    async def verify_claim(
        self, 
        claim: Claim, 
        context: Dict[str, Any] = None
    ) -> VerificationResult:
        """验证声明"""
        pass
    
    @abstractmethod
    def get_verification_type(self) -> str:
        """获取验证类型"""
        pass
    
    @abstractmethod
    def can_verify(self, claim: Claim) -> bool:
        """判断是否可以验证该声明"""
        pass
    
    def _create_verification_result(
        self,
        status: VerificationStatus,
        confidence: float,
        details: str,
        sources: List[str] = None,
        official_value: str = None,
        claimed_value: str = None,
        variance_explanation: str = None
    ) -> VerificationResult:
        """创建验证结果"""
        return VerificationResult(
            status=status.value,
            confidence=confidence,
            official_value=official_value,
            claimed_value=claimed_value,
            variance_explanation=variance_explanation,
            sources=sources or [],
            verification_details=details
        )