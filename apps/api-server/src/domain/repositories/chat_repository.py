"""聊天会话仓储接口"""
from abc import ABC, abstractmethod
from typing import List, Optional
from datetime import datetime

from ..entities.chat_session import ChatSession, Message


class ChatSessionRepository(ABC):
    """聊天会话仓储接口"""
    
    @abstractmethod
    async def save(self, session: ChatSession) -> ChatSession:
        """保存聊天会话"""
        pass
    
    @abstractmethod
    async def get_by_id(self, session_id: str) -> Optional[ChatSession]:
        """根据ID获取聊天会话"""
        pass
    
    @abstractmethod
    async def get_by_user(self, user_id: str, limit: int = 50) -> List[ChatSession]:
        """获取用户的聊天会话列表"""
        pass
    
    @abstractmethod
    async def delete(self, session_id: str) -> bool:
        """删除聊天会话"""
        pass
    
    @abstractmethod
    async def update(self, session: ChatSession) -> ChatSession:
        """更新聊天会话"""
        pass
    
    @abstractmethod
    async def find_by_title(self, title: str, user_id: Optional[str] = None) -> List[ChatSession]:
        """根据标题搜索聊天会话"""
        pass
    
    @abstractmethod
    async def get_recent_sessions(self, user_id: str, days: int = 7) -> List[ChatSession]:
        """获取最近的聊天会话"""
        pass
    
    @abstractmethod
    async def archive_old_sessions(self, user_id: str, days: int = 30) -> int:
        """归档旧的聊天会话，返回归档数量"""
        pass


class MessageRepository(ABC):
    """消息仓储接口"""
    
    @abstractmethod
    async def save(self, message: Message, session_id: str) -> Message:
        """保存消息"""
        pass
    
    @abstractmethod
    async def get_by_session(self, session_id: str, limit: Optional[int] = None) -> List[Message]:
        """获取会话的消息列表"""
        pass
    
    @abstractmethod
    async def get_by_id(self, message_id: str) -> Optional[Message]:
        """根据ID获取消息"""
        pass
    
    @abstractmethod
    async def delete(self, message_id: str) -> bool:
        """删除消息"""
        pass
    
    @abstractmethod
    async def search_content(self, session_id: str, query: str) -> List[Message]:
        """在会话中搜索消息内容"""
        pass
    
    @abstractmethod
    async def get_context_messages(self, session_id: str, max_tokens: int = 4000) -> List[Message]:
        """获取适合作为上下文的消息列表"""
        pass