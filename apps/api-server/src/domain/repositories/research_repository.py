"""研究任务仓储接口"""
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..entities.research_task import ResearchTask, ResearchPipeline
from ..value_objects.task_status import TaskStatus, ResearchTaskType


class ResearchTaskRepository(ABC):
    """研究任务仓储接口"""
    
    @abstractmethod
    async def save(self, task: ResearchTask) -> ResearchTask:
        """保存研究任务"""
        pass
    
    @abstractmethod
    async def get_by_id(self, task_id: str) -> Optional[ResearchTask]:
        """根据ID获取研究任务"""
        pass
    
    @abstractmethod
    async def get_by_pipeline(self, pipeline_id: str) -> List[ResearchTask]:
        """获取流水线的所有任务"""
        pass
    
    @abstractmethod
    async def get_by_status(self, status: TaskStatus, limit: int = 100) -> List[ResearchTask]:
        """根据状态获取任务列表"""
        pass
    
    @abstractmethod
    async def get_by_type(self, task_type: ResearchTaskType, limit: int = 100) -> List[ResearchTask]:
        """根据类型获取任务列表"""
        pass
    
    @abstractmethod
    async def update(self, task: ResearchTask) -> ResearchTask:
        """更新研究任务"""
        pass
    
    @abstractmethod
    async def delete(self, task_id: str) -> bool:
        """删除研究任务"""
        pass
    
    @abstractmethod
    async def find_pending_tasks(self, user_id: Optional[str] = None) -> List[ResearchTask]:
        """查找待执行的任务"""
        pass
    
    @abstractmethod
    async def find_running_tasks(self, user_id: Optional[str] = None) -> List[ResearchTask]:
        """查找正在运行的任务"""
        pass
    
    @abstractmethod
    async def get_user_tasks(self, user_id: str, limit: int = 50) -> List[ResearchTask]:
        """获取用户的任务列表"""
        pass


class ResearchPipelineRepository(ABC):
    """研究流水线仓储接口"""
    
    @abstractmethod
    async def save(self, pipeline: ResearchPipeline) -> ResearchPipeline:
        """保存研究流水线"""
        pass
    
    @abstractmethod
    async def get_by_id(self, pipeline_id: str) -> Optional[ResearchPipeline]:
        """根据ID获取研究流水线"""
        pass
    
    @abstractmethod
    async def get_by_thread(self, thread_id: str) -> Optional[ResearchPipeline]:
        """根据线程ID获取研究流水线"""
        pass
    
    @abstractmethod
    async def get_by_user(self, user_id: str, limit: int = 50) -> List[ResearchPipeline]:
        """获取用户的流水线列表"""
        pass
    
    @abstractmethod
    async def update(self, pipeline: ResearchPipeline) -> ResearchPipeline:
        """更新研究流水线"""
        pass
    
    @abstractmethod
    async def delete(self, pipeline_id: str) -> bool:
        """删除研究流水线"""
        pass
    
    @abstractmethod
    async def find_active_pipelines(self, user_id: Optional[str] = None) -> List[ResearchPipeline]:
        """查找活跃的流水线"""
        pass
    
    @abstractmethod
    async def get_pipeline_with_tasks(self, pipeline_id: str) -> Optional[ResearchPipeline]:
        """获取包含所有任务的完整流水线"""
        pass


class ResearchResultRepository(ABC):
    """研究结果仓储接口"""
    
    @abstractmethod
    async def save_result(self, task_id: str, result: Dict[str, Any]) -> bool:
        """保存研究结果"""
        pass
    
    @abstractmethod
    async def get_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取研究结果"""
        pass
    
    @abstractmethod
    async def get_pipeline_results(self, pipeline_id: str) -> Dict[str, Dict[str, Any]]:
        """获取流水线的所有结果"""
        pass
    
    @abstractmethod
    async def delete_result(self, task_id: str) -> bool:
        """删除研究结果"""
        pass
    
    @abstractmethod
    async def search_results(self, query: str, limit: int = 50) -> List[Dict[str, Any]]:
        """搜索研究结果"""
        pass