"""聊天会话领域实体"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from dataclasses import dataclass, field
from uuid import uuid4
from enum import Enum


class MessageRole(str, Enum):
    """消息角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class SessionStatus(str, Enum):
    """会话状态枚举"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"


@dataclass
class Message:
    """聊天消息值对象"""
    
    id: str = field(default_factory=lambda: str(uuid4()))
    role: MessageRole = MessageRole.USER
    content: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    
    # 消息特定属性
    token_count: Optional[int] = None
    processing_time: Optional[float] = None
    
    def __str__(self) -> str:
        preview = self.content[:50] + "..." if len(self.content) > 50 else self.content
        return f"Message(role={self.role.value}, content='{preview}')"


@dataclass
class ChatSession:
    """聊天会话实体"""
    
    # 标识符
    id: str = field(default_factory=lambda: str(uuid4()))
    
    # 基本属性
    title: str = ""
    status: SessionStatus = SessionStatus.ACTIVE
    
    # 消息历史
    messages: List[Message] = field(default_factory=list)
    
    # 时间戳
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    last_activity_at: datetime = field(default_factory=datetime.now)
    
    # 业务属性
    user_id: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)
    
    # 统计信息
    total_tokens: int = 0
    total_cost: float = 0.0
    
    def add_message(self, role: MessageRole, content: str, metadata: Optional[Dict[str, Any]] = None) -> Message:
        """添加消息"""
        message = Message(
            role=role,
            content=content,
            metadata=metadata or {}
        )
        
        self.messages.append(message)
        self.last_activity_at = datetime.now()
        self.updated_at = datetime.now()
        
        # 更新统计信息
        if message.token_count:
            self.total_tokens += message.token_count
        
        # 自动生成标题（基于首条用户消息）
        if not self.title and role == MessageRole.USER and len(self.messages) == 1:
            self.title = self._generate_title(content)
        
        return message
    
    def get_messages(self, limit: Optional[int] = None, role_filter: Optional[MessageRole] = None) -> List[Message]:
        """获取消息列表"""
        messages = self.messages
        
        if role_filter:
            messages = [msg for msg in messages if msg.role == role_filter]
        
        if limit:
            messages = messages[-limit:]
        
        return messages
    
    def get_context_messages(self, max_tokens: int = 4000) -> List[Message]:
        """获取适合作为上下文的消息列表（基于token限制）"""
        context_messages = []
        current_tokens = 0
        
        # 从最新消息开始倒序添加
        for message in reversed(self.messages):
            message_tokens = message.token_count or len(message.content) // 4  # 粗略估算
            
            if current_tokens + message_tokens > max_tokens and context_messages:
                break
            
            context_messages.insert(0, message)
            current_tokens += message_tokens
        
        return context_messages
    
    def pause(self) -> None:
        """暂停会话"""
        if self.status != SessionStatus.ACTIVE:
            raise ValueError(f"Cannot pause session in status: {self.status}")
        
        self.status = SessionStatus.PAUSED
        self.updated_at = datetime.now()
    
    def resume(self) -> None:
        """恢复会话"""
        if self.status != SessionStatus.PAUSED:
            raise ValueError(f"Cannot resume session in status: {self.status}")
        
        self.status = SessionStatus.ACTIVE
        self.updated_at = datetime.now()
        self.last_activity_at = datetime.now()
    
    def complete(self) -> None:
        """完成会话"""
        if self.status in [SessionStatus.COMPLETED, SessionStatus.ARCHIVED]:
            raise ValueError(f"Session is already {self.status.value}")
        
        self.status = SessionStatus.COMPLETED
        self.updated_at = datetime.now()
    
    def archive(self) -> None:
        """归档会话"""
        self.status = SessionStatus.ARCHIVED
        self.updated_at = datetime.now()
    
    def update_context(self, new_context: Dict[str, Any]) -> None:
        """更新会话上下文"""
        self.context.update(new_context)
        self.updated_at = datetime.now()
    
    def add_cost(self, cost: float) -> None:
        """添加成本"""
        self.total_cost += cost
        self.updated_at = datetime.now()
    
    @property
    def message_count(self) -> int:
        """消息数量"""
        return len(self.messages)
    
    @property
    def user_message_count(self) -> int:
        """用户消息数量"""
        return len([msg for msg in self.messages if msg.role == MessageRole.USER])
    
    @property
    def assistant_message_count(self) -> int:
        """助手消息数量"""
        return len([msg for msg in self.messages if msg.role == MessageRole.ASSISTANT])
    
    @property
    def is_active(self) -> bool:
        """判断会话是否活跃"""
        return self.status == SessionStatus.ACTIVE
    
    @property
    def last_user_message(self) -> Optional[Message]:
        """获取最后一条用户消息"""
        for message in reversed(self.messages):
            if message.role == MessageRole.USER:
                return message
        return None
    
    @property
    def last_assistant_message(self) -> Optional[Message]:
        """获取最后一条助手消息"""
        for message in reversed(self.messages):
            if message.role == MessageRole.ASSISTANT:
                return message
        return None
    
    @property
    def duration_minutes(self) -> float:
        """会话持续时间（分钟）"""
        return (self.last_activity_at - self.created_at).total_seconds() / 60
    
    def _generate_title(self, first_message: str) -> str:
        """基于首条消息生成标题"""
        # 简单的标题生成逻辑
        words = first_message.split()[:8]  # 取前8个词
        title = " ".join(words)
        if len(first_message) > len(title):
            title += "..."
        return title
    
    def __str__(self) -> str:
        return f"ChatSession(id={self.id}, title='{self.title}', messages={self.message_count})"
    
    def __repr__(self) -> str:
        return self.__str__()