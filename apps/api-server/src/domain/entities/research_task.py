"""投资研究任务领域实体"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field
from uuid import uuid4

from ..value_objects.task_status import TaskStatus, ResearchTaskType


@dataclass
class ResearchTask:
    """投资研究任务实体"""
    
    # 标识符
    id: str = field(default_factory=lambda: str(uuid4()))
    
    # 基本属性
    title: str = ""
    description: str = ""
    status: TaskStatus = TaskStatus.PENDING
    task_type: ResearchTaskType = ResearchTaskType.TRUTH_VERIFICATION
    order: int = 0
    
    # 时间戳
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 业务属性
    context: Dict[str, Any] = field(default_factory=dict)
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    
    # 关联关系
    thread_id: Optional[str] = None
    user_id: Optional[str] = None
    
    def start(self) -> None:
        """开始任务"""
        if self.status != TaskStatus.PENDING:
            raise ValueError(f"Cannot start task in status: {self.status}")
        
        self.status = TaskStatus.IN_PROGRESS
        self.started_at = datetime.now()
        self.updated_at = datetime.now()
    
    def complete(self, result: Dict[str, Any]) -> None:
        """完成任务"""
        if self.status != TaskStatus.IN_PROGRESS:
            raise ValueError(f"Cannot complete task in status: {self.status}")
        
        self.status = TaskStatus.COMPLETED
        self.result = result
        self.completed_at = datetime.now()
        self.updated_at = datetime.now()
    
    def fail(self, error_message: str) -> None:
        """任务失败"""
        if self.status not in [TaskStatus.PENDING, TaskStatus.IN_PROGRESS]:
            raise ValueError(f"Cannot fail task in status: {self.status}")
        
        self.status = TaskStatus.FAILED
        self.error_message = error_message
        self.updated_at = datetime.now()
    
    def reset(self) -> None:
        """重置任务"""
        self.status = TaskStatus.PENDING
        self.started_at = None
        self.completed_at = None
        self.result = None
        self.error_message = None
        self.updated_at = datetime.now()
    
    @property
    def is_completed(self) -> bool:
        """判断任务是否已完成"""
        return self.status == TaskStatus.COMPLETED
    
    @property
    def is_running(self) -> bool:
        """判断任务是否正在运行"""
        return self.status == TaskStatus.IN_PROGRESS
    
    @property
    def is_failed(self) -> bool:
        """判断任务是否失败"""
        return self.status == TaskStatus.FAILED
    
    @property
    def duration(self) -> Optional[float]:
        """获取任务执行时长（秒）"""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.now()
        return (end_time - self.started_at).total_seconds()
    
    def update_context(self, new_context: Dict[str, Any]) -> None:
        """更新任务上下文"""
        self.context.update(new_context)
        self.updated_at = datetime.now()
    
    def __str__(self) -> str:
        return f"ResearchTask(id={self.id}, title='{self.title}', status={self.status.value})"
    
    def __repr__(self) -> str:
        return self.__str__()


@dataclass
class ResearchPipeline:
    """投资研究流水线聚合根"""
    
    # 标识符
    id: str = field(default_factory=lambda: str(uuid4()))
    
    # 基本属性
    name: str = ""
    description: str = ""
    
    # 任务列表
    tasks: List[ResearchTask] = field(default_factory=list)
    
    # 时间戳
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 业务属性
    thread_id: Optional[str] = None
    user_id: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)
    
    def add_task(self, task: ResearchTask) -> None:
        """添加任务"""
        task.order = len(self.tasks)
        task.thread_id = self.thread_id
        task.user_id = self.user_id
        self.tasks.append(task)
        self.updated_at = datetime.now()
    
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        for i, task in enumerate(self.tasks):
            if task.id == task_id:
                del self.tasks[i]
                # 重新排序后续任务
                for j in range(i, len(self.tasks)):
                    self.tasks[j].order = j
                self.updated_at = datetime.now()
                return True
        return False
    
    def get_task(self, task_id: str) -> Optional[ResearchTask]:
        """获取任务"""
        for task in self.tasks:
            if task.id == task_id:
                return task
        return None
    
    def get_next_pending_task(self) -> Optional[ResearchTask]:
        """获取下一个待执行的任务"""
        for task in sorted(self.tasks, key=lambda t: t.order):
            if task.status == TaskStatus.PENDING:
                return task
        return None
    
    @property
    def total_tasks(self) -> int:
        """总任务数"""
        return len(self.tasks)
    
    @property
    def completed_tasks(self) -> int:
        """已完成任务数"""
        return len([t for t in self.tasks if t.is_completed])
    
    @property
    def failed_tasks(self) -> int:
        """失败任务数"""
        return len([t for t in self.tasks if t.is_failed])
    
    @property
    def progress(self) -> float:
        """进度百分比"""
        if self.total_tasks == 0:
            return 0.0
        return (self.completed_tasks / self.total_tasks) * 100
    
    @property
    def is_completed(self) -> bool:
        """判断流水线是否已完成"""
        return self.total_tasks > 0 and self.completed_tasks == self.total_tasks
    
    @property
    def has_failed_tasks(self) -> bool:
        """判断是否有失败的任务"""
        return self.failed_tasks > 0
    
    def __str__(self) -> str:
        return f"ResearchPipeline(id={self.id}, tasks={self.total_tasks}, progress={self.progress:.1f}%)"