"""投资研究领域服务 - 纯业务逻辑"""
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..entities.research_task import ResearchTask, ResearchPipeline
from ..value_objects.task_status import TaskStatus, ResearchTaskType
from ..repositories.research_repository import (
    ResearchTaskRepository, 
    ResearchPipelineRepository
)
from ...core.exceptions import (
    DomainException,
    BusinessRuleViolationError,
    InvalidOperationError
)


class ResearchDomainService:
    """投资研究领域服务"""
    
    def __init__(
        self,
        task_repository: ResearchTaskRepository,
        pipeline_repository: ResearchPipelineRepository
    ):
        self._task_repository = task_repository
        self._pipeline_repository = pipeline_repository
    
    async def create_research_pipeline(
        self,
        name: str,
        description: str,
        user_id: str,
        thread_id: Optional[str] = None
    ) -> ResearchPipeline:
        """创建研究流水线"""
        # 业务规则：用户不能有太多活跃的流水线
        active_pipelines = await self._pipeline_repository.find_active_pipelines(user_id)
        if len(active_pipelines) >= 5:
            raise BusinessRuleViolationError(
                "用户活跃流水线数量不能超过5个",
                {"user_id": user_id, "active_count": len(active_pipelines)}
            )
        
        pipeline = ResearchPipeline(
            name=name,
            description=description,
            user_id=user_id,
            thread_id=thread_id
        )
        
        return await self._pipeline_repository.save(pipeline)
    
    async def add_task_to_pipeline(
        self,
        pipeline_id: str,
        task_type: ResearchTaskType,
        title: str,
        description: str,
        context: Optional[Dict[str, Any]] = None
    ) -> ResearchTask:
        """向流水线添加任务"""
        pipeline = await self._pipeline_repository.get_by_id(pipeline_id)
        if not pipeline:
            raise InvalidOperationError(
                "add_task_to_pipeline",
                f"Pipeline {pipeline_id} not found"
            )
        
        # 业务规则：流水线任务数量限制
        if len(pipeline.tasks) >= 10:
            raise BusinessRuleViolationError(
                "单个流水线任务数量不能超过10个",
                {"pipeline_id": pipeline_id, "current_count": len(pipeline.tasks)}
            )
        
        task = ResearchTask(
            title=title,
            description=description,
            task_type=task_type,
            context=context or {},
            thread_id=pipeline.thread_id,
            user_id=pipeline.user_id
        )
        
        pipeline.add_task(task)
        await self._pipeline_repository.update(pipeline)
        
        return task
    
    async def start_next_task(self, pipeline_id: str) -> Optional[ResearchTask]:
        """开始流水线中的下一个任务"""
        pipeline = await self._pipeline_repository.get_pipeline_with_tasks(pipeline_id)
        if not pipeline:
            raise InvalidOperationError(
                "start_next_task",
                f"Pipeline {pipeline_id} not found"
            )
        
        next_task = pipeline.get_next_pending_task()
        if not next_task:
            return None
        
        # 业务规则：同时只能有一个任务在运行
        running_tasks = [t for t in pipeline.tasks if t.is_running]
        if running_tasks:
            raise BusinessRuleViolationError(
                "流水线中已有任务正在运行",
                {
                    "pipeline_id": pipeline_id,
                    "running_task_id": running_tasks[0].id
                }
            )
        
        next_task.start()
        await self._task_repository.update(next_task)
        
        return next_task
    
    async def complete_task(
        self,
        task_id: str,
        result: Dict[str, Any]
    ) -> ResearchTask:
        """完成任务"""
        task = await self._task_repository.get_by_id(task_id)
        if not task:
            raise InvalidOperationError(
                "complete_task",
                f"Task {task_id} not found"
            )
        
        if not task.status.can_complete:
            raise InvalidOperationError(
                "complete_task",
                f"Task {task_id} cannot be completed in status {task.status}"
            )
        
        task.complete(result)
        return await self._task_repository.update(task)
    
    async def fail_task(
        self,
        task_id: str,
        error_message: str
    ) -> ResearchTask:
        """标记任务失败"""
        task = await self._task_repository.get_by_id(task_id)
        if not task:
            raise InvalidOperationError(
                "fail_task",
                f"Task {task_id} not found"
            )
        
        if not task.status.can_fail:
            raise InvalidOperationError(
                "fail_task",
                f"Task {task_id} cannot be failed in status {task.status}"
            )
        
        task.fail(error_message)
        return await self._task_repository.update(task)
    
    async def retry_failed_task(self, task_id: str) -> ResearchTask:
        """重试失败的任务"""
        task = await self._task_repository.get_by_id(task_id)
        if not task:
            raise InvalidOperationError(
                "retry_failed_task",
                f"Task {task_id} not found"
            )
        
        if not task.is_failed:
            raise InvalidOperationError(
                "retry_failed_task",
                f"Task {task_id} is not in failed status"
            )
        
        task.reset()
        return await self._task_repository.update(task)
    
    async def get_pipeline_progress(self, pipeline_id: str) -> Dict[str, Any]:
        """获取流水线进度信息"""
        pipeline = await self._pipeline_repository.get_pipeline_with_tasks(pipeline_id)
        if not pipeline:
            raise InvalidOperationError(
                "get_pipeline_progress",
                f"Pipeline {pipeline_id} not found"
            )
        
        return {
            "pipeline_id": pipeline.id,
            "name": pipeline.name,
            "total_tasks": pipeline.total_tasks,
            "completed_tasks": pipeline.completed_tasks,
            "failed_tasks": pipeline.failed_tasks,
            "progress_percentage": pipeline.progress,
            "is_completed": pipeline.is_completed,
            "has_failed_tasks": pipeline.has_failed_tasks,
            "created_at": pipeline.created_at,
            "updated_at": pipeline.updated_at
        }
    
    async def validate_task_transition(
        self,
        task: ResearchTask,
        target_status: TaskStatus
    ) -> bool:
        """验证任务状态转换是否合法"""
        current_status = task.status
        
        # 定义合法的状态转换
        valid_transitions = {
            TaskStatus.PENDING: [TaskStatus.IN_PROGRESS, TaskStatus.FAILED],
            TaskStatus.IN_PROGRESS: [TaskStatus.COMPLETED, TaskStatus.FAILED],
            TaskStatus.COMPLETED: [],  # 完成状态不能转换到其他状态
            TaskStatus.FAILED: [TaskStatus.PENDING]  # 失败状态只能重置为待执行
        }
        
        return target_status in valid_transitions.get(current_status, [])
    
    async def calculate_pipeline_metrics(self, pipeline_id: str) -> Dict[str, Any]:
        """计算流水线指标"""
        pipeline = await self._pipeline_repository.get_pipeline_with_tasks(pipeline_id)
        if not pipeline:
            raise InvalidOperationError(
                "calculate_pipeline_metrics",
                f"Pipeline {pipeline_id} not found"
            )
        
        # 计算各种指标
        total_duration = 0
        completed_tasks = [t for t in pipeline.tasks if t.is_completed]
        
        for task in completed_tasks:
            if task.duration:
                total_duration += task.duration
        
        avg_duration = total_duration / len(completed_tasks) if completed_tasks else 0
        
        task_type_distribution = {}
        for task in pipeline.tasks:
            task_type = task.task_type.value
            task_type_distribution[task_type] = task_type_distribution.get(task_type, 0) + 1
        
        return {
            "total_duration_seconds": total_duration,
            "average_task_duration_seconds": avg_duration,
            "success_rate": (pipeline.completed_tasks / pipeline.total_tasks * 100) if pipeline.total_tasks > 0 else 0,
            "task_type_distribution": task_type_distribution,
            "completion_time": completed_tasks[-1].completed_at if completed_tasks else None
        }