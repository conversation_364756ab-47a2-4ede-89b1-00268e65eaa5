"""聊天领域服务 - 纯业务逻辑"""
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from ..entities.chat_session import ChatSession, Message, MessageRole, SessionStatus
from ..repositories.chat_repository import ChatSessionRepository, MessageRepository
from ...core.exceptions import (
    DomainException,
    BusinessRuleViolationError,
    InvalidOperationError
)


class ChatDomainService:
    """聊天领域服务"""
    
    def __init__(
        self,
        session_repository: ChatSessionRepository,
        message_repository: MessageRepository
    ):
        self._session_repository = session_repository
        self._message_repository = message_repository
    
    async def create_chat_session(
        self,
        user_id: Optional[str] = None,
        initial_message: Optional[str] = None
    ) -> ChatSession:
        """创建聊天会话"""
        # 业务规则：用户活跃会话数量限制
        if user_id:
            active_sessions = await self._get_active_sessions_count(user_id)
            if active_sessions >= 10:
                raise BusinessRuleViolationError(
                    "用户活跃会话数量不能超过10个",
                    {"user_id": user_id, "active_count": active_sessions}
                )
        
        session = ChatSession(user_id=user_id)
        
        # 如果有初始消息，添加到会话中
        if initial_message:
            session.add_message(MessageRole.USER, initial_message)
        
        return await self._session_repository.save(session)
    
    async def add_user_message(
        self,
        session_id: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Message:
        """添加用户消息"""
        session = await self._session_repository.get_by_id(session_id)
        if not session:
            raise InvalidOperationError(
                "add_user_message",
                f"Session {session_id} not found"
            )
        
        if not session.is_active:
            raise InvalidOperationError(
                "add_user_message",
                f"Session {session_id} is not active"
            )
        
        # 业务规则：消息长度限制
        if len(content) > 10000:
            raise BusinessRuleViolationError(
                "消息内容长度不能超过10000字符",
                {"session_id": session_id, "content_length": len(content)}
            )
        
        message = session.add_message(MessageRole.USER, content, metadata)
        await self._session_repository.update(session)
        
        return message
    
    async def add_assistant_message(
        self,
        session_id: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Message:
        """添加助手消息"""
        session = await self._session_repository.get_by_id(session_id)
        if not session:
            raise InvalidOperationError(
                "add_assistant_message",
                f"Session {session_id} not found"
            )
        
        message = session.add_message(MessageRole.ASSISTANT, content, metadata)
        await self._session_repository.update(session)
        
        return message
    
    async def pause_session(self, session_id: str) -> ChatSession:
        """暂停会话"""
        session = await self._session_repository.get_by_id(session_id)
        if not session:
            raise InvalidOperationError(
                "pause_session",
                f"Session {session_id} not found"
            )
        
        session.pause()
        return await self._session_repository.update(session)
    
    async def resume_session(self, session_id: str) -> ChatSession:
        """恢复会话"""
        session = await self._session_repository.get_by_id(session_id)
        if not session:
            raise InvalidOperationError(
                "resume_session",
                f"Session {session_id} not found"
            )
        
        session.resume()
        return await self._session_repository.update(session)
    
    async def complete_session(self, session_id: str) -> ChatSession:
        """完成会话"""
        session = await self._session_repository.get_by_id(session_id)
        if not session:
            raise InvalidOperationError(
                "complete_session",
                f"Session {session_id} not found"
            )
        
        session.complete()
        return await self._session_repository.update(session)
    
    async def get_conversation_context(
        self,
        session_id: str,
        max_tokens: int = 4000
    ) -> List[Message]:
        """获取会话上下文"""
        session = await self._session_repository.get_by_id(session_id)
        if not session:
            raise InvalidOperationError(
                "get_conversation_context",
                f"Session {session_id} not found"
            )
        
        return session.get_context_messages(max_tokens)
    
    async def calculate_session_metrics(self, session_id: str) -> Dict[str, Any]:
        """计算会话指标"""
        session = await self._session_repository.get_by_id(session_id)
        if not session:
            raise InvalidOperationError(
                "calculate_session_metrics",
                f"Session {session_id} not found"
            )
        
        return {
            "session_id": session.id,
            "message_count": session.message_count,
            "user_message_count": session.user_message_count,
            "assistant_message_count": session.assistant_message_count,
            "total_tokens": session.total_tokens,
            "total_cost": session.total_cost,
            "duration_minutes": session.duration_minutes,
            "status": session.status.value,
            "created_at": session.created_at,
            "last_activity_at": session.last_activity_at
        }
    
    async def validate_message_sequence(
        self,
        session_id: str,
        new_message_role: MessageRole
    ) -> bool:
        """验证消息序列是否合理"""
        session = await self._session_repository.get_by_id(session_id)
        if not session or not session.messages:
            return True  # 空会话或新会话，任何消息都可以
        
        last_message = session.messages[-1]
        
        # 业务规则：不能连续发送相同角色的消息（除了系统消息）
        if (last_message.role == new_message_role and 
            new_message_role != MessageRole.SYSTEM):
            return False
        
        return True
    
    async def auto_archive_old_sessions(
        self,
        user_id: str,
        days_threshold: int = 30
    ) -> int:
        """自动归档旧会话"""
        threshold_date = datetime.now() - timedelta(days=days_threshold)
        old_sessions = await self._session_repository.get_by_user(user_id)
        
        archived_count = 0
        for session in old_sessions:
            if (session.last_activity_at < threshold_date and 
                session.status == SessionStatus.ACTIVE):
                session.archive()
                await self._session_repository.update(session)
                archived_count += 1
        
        return archived_count
    
    async def _get_active_sessions_count(self, user_id: str) -> int:
        """获取用户活跃会话数量"""
        sessions = await self._session_repository.get_by_user(user_id)
        return len([s for s in sessions if s.is_active])
    
    async def validate_session_health(self, session_id: str) -> Dict[str, Any]:
        """验证会话健康状态"""
        session = await self._session_repository.get_by_id(session_id)
        if not session:
            return {"healthy": False, "reason": "Session not found"}
        
        issues = []
        
        # 检查消息序列
        prev_role = None
        consecutive_count = 0
        for message in session.messages:
            if message.role == prev_role and message.role != MessageRole.SYSTEM:
                consecutive_count += 1
                if consecutive_count > 2:
                    issues.append(f"Too many consecutive {message.role.value} messages")
                    break
            else:
                consecutive_count = 1
                prev_role = message.role
        
        # 检查会话活跃度
        if session.duration_minutes > 1440:  # 24小时
            issues.append("Session duration too long")
        
        # 检查消息数量
        if session.message_count > 1000:
            issues.append("Too many messages in session")
        
        return {
            "healthy": len(issues) == 0,
            "issues": issues,
            "metrics": await self.calculate_session_metrics(session_id)
        }