# 环境配置
ENVIRONMENT=development

# 应用配置
APP_NAME=investor-insight
LOG_LEVEL=DEBUG

# SLS 日志配置 - yai-loguru-sinks
SLS_ENABLED=true
SLS_ENDPOINT=cn-beijing.log.aliyuncs.com
SLS_ACCESS_KEY_ID=LTAI5tPrkKMrPLW1XjbBxwhm
SLS_ACCESS_KEY_SECRET=******************************
SLS_PROJECT=yai-log-test
SLS_LOGSTORE=app-log
SLS_REGION=cn-beijing
SLS_TOPIC=default

# LLM配置
OPENROUTER_API_KEY=sk-or-xxx  # OpenRouter API密钥（推荐，成本更低）
OPENAI_API_KEY=sk-xxx          # OpenAI API密钥（备用）
ANTHROPIC_API_KEY=sk-xxx       # Anthropic API密钥（备用）

# 监控配置 (可选)
JAEGER_AGENT_HOST=localhost
JAEGER_AGENT_PORT=6831

# 数据源配置 (可选)
ALPHA_VANTAGE_API_KEY=xxx
FINNHUB_API_KEY=xxx

# OpenRouter API配置
OPENROUTER_API_KEY=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
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
CLAUDE_API_KEY=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
CLAUDE_BASE_URL=https://openrouter.ai/api/v1
PERPLEXITY_API_KEY=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
PERPLEXITY_BASE_URL=https://openrouter.ai/api/v1