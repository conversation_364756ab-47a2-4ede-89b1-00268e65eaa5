# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.venv/
venv/
ENV/
env/

# IDEs
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log
logs/
server.log
*.stdout
# OS
.DS_Store
Thumbs.db

# Environment variables
.env
.env.local
.env.*.local

# Testing
.coverage
.pytest_cache/
htmlcov/

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json