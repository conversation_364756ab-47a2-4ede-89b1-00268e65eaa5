{"name": "api-server", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api-server/src", "projectType": "application", "tags": ["type:app", "platform:server", "framework:fastapi"], "targets": {"dev": {"executor": "nx:run-commands", "options": {"command": "uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000", "cwd": "apps/api-server"}}, "test": {"executor": "nx:run-commands", "options": {"command": "python3 -m pytest", "cwd": "apps/api-server"}, "cache": true}, "lint": {"executor": "nx:run-commands", "options": {"command": "ruff check .", "cwd": "apps/api-server"}, "cache": true}, "type-check": {"executor": "nx:run-commands", "options": {"command": "python3 -m mypy .", "cwd": "apps/api-server"}, "cache": true}, "build": {"executor": "nx:run-commands", "options": {"command": "python3 -m build", "cwd": "apps/api-server"}, "outputs": ["{projectRoot}/dist"], "cache": true}}}