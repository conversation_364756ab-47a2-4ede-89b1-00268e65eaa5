"""
事件驱动投资分析测试demo
"""

import asyncio
import os
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage

from src.api.demo.graph import graph
from src.api.demo.state import InvestmentAnalysisState
from src.api.demo.configuration import VestConfiguration

load_dotenv()


async def test_investment_analysis():
    """测试事件驱动投资分析"""
    
    # 测试投资标的信息
    test_profile = """
    投资标的：特斯拉 (TSLA)
    
    基本信息：
    - 公司名称：Tesla, Inc.
    - 股票代码：TSLA (NASDAQ)
    - 行业：电动汽车、清洁能源
    - 市值：约8000亿美元
    
    近期事件：
    - 2024年第四季度交付量超预期
    - 自动驾驶技术取得重大突破
    - 中国市场份额持续增长
    - 新工厂建设计划公布
    
    关注点：
    - 电动汽车市场竞争加剧
    - 自动驾驶监管政策变化
    - 原材料价格波动
    - 马斯克的管理风格影响
    """
    
    # 初始状态
    initial_state: InvestmentAnalysisState = {
        "messages": [HumanMessage(content="请分析特斯拉的投资机会")],
        "profile": test_profile,
        "fact_check_questions": [],
        "prediction_questions": [],
        "quantitative_questions": [],
        "macro_analysis_results": [],
        "industry_chain_results": [],
        "quantitative_results": [],
        "final_synthesis_results": [],
        "macro_loop_count": 0,
        "industry_loop_count": 0,
        "quantitative_loop_count": 0,
        "final_loop_count": 0,
        "max_loops": 3,
        "fmp_api_key": os.getenv("FMP_API_KEY", ""),
    }
    
    # 配置
    config = {
        "configurable": {
            "max_macro_loops": 2,
            "max_industry_loops": 2,
            "max_quantitative_loops": 2,
            "enable_debug": True,
        }
    }
    
    print("🚀 开始事件驱动投资分析...")
    print(f"📊 分析标的: 特斯拉 (TSLA)")
    print("=" * 50)
    
    try:
        # 执行分析图
        final_state = await graph.ainvoke(initial_state, config=config)
        
        print("\n✅ 分析完成！")
        print("=" * 50)
        
        # 输出结果
        print("\n📋 事实核查问题:")
        for i, question in enumerate(final_state.get("fact_check_questions", []), 1):
            print(f"{i}. {question}")
        
        print("\n🔮 预测数据需求问题:")
        for i, question in enumerate(final_state.get("prediction_questions", []), 1):
            print(f"{i}. {question}")
        
        print("\n📊 量化筛选问题:")
        for i, question in enumerate(final_state.get("quantitative_questions", []), 1):
            print(f"{i}. {question}")
        
        print("\n🌍 宏观分析结果:")
        for i, result in enumerate(final_state.get("macro_analysis_results", []), 1):
            print(f"\n--- 宏观分析 {i} ---")
            print(result[:500] + "..." if len(result) > 500 else result)
        
        print("\n🏭 产业链分析结果:")
        for i, result in enumerate(final_state.get("industry_chain_results", []), 1):
            print(f"\n--- 产业链分析 {i} ---")
            print(result[:500] + "..." if len(result) > 500 else result)
        
        print("\n📈 量化分析结果:")
        for i, result in enumerate(final_state.get("quantitative_results", []), 1):
            print(f"\n--- 量化分析 {i} ---")
            print(result[:500] + "..." if len(result) > 500 else result)
        
        print("\n🎯 最终投资论点:")
        for i, result in enumerate(final_state.get("final_synthesis_results", []), 1):
            print(f"\n--- 最终综合分析 {i} ---")
            print(result)
        
        # 统计信息
        print("\n📊 分析统计:")
        print(f"   宏观分析循环次数: {final_state.get('macro_loop_count', 0)}")
        print(f"   产业链分析循环次数: {final_state.get('industry_loop_count', 0)}")
        print(f"   量化分析循环次数: {final_state.get('quantitative_loop_count', 0)}")
        print(f"   最终综合循环次数: {final_state.get('final_loop_count', 0)}")
        
        return final_state
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


async def test_configuration():
    """测试配置参数"""
    print("\n=== 配置测试 ===")
    
    # 测试不同配置
    configs_to_test = [
        {
            "name": "快速模式",
            "config": {
                "configurable": {
                    "max_macro_loops": 1,
                    "max_industry_loops": 1,
                    "max_quantitative_loops": 1,
                    "enable_debug": False
                }
            }
        },
        {
            "name": "标准模式", 
            "config": {
                "configurable": {
                    "max_macro_loops": 2,
                    "max_industry_loops": 2,
                    "max_quantitative_loops": 2,
                    "enable_debug": True
                }
            }
        },
        {
            "name": "深度模式",
            "config": {
                "configurable": {
                    "max_macro_loops": 3,
                    "max_industry_loops": 3,
                    "max_quantitative_loops": 3,
                    "enable_debug": True
                }
            }
        }
    ]
    
    test_profile = """
    投资标的：苹果公司 (AAPL)
    
    基本信息：
    - 公司名称：Apple Inc.
    - 股票代码：AAPL (NASDAQ)
    - 行业：消费电子、软件服务
    - 市值：约3万亿美元
    
    近期事件：
    - Vision Pro发布引发AR/VR市场关注
    - 服务业务持续增长
    - 中国市场面临挑战
    - AI功能集成到产品线
    """
    
    for test_config in configs_to_test:
        print(f"\n🔧 测试配置: {test_config['name']}")
        
        initial_state = {
            "messages": [HumanMessage(content="请分析苹果公司的投资机会")],
            "profile": test_profile,
            "fact_check_questions": [],
            "prediction_questions": [],
            "quantitative_questions": [],
            "macro_analysis_results": [],
            "industry_chain_results": [],
            "quantitative_results": [],
            "final_synthesis_results": [],
            "macro_loop_count": 0,
            "industry_loop_count": 0,
            "quantitative_loop_count": 0,
            "final_loop_count": 0,
            "max_loops": 3,
            "fmp_api_key": os.getenv("FMP_API_KEY", ""),
        }
        
        try:
            result = await graph.ainvoke(initial_state, config=test_config['config'])
            
            macro_count = result.get('macro_loop_count', 0)
            industry_count = result.get('industry_loop_count', 0)
            quantitative_count = result.get('quantitative_loop_count', 0)
            final_count = result.get('final_loop_count', 0)
            
            print(f"   📊 宏观循环: {macro_count}")
            print(f"   🏭 产业链循环: {industry_count}")
            print(f"   📈 量化循环: {quantitative_count}")
            print(f"   🎯 最终循环: {final_count}")
            print(f"   ✅ 配置测试通过")
            
        except Exception as e:
            print(f"   ❌ 配置测试失败: {str(e)}")



    """测试配置模型"""
    print("\n=== 配置模型测试 ===")
    
    try:
        # 测试默认配置
        config = VestConfiguration()
        print(f"✅ 默认配置创建成功")
        print(f"   分析模型: {config.analysis_model}")
        print(f"   问题生成模型: {config.question_generation_model}")
        print(f"   综合模型: {config.synthesis_model}")
        print(f"   最大宏观循环: {config.max_macro_loops}")
        print(f"   最大产业链循环: {config.max_industry_loops}")
        print(f"   最大量化循环: {config.max_quantitative_loops}")
        
        # 测试从配置创建
        test_runnable_config = {
            "configurable": {
                "max_macro_loops": 5,
                "max_industry_loops": 4,
                "enable_debug": False
            }
        }
        
        config_from_runnable = VestConfiguration.from_runnable_config(test_runnable_config)
        print(f"\n✅ 从RunnableConfig创建配置成功")
        print(f"   最大宏观循环: {config_from_runnable.max_macro_loops}")
        print(f"   最大产业链循环: {config_from_runnable.max_industry_loops}")
        print(f"   调试模式: {config_from_runnable.enable_debug}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置模型测试失败: {str(e)}")
        return False


async def test_multiple_targets():
    """测试多个投资标的"""
    print("\n=== 多标的测试 ===")
    
    targets = [
        {
            "name": "英伟达 (NVDA)",
            "profile": """
            投资标的：英伟达 (NVDA)
            
            基本信息：
            - 公司名称：NVIDIA Corporation
            - 股票代码：NVDA (NASDAQ)
            - 行业：半导体、AI芯片
            - 市值：约2万亿美元
            
            近期事件：
            - AI芯片需求爆发式增长
            - 数据中心业务快速扩张
            - 汽车芯片业务发展
            - 地缘政治影响供应链
            """
        },
        {
            "name": "比亚迪 (BYD)",
            "profile": """
            投资标的：比亚迪 (BYD)
            
            基本信息：
            - 公司名称：比亚迪股份有限公司
            - 股票代码：002594 (深交所), 1211 (港交所)
            - 行业：新能源汽车、电池
            - 市值：约3000亿人民币
            
            近期事件：
            - 新能源汽车销量持续增长
            - 刀片电池技术领先
            - 海外市场扩张加速
            - 产业链垂直整合优势
            """
        }
    ]
    
    config = {
        "configurable": {
            "max_macro_loops": 1,
            "max_industry_loops": 1,
            "max_quantitative_loops": 1,
            "enable_debug": False,
        }
    }
    
    for target in targets:
        print(f"\n🎯 分析标的: {target['name']}")
        print("-" * 30)
        
        initial_state = {
            "messages": [HumanMessage(content=f"请分析{target['name']}的投资机会")],
            "profile": target['profile'],
            "fact_check_questions": [],
            "prediction_questions": [],
            "quantitative_questions": [],
            "macro_analysis_results": [],
            "industry_chain_results": [],
            "quantitative_results": [],
            "final_synthesis_results": [],
            "macro_loop_count": 0,
            "industry_loop_count": 0,
            "quantitative_loop_count": 0,
            "final_loop_count": 0,
            "max_loops": 2,
            "fmp_api_key": os.getenv("FMP_API_KEY", ""),
        }
        
        try:
            result = await graph.ainvoke(initial_state, config=config)
            
            print(f"   ✅ {target['name']} 分析完成")
            print(f"   📋 生成问题数: {len(result.get('fact_check_questions', []))}")
            print(f"   🔄 总循环次数: {result.get('macro_loop_count', 0) + result.get('industry_loop_count', 0) + result.get('quantitative_loop_count', 0)}")
            
            # 显示最终论点摘要
            final_results = result.get('final_synthesis_results', [])
            if final_results:
                summary = final_results[-1][:200] + "..." if len(final_results[-1]) > 200 else final_results[-1]
                print(f"   💡 投资论点摘要: {summary}")
            
        except Exception as e:
            print(f"   ❌ {target['name']} 分析失败: {str(e)}")


async def main():
    """主测试函数"""
    print("🎯 事件驱动投资分析系统测试")
    print("=" * 60)
    
    # 运行测试
    try:
        # 1. 配置模型测试        
        # 2. 主要功能测试
        await test_investment_analysis()
        
        # 3. 配置参数测试
        await test_configuration()
        
        # 4. 多标的测试
        await test_multiple_targets()
        
        print("\n🎉 所有测试完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())