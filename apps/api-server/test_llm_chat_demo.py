#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLMClientUtils create_llm Chat 测试 Demo
测试不同 LLM 模型的聊天功能
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目路径到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from langchain_core.messages import HumanMessage, SystemMessage
from src.api.demo.llm_client_utils import LLMClientUtils
from src.api.demo.llm_client_enum import LLMClientEnum
from src.api.demo.llm_model import LLMNacosConfig


class LLMChatTester:
    """LLM Chat 测试器"""
    
    def __init__(self):
        self.test_results = []
        
    def print_separator(self, title: str):
        """打印分隔符"""
        print("\n" + "=" * 60)
        print(f" {title} ")
        print("=" * 60)
        
    def print_config_info(self):
        """打印配置信息"""
        self.print_separator("LLM 配置信息")
        
      # 确保获取正确的配置实例
        config = LLMNacosConfig.get_instance()
    # 检查config是否为LLMNacosConfig实例
        if not isinstance(config, LLMNacosConfig):
            print(f"错误：config对象类型不正确，实际类型为: {type(config)}")
            return
        available_llms = config.get_available_llms()

        print(f"可用的 LLM: {available_llms}")
        
        for llm_type in available_llms:
            llm_config = config.get_config(llm_type)
            if llm_config:
                print(f"\n{llm_type.upper()}:")
                print(f"  API Base: {llm_config.api_base}")
                print(f"  API Key: {llm_config.api_key[:10]}...{llm_config.api_key[-4:] if len(llm_config.api_key) > 14 else llm_config.api_key}")
        
        primary_llm = config.get_primary_llm()
        if primary_llm:
            print(f"\n主要 LLM: {primary_llm.api_base}")
    
    def test_single_llm(self, model_type: LLMClientEnum, test_message: str = "你好，请简单介绍一下你自己"):
        """测试单个 LLM 模型"""
        self.print_separator(f"测试 {model_type.description}")
        
        try:
            print(f"模型: {model_type.model_name}")
            print(f"LLM 类型: {model_type.llm}")
            print(f"测试消息: {test_message}")
            print("-" * 40)
            
            # 创建 LLM 实例
            llm = LLMClientUtils.create_llm(
                model_type=model_type,
                streaming=False,
                temperature=0.7
            )
            
            print("✅ LLM 实例创建成功")
            
            # 准备消息
            messages = [
                SystemMessage(content="你是一个有用的AI助手，请用中文回答问题。"),
                HumanMessage(content=test_message)
            ]
            
            # 发送消息并获取响应
            print("🔄 正在发送消息...")
            response = llm.invoke(messages)
            
            print("\n📝 AI 响应:")
            print(f"{response.content}")
            
            # 记录测试结果
            self.test_results.append({
                "model": model_type.description,
                "model_name": model_type.model_name,
                "llm_type": model_type.llm,
                "status": "成功",
                "response_length": len(response.content),
                "error": None
            })
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            
            # 记录失败结果
            self.test_results.append({
                "model": model_type.description,
                "model_name": model_type.model_name,
                "llm_type": model_type.llm,
                "status": "失败",
                "response_length": 0,
                "error": str(e)
            })
            
            return False
    
    async def test_async_llm(self, model_type: LLMClientEnum, test_message: str = "请用一句话总结人工智能的发展趋势"):
        """测试异步 LLM 模型"""
        self.print_separator(f"异步测试 {model_type.description}")
        
        try:
            print(f"模型: {model_type.model_name}")
            print(f"测试消息: {test_message}")
            print("-" * 40)
            
            # 创建异步 LLM 实例
            llm = await LLMClientUtils.async_create_llm(
                model_type=model_type,
                streaming=False,
                temperature=0.7
            )
            
            print("✅ 异步 LLM 实例创建成功")
            
            # 准备消息
            messages = [
                SystemMessage(content="你是一个专业的AI分析师，请简洁回答。"),
                HumanMessage(content=test_message)
            ]
            
            # 异步发送消息
            print("🔄 正在异步发送消息...")
            response = await llm.ainvoke(messages)
            
            print("\n📝 AI 响应:")
            print(f"{response.content}")
            
            return True
            
        except Exception as e:
            print(f"❌ 异步测试失败: {str(e)}")
            return False
    
    def test_streaming_chat(self, model_type: LLMClientEnum, test_message: str = "请详细解释什么是机器学习"):
        """测试流式聊天"""
        self.print_separator(f"流式测试 {model_type.description}")
        
        try:
            print(f"模型: {model_type.model_name}")
            print(f"测试消息: {test_message}")
            print("-" * 40)
            
            # 创建流式 LLM 实例
            llm = LLMClientUtils.create_llm(
                model_type=model_type,
                streaming=True,
                temperature=0.7
            )
            
            print("✅ 流式 LLM 实例创建成功")
            
            # 准备消息
            messages = [
                SystemMessage(content="你是一个教育专家，请详细解释概念。"),
                HumanMessage(content=test_message)
            ]
            
            print("🔄 正在流式发送消息...")
            print("\n📝 AI 流式响应:")
            
            # 流式处理响应
            full_response = ""
            for chunk in llm.stream(messages):
                if hasattr(chunk, 'content') and chunk.content:
                    print(chunk.content, end='', flush=True)
                    full_response += chunk.content
            
            print(f"\n\n✅ 流式响应完成，总长度: {len(full_response)} 字符")
            return True
            
        except Exception as e:
            print(f"❌ 流式测试失败: {str(e)}")
            return False
    
    def print_test_summary(self):
        """打印测试总结"""
        self.print_separator("测试总结")
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["status"] == "成功"])
        failed_tests = total_tests - successful_tests
        
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(successful_tests/total_tests*100):.1f}%" if total_tests > 0 else "成功率: 0%")
        
        print("\n详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "成功" else "❌"
            print(f"{status_icon} {result['model']} ({result['model_name']})")
            if result["error"]:
                print(f"   错误: {result['error']}")
            elif result["response_length"] > 0:
                print(f"   响应长度: {result['response_length']} 字符")


def main():
    """主测试函数"""
    print("🚀 开始 LLMClientUtils Chat 功能测试")
    
    tester = LLMChatTester()
    
    # 显示配置信息
    tester.print_config_info()
    
    # 定义要测试的模型列表
    test_models = [
        # Claude (通过OpenRouter)
        LLMClientEnum.CLAUDE_SONNET_4,  # 注意：这里有重复定义，实际是claude
    ]
    
    # 基础同步测试
    for model in test_models:
        success = tester.test_single_llm(model)
        if success:
            # 如果基础测试成功，进行流式测试
            tester.test_streaming_chat(model, "请简要介绍Python编程语言的特点")
            break  # 只测试第一个成功的模型的流式功能
    
    # 异步测试（测试第一个可用的模型）
    async def run_async_test():
        for model in test_models:
            try:
                await tester.test_async_llm(model)
                break  # 只测试第一个成功的模型
            except Exception:
                continue
    
    # 运行异步测试
    try:
        asyncio.run(run_async_test())
    except Exception as e:
        print(f"异步测试跳过: {e}")
    
    # 打印测试总结
    tester.print_test_summary()
    
    print("\n🎉 测试完成！")


if __name__ == "__main__":
    main()