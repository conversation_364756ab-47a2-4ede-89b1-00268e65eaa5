#!/usr/bin/env python3
"""
事实核查功能测试脚本
用于验证API端点和核心功能
"""
import asyncio
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from src.domain.fact_check.workflow import fact_check_workflow
from src.infrastructure.services.llm import LLMFactory

async def test_claim_extraction():
    """测试声明提取功能"""
    print("=" * 50)
    print("测试声明提取功能")
    print("=" * 50)
    
    test_text = """
    据报道，苹果公司在2024年第四季度的营收达到了946亿美元，同比增长6%。
    公司计划在2025年春季发布新款MacBook Pro，搭载M4 Pro芯片。
    分析师预计苹果股价将在未来6个月内上涨15%。
    """
    
    task_id = "test_extraction_001"
    
    try:
        # 运行工作流
        print(f"启动事实核查任务: {task_id}")
        print(f"输入文本: {test_text.strip()}")
        
        result = await fact_check_workflow.run_fact_check(
            task_id=task_id,
            text=test_text.strip(),
            options={
                "enable_financial_verification": True,
                "enable_corporate_verification": False,
                "enable_news_verification": False,
                "deep_research_mode": False
            }
        )
        
        print("\n✅ 任务完成!")
        
        # 显示结果
        if result.get("final_report"):
            report = result["final_report"]
            print(f"\n📊 核查摘要:")
            print(f"- 总声明数: {report['claims_summary']['total_claims']}")
            print(f"- 已验证: {report['claims_summary']['verified']}")
            print(f"- 存在矛盾: {report['claims_summary']['contradicted']}")
            print(f"- 未能验证: {report['claims_summary']['unverified']}")
            print(f"- 总成本: ${report['total_cost']:.4f}")
            print(f"- 处理时间: {report['processing_time']:.1f}秒")
            
            print(f"\n📋 提取的声明:")
            for i, claim in enumerate(report['claims'], 1):
                print(f"{i}. \"{claim['text']}\"")
                print(f"   类型: {claim['type']}")
                print(f"   状态: {claim['status']}")
                print(f"   置信度: {claim['confidence']:.2f}")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_llm_connection():
    """测试LLM连接"""
    print("=" * 50)
    print("测试LLM连接")
    print("=" * 50)
    
    try:
        # 测试OpenRouter Claude 4
        print("测试OpenRouter Claude 4连接...")
        llm = LLMFactory.create_claude4_llm(task_type="test")
        
        # 简单测试
        from langchain_core.messages import HumanMessage
        messages = [HumanMessage(content="请用一句话介绍你自己")]
        
        response = await llm.ainvoke(messages)
        print(f"✅ OpenRouter Claude 4响应: {response.content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM连接测试失败: {str(e)}")
        
        # 尝试fallback
        try:
            print("尝试默认LLM...")
            llm = LLMFactory.create_default_llm()
            messages = [HumanMessage(content="测试连接")]
            response = await llm.ainvoke(messages)
            print(f"✅ 默认LLM响应: {response.content[:50]}...")
            return True
        except Exception as e2:
            print(f"❌ 默认LLM也失败: {str(e2)}")
            return False

async def test_cost_tracking():
    """测试成本追踪"""
    print("=" * 50)
    print("测试成本追踪")
    print("=" * 50)
    
    from src.infrastructure.cost_tracker import cost_tracker
    
    # 模拟一些调用
    cost1 = cost_tracker.record_call(
        model="anthropic/claude-3-5-sonnet",
        input_tokens=100,
        output_tokens=50,
        task_type="test"
    )
    
    cost2 = cost_tracker.record_call(
        model="anthropic/claude-3-5-sonnet", 
        input_tokens=200,
        output_tokens=100,
        task_type="test"
    )
    
    print(f"记录调用1成本: ${cost1:.4f}")
    print(f"记录调用2成本: ${cost2:.4f}")
    
    stats = cost_tracker.get_stats()
    print(f"\n📊 成本统计:")
    print(f"- 总调用次数: {stats['total_calls']}")
    print(f"- 总成本: ${stats['total_cost']:.4f}")
    print(f"- 今日成本: ${stats['daily_cost']:.4f}")
    
    if stats['optimization_suggestions']:
        print(f"\n💡 优化建议:")
        for suggestion in stats['optimization_suggestions']:
            print(f"- {suggestion}")
    
    return True

async def main():
    """主测试函数"""
    print("🚀 开始事实核查功能测试")
    print()
    
    # 检查环境变量
    if not os.getenv("OPENROUTER_API_KEY"):
        print("⚠️ 警告: 未设置OPENROUTER_API_KEY环境变量")
        print("某些测试可能会失败")
        print()
    
    tests = [
        ("成本追踪", test_cost_tracking),
        ("LLM连接", test_llm_connection),
        ("声明提取", test_claim_extraction),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
        
        print()
    
    # 总结
    print("=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！系统准备就绪。")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖。")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)