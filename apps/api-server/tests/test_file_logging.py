#!/usr/bin/env python3
"""
测试文件日志功能
"""
import os
import time
from pathlib import Path
from yai_nexus_logger import init_logging, get_logger, trace_context

def test_file_logging():
    """测试文件日志输出"""
    print("🧪 测试文件日志功能...")
    
    # 设置环境变量启用文件日志
    os.environ["LOG_APP_NAME"] = "investor-insight"
    os.environ["LOG_LEVEL"] = "DEBUG"
    os.environ["LOG_CONSOLE_ENABLED"] = "true"
    os.environ["LOG_FILE_ENABLED"] = "true"
    os.environ["LOG_FILE_PATH"] = "logs/investor-insight.log"
    
    # 初始化日志
    init_logging()
    logger = get_logger(__name__)
    
    # 记录一些测试日志
    logger.info("🚀 开始文件日志测试")
    logger.debug("这是调试信息", extra={"test_type": "file_logging"})
    logger.warn("这是警告信息", extra={"warning_code": "W001"})
    logger.error("这是错误信息", extra={"error_code": "E001", "details": "测试错误"})
    
    # 测试 trace_id
    trace_id = "test-trace-123"
    token = trace_context.set_trace_id(trace_id)
    
    try:
        logger.info("带 trace_id 的日志", extra={
            "user_id": "user_123",
            "action": "file_log_test",
            "timestamp": time.time()
        })
    finally:
        trace_context.reset_trace_id(token)
    
    logger.info("✅ 文件日志测试完成")
    
    # 检查文件是否存在
    log_file = Path("logs/investor-insight.log")
    if log_file.exists():
        print(f"✅ 日志文件已创建: {log_file.absolute()}")
        print(f"📁 文件大小: {log_file.stat().st_size} bytes")
        
        # 显示文件内容
        print("\n📄 日志文件内容:")
        print("-" * 80)
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
        print("-" * 80)
        
        return True
    else:
        print("❌ 日志文件未创建")
        return False

if __name__ == "__main__":
    success = test_file_logging()
    if success:
        print("\n🎉 文件日志测试成功！")
        print("📂 日志文件位置: apps/api-server/logs/investor-insight.log")
    else:
        print("\n❌ 文件日志测试失败！")
