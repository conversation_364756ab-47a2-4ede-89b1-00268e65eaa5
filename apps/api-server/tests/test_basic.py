#!/usr/bin/env python3
"""
基础功能测试脚本
测试不需要API密钥的基础功能
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

async def test_imports():
    """测试基础导入"""
    print("测试基础导入...")
    
    try:
        from src.domain.fact_check.state import FactCheckState, create_initial_state, Claim, ClaimType
        print("✅ 状态模型导入成功")
        
        from src.infrastructure.cost_tracker import cost_tracker
        print("✅ 成本追踪器导入成功")
        
        from src.infrastructure.adapters.agui_event_sender import agui_event_sender
        print("✅ AGUI事件发送器导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False

async def test_state_creation():
    """测试状态创建"""
    print("测试状态创建...")
    
    try:
        from src.domain.fact_check.state import create_initial_state
        
        state = create_initial_state(
            task_id="test_001",
            original_text="这是一个测试文本",
            options={"enable_financial_verification": True}
        )
        
        print(f"✅ 状态创建成功")
        print(f"  - 任务ID: {state['task_id']}")
        print(f"  - 原始文本: {state['original_text']}")
        print(f"  - Agent数量: {len(state['agents'])}")
        print(f"  - 当前步骤: {state['current_step']}")
        
        return True
    except Exception as e:
        print(f"❌ 状态创建失败: {str(e)}")
        return False

async def test_cost_tracking():
    """测试成本追踪"""
    print("测试成本追踪...")
    
    try:
        from src.infrastructure.cost_tracker import cost_tracker
        
        # 记录一些模拟调用
        cost1 = cost_tracker.record_call(
            model="anthropic/claude-3-5-sonnet",
            input_tokens=100,
            output_tokens=50,
            task_type="test"
        )
        
        cost2 = cost_tracker.record_call(
            model="anthropic/claude-3-5-sonnet",
            input_tokens=200,
            output_tokens=100,
            task_type="test"
        )
        
        stats = cost_tracker.get_stats()
        
        print(f"✅ 成本追踪测试通过")
        print(f"  - 调用1成本: ${cost1:.4f}")
        print(f"  - 调用2成本: ${cost2:.4f}")
        print(f"  - 总调用次数: {stats['total_calls']}")
        print(f"  - 总成本: ${stats['total_cost']:.4f}")
        
        return True
    except Exception as e:
        print(f"❌ 成本追踪测试失败: {str(e)}")
        return False

async def test_agui_event_sender():
    """测试AGUI事件发送器"""
    print("测试AGUI事件发送器...")
    
    try:
        from src.infrastructure.adapters.agui_event_sender import (
            agui_event_sender, 
            FactCheckEventType, 
            AgentRole,
            FactCheckEventPayload
        )
        
        # 创建事件载荷
        payload = FactCheckEventPayload(
            task_id="test_001",
            agent_role=AgentRole.CLAIM_EXTRACTOR,
            progress=50,
            message="测试消息"
        )
        
        print(f"✅ AGUI事件发送器测试通过")
        print(f"  - 事件类型数量: {len(FactCheckEventType)}")
        print(f"  - Agent角色数量: {len(AgentRole)}")
        print(f"  - 测试事件载荷: {payload.task_id}")
        
        return True
    except Exception as e:
        print(f"❌ AGUI事件发送器测试失败: {str(e)}")
        return False

async def test_api_endpoint():
    """测试API端点"""
    print("测试API端点...")
    
    try:
        from src.api.v1.endpoints.fact_check import router
        
        print(f"✅ API端点导入成功")
        print(f"  - 路由前缀: {router.prefix}")
        print(f"  - 路由数量: {len(router.routes)}")
        
        return True
    except Exception as e:
        print(f"❌ API端点测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始基础功能测试")
    print("=" * 50)
    
    tests = [
        ("基础导入", test_imports),
        ("状态创建", test_state_creation),
        ("成本追踪", test_cost_tracking),
        ("AGUI事件发送器", test_agui_event_sender),
        ("API端点", test_api_endpoint),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
        print()
    
    # 总结
    print("=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有基础测试都通过了！")
        print("\n📝 下一步:")
        print("1. 设置 OPENROUTER_API_KEY 环境变量")
        print("2. 启动后端服务: python main.py")
        print("3. 访问前端页面: http://localhost:4200/fact-check")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查代码。")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)