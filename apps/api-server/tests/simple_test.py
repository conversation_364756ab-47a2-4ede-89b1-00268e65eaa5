import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import yfinance as yf

# 简单测试yfinance
print("测试 yfinance...")
aapl = yf.Ticker("AAPL")
info = aapl.info
print(f"苹果公司: {info.get('longName')}")
print(f"当前价格: ${info.get('currentPrice')}")
print("yfinance 工作正常!")

# 测试我们的工具
from tools.market_data_tool import MarketDataTool

print("\n测试 MarketDataTool...")
tool = MarketDataTool()
result = tool._run("AAPL", "1mo", "price")
print(result[:500] + "...")  # 只显示前500字符