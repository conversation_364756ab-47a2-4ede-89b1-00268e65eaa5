#!/usr/bin/env python3
"""测试投资工具"""

import asyncio
from tools.market_data_tool import MarketDataTool

async def test_market_data_tool():
    """测试市场数据工具"""
    print("🧪 测试 MarketDataTool...")
    
    tool = MarketDataTool()
    
    # 测试苹果公司股票
    print("\n📊 获取 AAPL 数据...")
    result = await tool._arun("AAPL", "1mo", "all")
    print(result)
    
    # 测试特斯拉股票
    print("\n📊 获取 TSLA 价格信息...")
    result = await tool._arun("TSLA", "1mo", "price")
    print(result)

if __name__ == "__main__":
    asyncio.run(test_market_data_tool())