#!/usr/bin/env python3
"""
测试 trace_id 追踪功能的脚本
"""
import asyncio
import httpx
import json
from yai_nexus_logger import get_logger, init_logging

# 初始化日志
init_logging()
logger = get_logger(__name__)

async def test_trace_id_flow():
    """测试完整的 trace_id 流程"""
    logger.info("开始测试 trace_id 追踪功能")
    
    # 测试用的 trace_id
    test_trace_id = "test_trace_12345"
    
    async with httpx.AsyncClient() as client:
        try:
            # 1. 测试健康检查端点
            logger.info("测试健康检查端点", trace_id=test_trace_id)
            response = await client.get(
                "http://localhost:8000/api/v1/health",
                headers={
                    "X-Request-ID": test_trace_id,
                    "X-Trace-ID": test_trace_id
                }
            )
            
            logger.info("健康检查响应", {
                "status_code": response.status_code,
                "response_trace_id": response.headers.get("X-Request-ID"),
                "trace_id": test_trace_id
            })
            
            # 2. 测试根端点
            logger.info("测试根端点", trace_id=test_trace_id)
            response = await client.get(
                "http://localhost:8000/",
                headers={
                    "X-Request-ID": test_trace_id,
                    "X-Trace-ID": test_trace_id
                }
            )
            
            logger.info("根端点响应", {
                "status_code": response.status_code,
                "response_trace_id": response.headers.get("X-Request-ID"),
                "trace_id": test_trace_id
            })
            
            # 验证响应头中的 trace_id
            response_trace_id = response.headers.get("X-Request-ID")
            if response_trace_id == test_trace_id:
                logger.info("✅ trace_id 传递成功", {
                    "sent_trace_id": test_trace_id,
                    "received_trace_id": response_trace_id
                })
            else:
                logger.error("❌ trace_id 传递失败", {
                    "sent_trace_id": test_trace_id,
                    "received_trace_id": response_trace_id
                })
                
        except Exception as e:
            logger.error("测试过程中发生错误", {
                "error": str(e),
                "trace_id": test_trace_id
            })

async def test_multiple_requests():
    """测试多个并发请求的 trace_id 隔离"""
    logger.info("开始测试多个并发请求的 trace_id 隔离")
    
    async def make_request(trace_id: str):
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "http://localhost:8000/api/v1/health",
                headers={
                    "X-Request-ID": trace_id,
                    "X-Trace-ID": trace_id
                }
            )
            return trace_id, response.headers.get("X-Request-ID")
    
    # 创建多个并发请求
    tasks = []
    for i in range(5):
        trace_id = f"concurrent_test_{i}"
        tasks.append(make_request(trace_id))
    
    results = await asyncio.gather(*tasks)
    
    # 验证结果
    for sent_id, received_id in results:
        if sent_id == received_id:
            logger.info("✅ 并发请求 trace_id 隔离成功", {
                "sent_trace_id": sent_id,
                "received_trace_id": received_id
            })
        else:
            logger.error("❌ 并发请求 trace_id 隔离失败", {
                "sent_trace_id": sent_id,
                "received_trace_id": received_id
            })

if __name__ == "__main__":
    print("🧪 开始测试 trace_id 追踪功能...")
    print("请确保后端服务正在运行 (python main.py)")
    print("=" * 50)
    
    asyncio.run(test_trace_id_flow())
    print("=" * 50)
    asyncio.run(test_multiple_requests())
    
    print("=" * 50)
    print("✅ 测试完成！请检查日志输出中的 trace_id 字段")
