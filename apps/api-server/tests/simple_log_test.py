#!/usr/bin/env python3
"""
简单的日志测试脚本 - 测试 yai-nexus-logger 基本功能
"""
import os
from yai_nexus_logger import init_logging, get_logger, trace_context
import uuid

def test_basic_logging():
    """测试基础日志功能"""
    print("🧪 测试基础日志功能...")
    
    # 设置环境变量
    os.environ["LOG_APP_NAME"] = "test-app"
    os.environ["LOG_LEVEL"] = "DEBUG"
    os.environ["LOG_CONSOLE_ENABLED"] = "true"
    
    # 初始化日志
    init_logging()
    
    # 获取日志器
    logger = get_logger(__name__)
    
    # 测试不同级别的日志
    logger.debug("这是一个调试日志", extra={"test": "debug"})
    logger.info("这是一个信息日志", extra={"test": "info"})
    logger.warn("这是一个警告日志", extra={"test": "warn"})
    logger.error("这是一个错误日志", extra={"test": "error"})
    
    print("✅ 基础日志测试完成")

def test_trace_id():
    """测试 trace_id 功能"""
    print("🧪 测试 trace_id 功能...")
    
    logger = get_logger(__name__)
    
    # 测试 trace_id 设置和获取
    test_trace_id = str(uuid.uuid4())
    
    # 设置 trace_id
    token = trace_context.set_trace_id(test_trace_id)
    
    try:
        logger.info("带 trace_id 的日志", extra={
            "description": "这条日志应该包含 trace_id",
            "test_data": {"key": "value"}
        })
        
        # 验证 trace_id 是否正确设置
        current_trace_id = trace_context.get_trace_id()
        if current_trace_id == test_trace_id:
            print(f"✅ trace_id 设置成功: {current_trace_id}")
        else:
            print(f"❌ trace_id 设置失败: 期望 {test_trace_id}, 实际 {current_trace_id}")
            
    finally:
        # 清理 trace_id
        trace_context.reset_trace_id(token)
    
    print("✅ trace_id 测试完成")

def test_structured_logging():
    """测试结构化日志"""
    print("🧪 测试结构化日志功能...")
    
    logger = get_logger(__name__)
    
    # 测试复杂的结构化数据
    logger.info("用户操作日志", extra={
        "user_id": "user_123",
        "action": "login",
        "ip_address": "***********",
        "user_agent": "Mozilla/5.0...",
        "metadata": {
            "session_id": "session_456",
            "device_type": "desktop",
            "location": "Beijing"
        }
    })
    
    logger.error("API 错误日志", extra={
        "endpoint": "/api/v1/users",
        "method": "POST",
        "status_code": 500,
        "error_message": "Database connection failed",
        "request_data": {
            "username": "test_user",
            "email": "<EMAIL>"
        },
        "stack_trace": "Traceback (most recent call last)..."
    })
    
    print("✅ 结构化日志测试完成")

def test_multiple_loggers():
    """测试多个日志器"""
    print("🧪 测试多个日志器...")
    
    # 创建不同模块的日志器
    auth_logger = get_logger("auth_module")
    api_logger = get_logger("api_module")
    db_logger = get_logger("database_module")
    
    # 设置不同的 trace_id
    trace_id_1 = str(uuid.uuid4())
    trace_id_2 = str(uuid.uuid4())
    
    # 模拟不同模块的日志
    token1 = trace_context.set_trace_id(trace_id_1)
    auth_logger.info("用户认证成功", extra={"user_id": "user_123"})
    api_logger.info("API 请求处理", extra={"endpoint": "/api/users"})
    trace_context.reset_trace_id(token1)
    
    token2 = trace_context.set_trace_id(trace_id_2)
    db_logger.info("数据库查询", extra={"query": "SELECT * FROM users"})
    api_logger.info("API 响应", extra={"status": 200})
    trace_context.reset_trace_id(token2)
    
    print("✅ 多个日志器测试完成")

if __name__ == "__main__":
    print("🚀 开始 yai-nexus-logger 功能测试...")
    print("=" * 60)
    
    try:
        test_basic_logging()
        print()
        
        test_trace_id()
        print()
        
        test_structured_logging()
        print()
        
        test_multiple_loggers()
        print()
        
        print("=" * 60)
        print("🎉 所有测试完成！")
        print("请检查上面的日志输出，确认格式和内容正确。")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
