"""主应用入口 - 基于依赖注入容器的新架构"""
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, Response, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Optional
import os
import uuid
import json
import asyncio
import time
from dotenv import load_dotenv

from src.core.container import ContainerManager
from src.core.settings import settings
from src.infrastructure.services.logging import configure_logging, get_logger
from src.infrastructure.services.monitoring import configure_tracing

# 加载环境变量
load_dotenv()

logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时配置
    log_level = os.getenv("LOG_LEVEL", "INFO")
    configure_logging(log_level)
    configure_tracing("investor-insight")
    
    logger.info("Application startup initiated")
    
    try:
        # 初始化依赖注入容器
        logger.info("Initializing dependency injection container...")
        container = ContainerManager().initialize()
        
        logger.info("Application startup completed successfully")
        
        yield
        
    except Exception as e:
        logger.error("Application startup failed", error=str(e))
        raise
    finally:
        # 关闭时清理
        logger.info("Application shutdown initiated")
        logger.info("Application shutdown completed")

# 创建 FastAPI 应用
app = FastAPI(
    title=settings.app_name,
    description="AI-powered investment research platform backend",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if os.getenv("ENVIRONMENT") == "development" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") == "development" else None
)

# 请求日志中间件 - 记录请求信息和性能指标
@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    """请求日志中间件 - 记录请求信息和性能指标"""
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # 记录请求开始
    logger.info("请求开始", extra={
        "request_id": request_id,
        "method": request.method,
        "url": str(request.url),
        "client_ip": request.client.host,
        "user_agent": request.headers.get("user-agent", "")
    })

    # 记录 DEBUG 级别的详细信息（会上报到 SLS）
    logger.debug("请求详细信息", extra={
        "request_id": request_id,
        "headers": dict(request.headers),
        "query_params": dict(request.query_params)
    })

    # 处理请求
    response = await call_next(request)

    # 计算处理时间
    process_time = time.time() - start_time

    # 记录请求结束
    logger.info("请求结束", extra={
        "request_id": request_id,
        "status_code": response.status_code,
        "process_time": f"{process_time:.3f}s"
    })

    # 记录性能指标（DEBUG 级别）
    logger.debug("请求性能指标", extra={
        "request_id": request_id,
        "process_time_ms": round(process_time * 1000, 2),
        "response_size": response.headers.get("content-length", "unknown")
    })

    response.headers["X-Request-ID"] = request_id
    response.headers["X-Process-Time"] = f"{process_time:.3f}"
    return response

# CORS 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:4200",  # 添加前端开发服务器端口
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001",
        "http://127.0.0.1:4200"   # 添加前端开发服务器端口
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*", "X-Request-ID", "X-Trace-ID"],  # 添加 trace_id 相关头
)

# 路由配置
from src.api.v1 import v1_router
app.include_router(v1_router, prefix="/api")

# 插件路由注册
try:
    from demo_feature_bs import router as demo_feature_router
    app.include_router(demo_feature_router)
    logger.info("Demo feature plugin registered successfully")
except ImportError:
    logger.warning("Demo feature plugin not found or failed to import")

# 研究请求模型
class ResearchRequest(BaseModel):
    """研究请求模型"""
    
    query: str = Field(
        ...,
        description="投资研究查询内容",
        min_length=1,
        max_length=10000,
        example="请分析特斯拉(TSLA)的投资价值"
    )
    thread_id: Optional[str] = Field(
        None,
        description="对话线程ID，用于多轮对话上下文追踪",
        example="thread_abc123"
    )

# 全局变量：投资分析图和适配器
_analysis_graph = None
_agui_adapter = None

def get_analysis_graph():
    """获取投资分析图实例（单例模式）"""
    global _analysis_graph
    if _analysis_graph is None:
        try:
            from src.api.demo.graph import graph
            _analysis_graph = graph
            logger.info("Investment analysis graph created successfully")
        except Exception as e:
            logger.error(f"Failed to create investment analysis graph: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to initialize analysis graph: {str(e)}"
            )
    return _analysis_graph

def get_agui_adapter():
    """获取 AGUI 适配器实例（单例模式）"""
    global _agui_adapter
    if _agui_adapter is None:
        try:
            from src.api.adapter.agui_adapter import AGUIAdapter
            graph = get_analysis_graph()
            _agui_adapter = AGUIAdapter(graph)
            logger.info("AGUI adapter created successfully")
        except Exception as e:
            logger.error(f"Failed to create AGUI adapter: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to initialize AGUI adapter: {str(e)}"
            )
    return _agui_adapter

@app.get("/", tags=["root"])
async def root():
    """根路径"""
    logger.info("处理根路径请求")
    logger.debug("根路径调试信息 - 这条日志会上报到 SLS")

    return {
        "message": "Welcome to the Investor Insight API",
        "version": "1.0.0",
        "docs": "/docs" if os.getenv("ENVIRONMENT") == "development" else "disabled",
        "health": "/api/v1/health",
        "logging_test": "/test-logging"
    }

@app.get("/test-logging", tags=["debug"])
async def test_logging():
    """测试各种日志级别"""
    logger.debug("这是 DEBUG 级别日志 - 会上报到 SLS")
    logger.info("这是 INFO 级别日志")
    logger.warning("这是 WARNING 级别日志")
    logger.error("这是 ERROR 级别日志")

    # 结构化日志测试
    logger.info("结构化日志测试", extra={
        "user_id": 12345,
        "action": "test_logging",
        "metadata": {"key1": "value1", "key2": "value2"},
        "timestamp": time.time()
    })

    # 业务流程日志（PackId 自动关联）
    order_id = f"TEST-{int(time.time())}"
    logger.info("开始处理测试订单", extra={"order_id": order_id})
    logger.debug("验证订单数据", extra={"order_id": order_id, "step": "validation"})
    logger.info("订单验证通过", extra={"order_id": order_id, "step": "validation"})
    logger.debug("处理支付", extra={"order_id": order_id, "step": "payment", "amount": 99.99})
    logger.info("支付处理完成", extra={"order_id": order_id, "step": "payment", "status": "success"})
    logger.info("订单处理完成", extra={"order_id": order_id, "final_status": "completed"})

    return {
        "message": "日志测试完成，请检查 SLS 控制台",
        "order_id": order_id,
        "sls_project": "yai-log-test",
        "sls_logstore": "app-log",
        "note": "相关日志已通过 PackId 自动关联"
    }

@app.post("/research/stream", tags=["research"])
async def research_stream_endpoint(request: Request):
    """投资研究分析流式端点 - 兼容AGUI格式"""
    try:
        # 解析请求体
        body = await request.body()
        data = json.loads(body.decode())
        
        logger.info(f"收到研究流请求: {data}")
        
        # 提取AGUI消息内容
        thread_id = data.get("threadId", f"thread_{int(asyncio.get_event_loop().time() * 1000)}")
        run_id = data.get("runId", f"run_{int(asyncio.get_event_loop().time() * 1000)}")
        
        # AGUI消息在messages数组中
        messages = data.get("messages", [])
        if not messages:
            return {"error": "没有找到消息内容"}
        
        # 获取第一条消息的内容作为查询
        query = messages[0].get("content", "")
        
        if not query:
            return {"error": "缺少查询内容"}
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建任务对象
        from src.api.adapter.models import Task
        task = Task(
            id=task_id,
            query=query,
            thread_id=thread_id
        )
        
        logger.info(
            f"Starting research analysis for task {task_id}: {query[:100]}..."
        )
        
        # 获取适配器实例
        adapter = get_agui_adapter()
        
        # 创建流式响应
        async def event_stream():
            try:
                async for event_data in adapter.create_official_stream(task):
                    yield event_data
            except Exception as e:
                logger.exception(f"Error in event stream for task {task_id}: {e}")
                # 发送错误事件
                error_event = f'data: {{"type": "RUN_ERROR", "message": "{str(e)}"}}\n\n'
                yield error_event
        
        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )
        
    except Exception as e:
        logger.exception(f"Failed to start research analysis: {e}")
        return {"error": str(e)}

@app.post("/", tags=["agui"])
async def agui_post_endpoint(request: Request):
    """AGUI POST端点 - 处理HttpAgent请求"""
    try:
        # 解析请求体
        body = await request.body()
        data = json.loads(body.decode())

        logger.info(f"收到AGUI请求: {data}")

        # 提取AGUI消息内容
        thread_id = data.get("threadId", f"thread_{int(asyncio.get_event_loop().time() * 1000)}")
        run_id = data.get("runId", f"run_{int(asyncio.get_event_loop().time() * 1000)}")

        # AGUI消息在messages数组中
        messages = data.get("messages", [])
        if not messages:
            return {"error": "没有找到消息内容"}

        # 获取第一条消息的内容
        message_content = messages[0].get("content", "")

        # 解析消息内容
        try:
            message_data = json.loads(message_content) if isinstance(message_content, str) else message_content
            action = message_data.get("action")

            if action == "fact_check":
                # 处理事实核查请求 - 返回SSE流
                return StreamingResponse(
                    handle_fact_check_sse(message_data, thread_id, run_id),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Headers": "*",
                    }
                )
            else:
                # 默认响应
                return {"message": f"收到AGUI消息: {message_content}", "threadId": thread_id, "runId": run_id}

        except json.JSONDecodeError:
            # 如果消息不是JSON，返回默认响应
            return {"message": f"收到AGUI消息: {message_content}", "threadId": thread_id, "runId": run_id}

    except Exception as e:
        logger.error(f"AGUI端点错误: {e}", exc_info=True)
        return {"error": str(e)}

async def handle_fact_check_sse(message_data: dict, thread_id: str, run_id: str):
    """处理事实核查的SSE流"""
    try:
        logger.info(f"开始处理事实核查SSE流: {message_data}")

        task_id = message_data.get("taskId", f"fact_check_{int(asyncio.get_event_loop().time() * 1000)}")
        text = message_data.get("text", "")
        options = message_data.get("options", {})

        if not text:
            yield f"data: {json.dumps({'type': 'error', 'message': '缺少文本内容'})}\n\n"
            return

        # 发送开始事件 - 符合AGUI格式
        start_event = {
            'type': 'message',
            'content': f'事实核查任务已启动: {task_id}',
            'threadId': thread_id,
            'runId': run_id,
            'custom': {
                'type': 'fact_check_task_started',
                'payload': {
                    'taskId': task_id,
                    'data': {
                        'id': task_id,
                        'status': 'started',
                        'original_text': text,
                        'start_time': asyncio.get_event_loop().time()
                    }
                }
            }
        }
        yield f"data: {json.dumps(start_event)}\n\n"

        # 导入事实核查工作流
        from src.domain.fact_check.workflow import get_fact_check_workflow

        # 运行事实核查
        workflow = get_fact_check_workflow()
        final_state = await workflow.run_fact_check(
            task_id=task_id,
            text=text,
            options={
                "enable_financial_verification": options.get("enableFinancialVerification", True),
                "enable_corporate_verification": options.get("enableCorporateVerification", True),
                "enable_news_verification": options.get("enableNewsVerification", True),
                "deep_research_mode": options.get("deepResearchMode", False)
            }
        )

        # 发送完成事件 - 符合AGUI格式
        complete_event = {
            'type': 'message',
            'content': f'事实核查任务完成: {task_id}',
            'threadId': thread_id,
            'runId': run_id,
            'custom': {
                'type': 'fact_check_task_complete',
                'payload': {
                    'taskId': task_id,
                    'data': {
                        'id': task_id,
                        'status': 'completed',
                        'original_text': text,
                        'claims': final_state.get('final_report', {}).get('claims', []),
                        'claims_summary': final_state.get('final_report', {}).get('claims_summary', {}),
                        'total_cost': final_state.get('final_report', {}).get('total_cost', 0),
                        'processing_time': final_state.get('final_report', {}).get('processing_time', 0),
                        'timestamp': final_state.get('final_report', {}).get('timestamp', ''),
                        'has_conflicts': final_state.get('final_report', {}).get('has_conflicts', False),
                        'debates': []
                    }
                }
            }
        }
        yield f"data: {json.dumps(complete_event)}\n\n"

    except Exception as e:
        logger.error(f"事实核查SSE处理失败: {e}", exc_info=True)
        error_event = {
            'type': 'error',
            'content': f'事实核查任务失败: {str(e)}',
            'custom': {
                'type': 'fact_check_error',
                'payload': {
                    'taskId': task_id,
                    'error': str(e)
                }
            }
        }
        yield f"data: {json.dumps(error_event)}\n\n"

# 注意：此应用只能通过 uvicorn 启动
# 启动命令：uvicorn main:app --host 0.0.0.0 --port 8000 --reload