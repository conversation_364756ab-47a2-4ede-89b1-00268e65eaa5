<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Nx Workspace Project Graph</title>
    
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />

    <script id="environment" src="static/environment.js"></script>

    <!-- Theming -->
    <script>
      // On page load or when changing themes, best to add inline in `head` to avoid FOUC
      if (
        localStorage.getItem('nx-dep-graph-theme') === 'dark' ||
        (localStorage.getItem('nx-dep-graph-theme') === null &&
          window.matchMedia('(prefers-color-scheme: dark)').matches)
      ) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    </script>
  <link rel="stylesheet" href="static/styles.css"></head>

  <body class="bg-white text-slate-500 dark:bg-slate-900 dark:text-slate-400">
    <div class="flex p-0" id="app"></div>
  <script src="static/runtime.js" ></script><script src="static/styles.js" ></script><script src="static/main.js" ></script></body>
</html>
