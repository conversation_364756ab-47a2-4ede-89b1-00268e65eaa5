# 投资研究事实核查功能设计方案

> **文档类型**: 产品功能讨论  
> **创建时间**: 2025-07-22  
> **状态**: 待讨论  
> **优先级**: 高

## 背景与目标

投资研究的第一步是事实核查，确保分析建立在准确可靠的信息基础上。作为 to C 产品，我们需要在成本可控的前提下，为用户提供有效的事实验证服务。

### 核心目标
- 🎯 **准确性**: 提供可信的信息验证服务
- 💰 **成本控制**: 适合 to C 产品的经济模式  
- ⚡ **用户体验**: 快速、直观的验证流程
- 🤖 **智能化**: AI 驱动的自动化验证

---

## 产品设计方案

### 1. 用户工作流设计

```mermaid
graph LR
    A[用户输入信息] --> B[智能提取声明]
    B --> C[多源验证]
    C --> D[可信度评分]
    D --> E[结果展示]
    E --> F[人工确认/标注]
```

#### 输入方式
- **文本粘贴**: 支持复制粘贴研究报告、新闻等
- **文档上传**: PDF、Word 等格式文档
- **URL 链接**: 直接输入网页链接进行分析
- **语音输入**: 移动端语音转文字

#### 信息提取
- **财务数据识别**: 营收、利润、市值等关键指标
- **公司声明提取**: 业绩预测、战略规划等声明
- **市场预测识别**: 行业趋势、价格预测等
- **时间敏感信息**: 自动识别需要实时验证的信息

### 2. 验证结果展示

#### 分层展示结构
```
📊 验证摘要
├── ✅ 已验证 (87%)    # 官方数据确认
├── ⚠️ 存疑 (10%)      # 信息不一致或过时
└── ❌ 无法验证 (3%)   # 缺乏可靠来源
```

#### 详细信息卡片
每个验证项目显示：
- **声明内容**: 原始信息摘要
- **验证状态**: 通过/存疑/失败
- **可信度分数**: 0-100分，基于多维度评估
- **数据来源**: 显示验证数据的具体来源
- **最后更新**: 数据的时效性标注
- **相关链接**: 跳转到原始数据源

#### 交互功能
- **一键深度验证**: 针对存疑信息进行更详细检查
- **用户标注**: 用户可以标记验证结果的准确性
- **分享功能**: 验证结果可以分享给团队
- **历史记录**: 查看之前的验证历史

### 3. 协作与反馈机制

#### 社区验证
- **专家标注**: 认证用户可以提供专业意见
- **众包验证**: 普通用户参与验证投票
- **声誉系统**: 基于贡献质量的用户评级

#### 错误修正
- **用户举报**: 发现错误时可以举报
- **官方审核**: 专业团队定期审核数据质量
- **自动更新**: 数据源更新时自动重新验证

---

## 技术实现方案

### 1. AI Agent 架构 (基于 LangGraph)

```python
# 事实核查工作流图
from langgraph.graph import StateGraph

class FactCheckState(TypedDict):
    input_content: str
    extracted_claims: List[Claim]
    verification_results: List[VerificationResult]
    confidence_scores: Dict[str, float]
    final_report: FactCheckReport

# 构建工作流
fact_check_graph = StateGraph(FactCheckState)

# 添加处理节点
fact_check_graph.add_node("extract_claims", extract_claims_node)
fact_check_graph.add_node("search_sources", search_sources_node)  
fact_check_graph.add_node("verify_facts", verify_facts_node)
fact_check_graph.add_node("calculate_scores", calculate_scores_node)
fact_check_graph.add_node("generate_report", generate_report_node)

# 添加条件路由
fact_check_graph.add_conditional_edges(
    "verify_facts",
    need_human_review,
    {
        "review_required": "human_review",
        "sufficient": "generate_report"
    }
)
```

### 2. 核心技术模块

#### 信息提取引擎
```python
class IntelligentClaimExtractor:
    """基于 NLP 的声明提取器"""
    
    def __init__(self):
        self.financial_model = load_financial_ner_model()
        self.statement_classifier = load_statement_classifier()
    
    def extract_financial_claims(self, text: str) -> List[FinancialClaim]:
        """提取财务相关声明"""
        entities = self.financial_model.extract_entities(text)
        claims = []
        
        for entity in entities:
            if entity.type in ['revenue', 'profit', 'market_cap']:
                claim = FinancialClaim(
                    content=entity.text,
                    value=entity.value,
                    company=entity.company,
                    time_period=entity.time_period,
                    confidence=entity.confidence
                )
                claims.append(claim)
        
        return claims
    
    def extract_predictive_statements(self, text: str) -> List[PredictiveStatement]:
        """提取预测性声明"""
        predictions = self.statement_classifier.classify_predictions(text)
        return [PredictiveStatement(**pred) for pred in predictions]
```

#### 多源验证引擎
```python
class MultiSourceVerifier:
    """多数据源验证引擎"""
    
    def __init__(self):
        self.data_sources = {
            'primary': [SECClient(), CompanyIRClient()],
            'secondary': [AlphaVantageClient(), YahooFinanceClient()],
            'tertiary': [WebScrapingClient(), CrowdsourceClient()]
        }
    
    async def verify_claim(self, claim: Claim) -> VerificationResult:
        results = []
        
        # 分层验证策略
        for tier, sources in self.data_sources.items():
            for source in sources:
                try:
                    result = await source.verify(claim)
                    if result:
                        results.append(result)
                        # 如果高级数据源已确认，可以提前结束
                        if tier == 'primary' and result.confidence > 0.9:
                            break
                except Exception as e:
                    logger.warning(f"Source {source} failed: {e}")
        
        return self.aggregate_results(results)
    
    def aggregate_results(self, results: List[SourceResult]) -> VerificationResult:
        """聚合多个数据源的验证结果"""
        if not results:
            return VerificationResult(status="unverified", confidence=0.0)
        
        # 加权平均，官方数据源权重更高
        weighted_confidence = sum(
            result.confidence * self.get_source_weight(result.source)
            for result in results
        ) / sum(self.get_source_weight(r.source) for r in results)
        
        return VerificationResult(
            status=self.determine_status(weighted_confidence),
            confidence=weighted_confidence,
            sources=results,
            last_updated=datetime.now()
        )
```

#### 可信度计算引擎
```python
class CredibilityScorer:
    """基于多维度的可信度评分"""
    
    def calculate_credibility(self, verification_result: VerificationResult) -> float:
        factors = {
            'source_authority': self.score_source_authority(verification_result.sources),
            'temporal_relevance': self.score_temporal_relevance(verification_result.timestamp),
            'cross_source_consistency': self.score_consistency(verification_result.sources),
            'data_completeness': self.score_completeness(verification_result.data),
            'historical_accuracy': self.score_historical_accuracy(verification_result.source_track_record)
        }
        
        # 加权计算最终分数
        weights = {
            'source_authority': 0.3,
            'temporal_relevance': 0.2,
            'cross_source_consistency': 0.25,
            'data_completeness': 0.15,
            'historical_accuracy': 0.1
        }
        
        final_score = sum(
            factors[factor] * weights[factor] 
            for factor in factors
        )
        
        return min(100, max(0, final_score * 100))  # 转换为0-100分
```

---

## 数据源获取策略

### 1. 分层数据源架构

#### 第一层：免费官方数据源 (权重: 80%)
```python
class OfficialDataSources:
    """免费官方数据源"""
    
    sources = {
        'sec_edgar': {
            'url': 'https://www.sec.gov/Archives/edgar/data',
            'description': 'SEC EDGAR 数据库 - 美股公司财报',
            'cost': 'FREE',
            'rate_limit': '无限制',
            'data_types': ['10-K', '10-Q', '8-K', 'DEF 14A']
        },
        'company_ir': {
            'description': '公司官方投资者关系页面',
            'cost': 'FREE',
            'rate_limit': '需要合理限频',
            'data_types': ['earnings_releases', 'financial_reports', 'guidance']
        },
        'regulatory_filings': {
            'description': '监管机构公开数据',
            'sources': ['证监会', '银保监会', '交易所'],
            'cost': 'FREE'
        }
    }
```

#### 第二层：免费/低成本 API (权重: 60%)
```python
class LowCostAPIs:
    """免费或低成本第三方 API"""
    
    apis = {
        'alpha_vantage': {
            'free_tier': '500 calls/day',
            'paid_tier': '$20/month for 5000 calls/day',
            'data_types': ['stock_prices', 'company_overview', 'financial_statements']
        },
        'yahoo_finance': {
            'cost': 'FREE (非官方)',
            'rate_limit': '需要控制频率',
            'reliability': '中等',
            'data_types': ['real_time_prices', 'basic_financials', 'news']
        },
        'financial_modeling_prep': {
            'free_tier': '250 calls/day', 
            'paid_tier': '$15/month for 1000 calls/day',
            'data_types': ['financial_statements', 'ratios', 'dcf_models']
        }
    }
```

#### 第三层：网页爬取与众包 (权重: 40%)
```python
class AlternativeDataSources:
    """网页爬取和众包数据"""
    
    def __init__(self):
        self.web_scraper = ComplianceWebScraper()
        self.crowdsource_manager = CrowdsourceManager()
    
    async def scrape_news_sources(self, query: str) -> List[NewsArticle]:
        """合规爬取财经新闻网站"""
        sources = [
            'finance.yahoo.com',
            'marketwatch.com', 
            'fool.com',
            'seekingalpha.com'  # 免费部分
        ]
        
        results = []
        for source in sources:
            # 遵循 robots.txt，控制访问频率
            articles = await self.web_scraper.scrape_with_compliance(
                source, query, respect_robots_txt=True
            )
            results.extend(articles)
        
        return results
    
    async def get_crowdsource_verification(self, claim: Claim) -> CrowdsourceResult:
        """获取众包验证结果"""
        return await self.crowdsource_manager.get_community_consensus(claim)
```

### 2. 成本控制策略

#### 智能路由算法
```python
class DataSourceRouter:
    """根据查询类型和成本预算智能选择数据源"""
    
    def __init__(self, daily_budget: float = 10.0):  # 每日API调用预算
        self.daily_budget = daily_budget
        self.usage_tracker = UsageTracker()
    
    async def route_verification_request(self, claim: Claim) -> List[DataSource]:
        """选择最经济高效的数据源组合"""
        
        # 优先使用免费官方数据
        if claim.type == 'financial_statement':
            return [SECDataSource(), CompanyIRSource()]
        
        # 实时数据需求
        elif claim.requires_real_time:
            if self.usage_tracker.remaining_budget > 0.1:
                return [AlphaVantageAPI(), YahooFinanceAPI()]
            else:
                return [YahooFinanceAPI(), WebScrapingSource()]
        
        # 一般验证需求
        else:
            return [YahooFinanceAPI(), NewsScrapingSource(), CrowdsourceSource()]
```

#### 缓存优化策略
```python
class IntelligentCache:
    """智能缓存减少重复API调用"""
    
    def __init__(self):
        self.redis_client = Redis()
        self.cache_ttl = {
            'financial_statements': 86400,  # 24小时
            'stock_prices': 300,           # 5分钟
            'news_articles': 3600,         # 1小时
            'company_info': 86400 * 7      # 7天
        }
    
    async def get_or_fetch(self, key: str, fetcher: Callable, data_type: str):
        """缓存优先的数据获取"""
        cached_data = await self.redis_client.get(key)
        
        if cached_data:
            return json.loads(cached_data)
        
        # 缓存未命中，调用API
        fresh_data = await fetcher()
        ttl = self.cache_ttl.get(data_type, 3600)
        
        await self.redis_client.setex(
            key, ttl, json.dumps(fresh_data)
        )
        
        return fresh_data
```

---

## 商业模式设计

### 1. 分层服务模式

| 功能特性 | 免费版 | 专业版 (¥39/月) | 企业版 (¥199/月) |
|----------|--------|------------------|-------------------|
| **数据源** | 免费官方数据 | + 付费API配额 | + 实时高级数据源 |
| **验证速度** | 标准 (30秒) | 快速 (10秒) | 极速 (3秒) |
| **批量处理** | 5个/天 | 100个/天 | 无限制 |
| **历史数据** | 1个月 | 1年 | 永久保存 |
| **API访问** | ❌ | 基础API | 完整API |
| **团队协作** | ❌ | 5人团队 | 无限制团队 |
| **客服支持** | 社区 | 邮件支持 | 专属客服 |

### 2. 成本结构分析

#### 免费版成本 (用户基础)
- 服务器成本: ¥2/月/用户
- 免费API调用: ¥1/月/用户 
- 存储成本: ¥0.5/月/用户
- **总成本**: ¥3.5/月/用户

#### 付费版收入模型
- 专业版: ¥39/月 - ¥3.5成本 - ¥8 API成本 = **¥27.5利润**
- 企业版: ¥199/月 - ¥10成本 - ¥20 API成本 = **¥169利润**

### 3. 增长策略
- **免费增值模式**: 免费版培养用户习惯
- **口碑营销**: 准确的验证建立用户信任  
- **生态合作**: 与财经媒体、投资平台合作
- **企业客户**: 面向研究机构提供批量服务

---

## 风险评估与缓解策略

### 1. 技术风险

| 风险类型 | 风险等级 | 影响 | 缓解措施 |
|----------|----------|------|----------|
| **API限制** | 中 | 验证能力下降 | 多源备份，智能路由 |
| **数据准确性** | 高 | 误导用户决策 | 多源交叉验证，人工审核 |
| **爬虫限制** | 中 | 部分数据源失效 | 合规爬取，IP轮换 |
| **AI幻觉** | 高 | 生成错误信息 | 严格基于事实，人工验证 |

### 2. 法律合规风险

#### 数据使用合规
- **版权问题**: 只爬取公开数据，遵循 robots.txt
- **API服务条款**: 严格遵守各API提供商的使用条款
- **数据隐私**: 不存储用户敏感投资信息
- **免责声明**: 明确标注信息仅供参考，不构成投资建议

#### 投资建议合规
```markdown
⚠️ **重要声明**
本平台提供的事实核查服务仅用于信息验证，不构成任何形式的投资建议。
用户应独立判断投资决策，并承担相应风险。
```

### 3. 商业风险

- **竞争风险**: 大厂入局 → 差异化定位，专注事实核查细分领域
- **用户获取成本**: 获客成本高 → 口碑营销，社区运营
- **付费转化率**: 免费用户不愿付费 → 提升付费价值，限制免费功能

---

## 开发路线图

### Phase 1: MVP (4周)
- [x] **Week 1-2**: 基础架构搭建
  - LangGraph Agent 框架
  - 数据源接入 (SEC, Yahoo Finance)
  - 基础UI界面
  
- [ ] **Week 3-4**: 核心功能开发
  - 信息提取引擎
  - 简单验证流程
  - 结果展示页面

### Phase 2: 增强验证 (3周)  
- [ ] **Week 5-6**: 多源验证
  - Alpha Vantage API 接入
  - 网页爬取模块
  - 可信度评分算法

- [ ] **Week 7**: 用户体验优化
  - 实时验证反馈
  - 缓存优化
  - 移动端适配

### Phase 3: 社区功能 (2周)
- [ ] **Week 8**: 众包验证系统
  - 用户标注功能
  - 声誉系统
  - 专家认证

- [ ] **Week 9**: 协作功能
  - 团队共享
  - 历史记录
  - 批量处理

### Phase 4: 商业化 (2周)
- [ ] **Week 10-11**: 付费功能
  - 分层服务
  - 支付系统
  - 高级API

### Phase 5: 企业功能 (1周)  
- [ ] **Week 12**: 企业版
  - API接口
  - 白标解决方案
  - 私有部署

---

## 讨论问题

### 1. 产品定位
- 🤔 **问题**: 是否应该专注于事实核查，还是扩展到完整的投资研究工作流？
- 💡 **建议**: 先专注做好事实核查，建立用户信任后再扩展

### 2. 数据源优先级  
- 🤔 **问题**: 在成本和准确性之间如何平衡？免费数据源的可靠性如何保证？
- 💡 **建议**: 建立数据质量监控体系，定期人工抽检

### 3. 商业模式
- 🤔 **问题**: ¥39/月的定价是否合理？企业客户的获客策略是什么？
- 💡 **建议**: A/B测试不同价格点，先服务个人用户建立口碑

### 4. 技术实现
- 🤔 **问题**: 如何处理中文财经信息的验证？是否需要支持多语言？
- 💡 **建议**: 先专注英文市场，中文市场数据源更复杂

### 5. 竞争策略
- 🤔 **问题**: 面对彭博终端等专业工具的竞争，如何差异化？
- 💡 **建议**: 定位普通投资者市场，强调易用性和成本优势

---

## 下一步行动

1. **技术验证** (本周)
   - [ ] 搭建基础 LangGraph 工作流
   - [ ] 测试 SEC API 和 Yahoo Finance 数据获取
   - [ ] 验证信息提取算法准确性

2. **用户调研** (下周)
   - [ ] 访谈10位目标用户了解需求痛点
   - [ ] 分析竞品功能和定价策略  
   - [ ] 确定MVP功能优先级

3. **商务拓展** (2周内)
   - [ ] 联系免费数据源提供商确认使用条款
   - [ ] 评估付费数据源的成本和价值
   - [ ] 寻找潜在合作伙伴

**期待大家的反馈和建议！** 🚀