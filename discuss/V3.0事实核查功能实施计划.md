# V3.0 事实核查功能实施计划

> **文档类型**: 技术实施计划  
> **创建时间**: 2025-07-22  
> **状态**: 待讨论  
> **实施阶段**: Phase 0 MVP  
> **预期工期**: 6 周

## 项目背景与目标

### 战略定位

基于产品演进路线图，我们将以**事实核查功能**作为 V3.0 多 Agent 协作架构的**技术验证项目**。这不仅是一个有实际价值的功能，更是我们通往"AI 投研天团"愿景的关键一步。

### 核心目标

1. **技术验证**: 验证 LangGraph 多 Agent 协作架构的可行性
2. **用户价值**: 提供专业级的投资信息事实核查服务
3. **架构奠基**: 为后续完整投研功能建立可扩展的技术基础
4. **团队能力**: 建立团队的多 Agent 系统开发和调试能力

---

## 技术可行性分析

### ✅ 高可行性评估

#### 架构设计合理性
```mermaid
graph TB
    subgraph "前端层"
        UI[用户界面] --> API[API Client]
    end
    
    subgraph "后端 Agent 系统"
        O[🎯 项目总监<br/>Orchestrator] --> CE[📝 声明提取器<br/>Claim Extractor]
        O --> VS[👥 验证专家团队]
        O --> DM[🔄 辩论主持人<br/>Debate Moderator]
        O --> SA[📄 报告生成器<br/>Synthesis Agent]
        
        VS --> FV[💰 财务验证员]
        VS --> CV[🏢 公司验证员]  
        VS --> NV[🌐 新闻验证员]
    end
    
    subgraph "数据源层"
        SEC[SEC EDGAR API]
        FMP[Financial APIs]
        NEWS[News APIs]
        WEB[Web Search]
    end
    
    API --> O
    FV --> SEC
    FV --> FMP
    CV --> WEB
    NV --> NEWS
```

#### 技术选型评估

| 技术组件 | 选型方案 | 可行性 | 风险评估 |
|----------|----------|--------|----------|
| **Agent 框架** | LangGraph | ✅ 高 | 学习成本中等，但架构清晰 |
| **LLM 模型** | GPT-4o/Claude-3.5 | ✅ 高 | 成本可控，API 稳定 |
| **数据源集成** | SEC API + 免费财经 API | ✅ 高 | 免费额度充足，备选方案多 |
| **缓存机制** | Redis | ✅ 高 | 成熟技术，降低 API 成本 |
| **实时通信** | SSE | ✅ 高 | 现有架构已支持 |

#### 成本控制策略
```python
# 智能成本控制
class CostControlManager:
    daily_budget = 50.0  # 每日API调用预算 $50
    
    cache_strategy = {
        "financial_data": "24h",      # 财务数据缓存24小时
        "company_info": "7d",         # 公司信息缓存7天  
        "news_articles": "1h",        # 新闻文章缓存1小时
    }
    
    fallback_strategy = {
        "primary": "paid_api",        # 优先使用付费API
        "secondary": "free_api",      # 降级到免费API
        "tertiary": "web_scraping"    # 最后使用网页爬取
    }
```

---

## 前后端对接方案

### 现有架构评估

#### ✅ 前端架构兼容性
```typescript
// 现有技术栈完全支持
- Next.js 15 + React 19 + TypeScript ✅
- Tailwind CSS + Headless UI ✅
- React Query + Zustand ✅  
- SSE 实时通信 ✅
- 任务状态管理 ✅
```

#### ✅ API 接口兼容性
```typescript
// 现有 API 结构可直接扩展
interface FactCheckTask extends AnalysisTask {
  analysis_type: 'fact_check';
  input_data: {
    text_content: string;
    source_url?: string;
    priority_level?: 'standard' | 'urgent';
  };
  result?: FactCheckResult;
}

interface FactCheckResult extends AnalysisResult {
  verified_claims: VerifiedClaim[];
  conflicted_claims: ConflictedClaim[];
  unverified_claims: UnverifiedClaim[];
  overall_credibility: number; // 0-100
  processing_time: number;
  agents_involved: string[];
}
```

### 新增 UI 组件设计

#### 1. Agent 协作看板
```typescript
interface AgentBoardProps {
  agents: AgentInfo[];
  currentTask?: string;
  collaborationMode: 'parallel' | 'sequential' | 'debate';
}

const AgentBoard: React.FC<AgentBoardProps> = ({ agents, currentTask, collaborationMode }) => {
  return (
    <div className="grid grid-cols-3 gap-4">
      {agents.map(agent => (
        <AgentCard 
          key={agent.id}
          agent={agent}
          isActive={agent.currentTask === currentTask}
          progress={agent.progress}
        />
      ))}
    </div>
  );
};
```

#### 2. 事实核查结果展示
```typescript
interface FactCheckReportProps {
  result: FactCheckResult;
  showDetails: boolean;
  onClaimClick: (claim: VerifiedClaim) => void;
}

const FactCheckReport: React.FC<FactCheckReportProps> = ({ result, showDetails, onClaimClick }) => {
  return (
    <div className="space-y-6">
      {/* 整体可信度评分 */}
      <CredibilityScore score={result.overall_credibility} />
      
      {/* 分类结果展示 */}
      <ClaimCategories 
        verified={result.verified_claims}
        conflicted={result.conflicted_claims}
        unverified={result.unverified_claims}
        onClaimClick={onClaimClick}
      />
      
      {/* Agent 协作过程 */}
      {showDetails && (
        <AgentCollaborationLog agents={result.agents_involved} />
      )}
    </div>
  );
};
```

#### 3. 实时 Agent 状态监控
```typescript
interface AgentStatusMonitorProps {
  taskId: string;
  onAgentUpdate: (agent: AgentStatus) => void;
}

const AgentStatusMonitor: React.FC<AgentStatusMonitorProps> = ({ taskId, onAgentUpdate }) => {
  const [agents, setAgents] = useState<AgentStatus[]>([]);
  
  useEffect(() => {
    const sseClient = createSSEClient(`/api/tasks/${taskId}/stream`);
    
    sseClient.on('agent_started', (event) => {
      // 更新 Agent 状态
      updateAgentStatus(event.data.agent_id, 'processing');
    });
    
    sseClient.on('agent_completed', (event) => {
      updateAgentStatus(event.data.agent_id, 'completed');
    });
    
    sseClient.on('debate_started', (event) => {
      // 显示辩论过程
      setDebateMode(true);
    });
    
    sseClient.connect();
    return () => sseClient.disconnect();
  }, [taskId]);
  
  return (
    <div className="space-y-4">
      {agents.map(agent => (
        <AgentStatusCard key={agent.id} agent={agent} />
      ))}
    </div>
  );
};
```

---

## 实施计划与里程碑

### 总体时间安排：6 周

```mermaid
gantt
    title V3.0 事实核查功能开发计划
    dateFormat  YYYY-MM-DD
    
    section Phase 1: 架构搭建 (2周)
    LangGraph框架搭建    :2025-07-23, 1w
    基础Agent开发        :2025-07-30, 1w
    
    section Phase 2: 核心功能 (2周)  
    验证专家团队         :2025-08-06, 1w
    辩论协作机制         :2025-08-13, 1w
    
    section Phase 3: 前端集成 (1.5周)
    UI组件开发          :2025-08-20, 1w
    前后端联调          :2025-08-27, 0.5w
    
    section Phase 4: 测试发布 (0.5周)
    系统测试            :2025-08-30, 0.5w
```

### 详细实施计划

#### Week 1-2: 后端架构搭建

**Week 1: LangGraph 多 Agent 框架**
```python
# 核心开发任务
tasks = [
    "搭建 LangGraph StateGraph 基础框架",
    "实现 Orchestrator 项目总监 Agent",
    "开发 Claim Extractor 声明提取 Agent", 
    "集成基础数据源 (SEC API, Alpha Vantage)",
    "实现 Redis 缓存机制"
]

# 成功标准
success_criteria = [
    "能够接收文本输入并提取结构化声明",
    "Orchestrator 能够正确分配任务给下游 Agent",
    "数据源调用正常，缓存机制生效"
]
```

**Week 2: 基础验证专家开发**
```python
# 开发 2-3 个核心验证专家
agents_to_develop = [
    "FinancialVerifier: 财务数据验证专家",
    "CorporateVerifier: 公司声明验证专家", 
    "NewsVerifier: 新闻传闻验证专家"
]

# 实现基础协作机制
collaboration_features = [
    "并行任务执行",
    "结果聚合和冲突检测",
    "简单的报告生成"
]
```

#### Week 3-4: 协作机制与辩论

**Week 3: 辩论主持人开发**
```python
# DebateModerator 核心功能
debate_features = [
    "冲突检测：识别验证结果中的矛盾",
    "辩论组织：让持不同观点的 Agent 进行对话",
    "共识形成：引导达成最终一致意见或明确分歧"
]

# 测试场景
test_scenarios = [
    "财务数据不一致场景",
    "新闻来源可信度争议",
    "时效性信息冲突"
]
```

**Week 4: 质量控制与报告生成**
```python
# 完善报告生成和质量控制
quality_control = [
    "事实核查结果标准化格式",
    "可信度评分算法",
    "数据源追溯和引用",
    "报告质量自检机制"
]
```

#### Week 5: 前端 UI 开发

**新增组件开发优先级**
```typescript
// P0: 核心功能组件
priority_0 = [
    "FactCheckSubmission: 提交界面",
    "AgentProgressBoard: Agent 进度看板", 
    "FactCheckReport: 结果报告展示"
]

// P1: 增强体验组件  
priority_1 = [
    "AgentDebateViewer: 辩论过程展示",
    "CredibilityMeter: 可信度评分可视化",
    "SourceReferences: 数据源引用展示"
]
```

#### Week 6: 集成测试与发布

**测试计划**
```typescript
// 端到端测试场景
e2e_test_cases = [
    {
        name: "标准财务数据验证",
        input: "苹果公司Q3营收达到943亿美元",
        expected: "验证通过，来源：SEC 10-Q"
    },
    {
        name: "冲突信息处理", 
        input: "特斯拉CEO宣布辞职（虚假信息）",
        expected: "验证失败，无官方消息证实"
    },
    {
        name: "复杂多声明处理",
        input: "包含多个可验证声明的投资报告",
        expected: "分类处理，部分验证通过"
    }
]
```

---

## 技术实现细节

### Agent 系统架构

#### 项目总监 (Orchestrator)
```python
class FactCheckOrchestrator:
    """事实核查项目总监"""
    
    def __init__(self):
        self.state_graph = StateGraph(FactCheckState)
        self.agents = {
            'claim_extractor': ClaimExtractorAgent(),
            'financial_verifier': FinancialVerifierAgent(),
            'corporate_verifier': CorporateVerifierAgent(),
            'news_verifier': NewsVerifierAgent(),
            'debate_moderator': DebateModeratorAgent(),
            'report_generator': ReportGeneratorAgent()
        }
    
    async def orchestrate_fact_check(self, input_text: str) -> FactCheckReport:
        """编排整个事实核查流程"""
        
        # 初始化状态
        initial_state = FactCheckState(
            input_text=input_text,
            extracted_claims=[],
            verification_results={},
            conflicts=[],
            final_report=None
        )
        
        # 执行工作流
        final_state = await self.state_graph.ainvoke(initial_state)
        return final_state.final_report
    
    def build_workflow(self):
        """构建事实核查工作流"""
        
        # 添加节点
        self.state_graph.add_node("extract_claims", self._extract_claims)
        self.state_graph.add_node("verify_claims", self._verify_claims)
        self.state_graph.add_node("resolve_conflicts", self._resolve_conflicts)
        self.state_graph.add_node("generate_report", self._generate_report)
        
        # 添加边
        self.state_graph.add_edge("extract_claims", "verify_claims")
        self.state_graph.add_conditional_edges(
            "verify_claims",
            self._check_for_conflicts,
            {
                "has_conflicts": "resolve_conflicts",
                "no_conflicts": "generate_report"
            }
        )
        self.state_graph.add_edge("resolve_conflicts", "generate_report")
        
        # 设置入口
        self.state_graph.set_entry_point("extract_claims")
```

#### 验证专家示例
```python
class FinancialVerifierAgent:
    """财务数据验证专家"""
    
    def __init__(self):
        self.data_sources = {
            'sec': SECDataSource(),
            'alpha_vantage': AlphaVantageSource(),
            'yahoo_finance': YahooFinanceSource()
        }
        self.cache = RedisCache()
    
    async def verify_financial_claim(self, claim: FinancialClaim) -> VerificationResult:
        """验证财务相关声明"""
        
        # 尝试从缓存获取
        cache_key = f"financial:{claim.company}:{claim.metric}:{claim.period}"
        cached_result = await self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        verification_results = []
        
        # 多源验证
        for source_name, source in self.data_sources.items():
            try:
                result = await source.verify_claim(claim)
                if result:
                    verification_results.append(result)
            except Exception as e:
                logger.warning(f"Source {source_name} failed: {e}")
        
        # 结果聚合
        final_result = self._aggregate_results(verification_results, claim)
        
        # 缓存结果
        await self.cache.set(cache_key, final_result, expire=86400)  # 24小时
        
        return final_result
    
    def _aggregate_results(self, results: List[SourceResult], claim: FinancialClaim) -> VerificationResult:
        """聚合多个数据源的验证结果"""
        
        if not results:
            return VerificationResult(
                status="unverified",
                confidence=0.0,
                message="无法找到可靠数据源验证此声明"
            )
        
        # 官方数据源优先
        official_results = [r for r in results if r.source_type == "official"]
        if official_results:
            best_result = max(official_results, key=lambda x: x.reliability_score)
            return VerificationResult(
                status="verified" if best_result.matches_claim else "contradicted",
                confidence=best_result.reliability_score,
                source=best_result.source_name,
                evidence=best_result.data,
                message=f"根据{best_result.source_name}数据验证"
            )
        
        # 加权平均其他数据源
        weighted_confidence = sum(r.reliability_score * r.confidence for r in results) / sum(r.reliability_score for r in results)
        
        return VerificationResult(
            status="partially_verified",
            confidence=weighted_confidence,
            sources=[r.source_name for r in results],
            message=f"基于{len(results)}个数据源的综合验证"
        )
```

### 前端状态管理

#### Zustand Store 扩展
```typescript
interface FactCheckStore {
  // 状态
  currentTask: FactCheckTask | null;
  agents: AgentStatus[];
  realTimeUpdates: SSEEvent[];
  
  // 操作
  submitFactCheck: (text: string) => Promise<string>;
  subscribeToUpdates: (taskId: string) => void;
  updateAgentStatus: (agentId: string, status: AgentStatus) => void;
  clearSession: () => void;
}

const useFactCheckStore = create<FactCheckStore>((set, get) => ({
  currentTask: null,
  agents: [],
  realTimeUpdates: [],
  
  submitFactCheck: async (text: string) => {
    const apiClient = getApiClient();
    const response = await apiClient.createTask({
      title: "事实核查任务",
      analysis_type: AnalysisType.FACT_CHECK,
      input_data: { text_content: text }
    });
    
    if (response.success && response.data) {
      set({ currentTask: response.data });
      get().subscribeToUpdates(response.data.id);
      return response.data.id;
    }
    
    throw new Error(response.error || "创建任务失败");
  },
  
  subscribeToUpdates: (taskId: string) => {
    const sseClient = createSSEClient(`/api/tasks/${taskId}/stream`);
    
    sseClient.on('agent_update', (event) => {
      set(state => ({
        agents: state.agents.map(agent => 
          agent.id === event.data.agent_id 
            ? { ...agent, ...event.data }
            : agent
        ),
        realTimeUpdates: [...state.realTimeUpdates, event]
      }));
    });
    
    sseClient.connect();
  }
}));
```

---

## 风险评估与缓解策略

### 技术风险

| 风险类型 | 风险等级 | 潜在影响 | 缓解策略 |
|----------|----------|----------|----------|
| **LangGraph 学习曲线** | 中 | 开发延期 | 提前技术预研，准备备选方案 |
| **Agent 协作复杂度** | 高 | 系统不稳定 | 分阶段实现，先简后繁 |
| **API 成本控制** | 中 | 运营成本超预算 | 智能缓存 + 降级策略 |
| **实时性能问题** | 中 | 用户体验差 | 异步处理 + 进度反馈 |

### 产品风险

| 风险类型 | 风险等级 | 潜在影响 | 缓解策略 |
|----------|----------|----------|----------|
| **用户接受度** | 中 | 产品推广困难 | 用户测试 + 功能简化 |
| **准确性质疑** | 高 | 产品信任度低 | 多源验证 + 透明度机制 |
| **竞争对手跟进** | 低 | 差异化优势丧失 | 持续技术创新 |

### 缓解措施

#### 技术风险缓解
```python
# 1. 渐进式复杂度增加
phase_0_agents = ["ClaimExtractor", "BasicVerifier", "ReportGenerator"]
phase_1_agents = phase_0_agents + ["DebateModerator", "ConflictResolver"]
phase_2_agents = phase_1_agents + ["AdvancedAnalyzer", "QualityController"]

# 2. 强健的错误处理
class RobustAgentExecutor:
    async def execute_with_fallback(self, agent: Agent, task: Task) -> Result:
        try:
            return await agent.execute(task)
        except AgentError as e:
            logger.warning(f"Agent {agent.name} failed, trying fallback")
            return await self.fallback_strategy(task, e)
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return ErrorResult(f"Agent执行失败: {str(e)}")

# 3. 成本监控和控制
class CostMonitor:
    def __init__(self, daily_limit: float = 50.0):
        self.daily_limit = daily_limit
        self.current_usage = 0.0
    
    async def check_budget(self, estimated_cost: float) -> bool:
        if self.current_usage + estimated_cost > self.daily_limit:
            await self.switch_to_free_sources()
            return False
        return True
```

---

## 成功标准与验收条件

### 技术验收标准

#### 功能性验收
- [ ] **声明提取准确率** ≥ 85%
- [ ] **财务数据验证准确率** ≥ 95%
- [ ] **新闻信息验证召回率** ≥ 80%
- [ ] **系统响应时间** ≤ 30秒（标准文档）
- [ ] **Agent 协作成功率** ≥ 90%

#### 性能验收
- [ ] **并发处理能力** ≥ 10个同时任务
- [ ] **缓存命中率** ≥ 60%
- [ ] **API 调用成本** ≤ $2/任务
- [ ] **前端加载时间** ≤ 3秒
- [ ] **SSE 连接稳定性** ≥ 99%

### 用户体验验收

#### 可用性标准
- [ ] **新用户完成率** ≥ 80%（首次提交任务）
- [ ] **结果理解度** ≥ 85%（用户能理解验证结果）
- [ ] **界面响应性** - 所有操作 ≤ 2秒反馈
- [ ] **错误处理** - 友好的错误提示和恢复引导

#### 价值验证
- [ ] **用户满意度** ≥ 4.0/5.0
- [ ] **功能完成率** ≥ 90%（成功完成事实核查）
- [ ] **重复使用率** ≥ 60%（7天内再次使用）

---

## 关键讨论问题

### 1. 技术架构决策 🤔

**问题集**：
- LangGraph vs 自研编排框架，哪个更适合我们的长期规划？
- Agent 间通信是否需要引入消息队列（如 Redis Pub/Sub）？
- 如何平衡 Agent 自主性和系统可控性？

**讨论要点**：
- 技术栈的学习成本和维护成本
- 系统的可调试性和可监控性
- 未来扩展到完整投研功能的架构兼容性

### 2. 用户体验设计 🎨

**问题集**：
- Agent 协作过程对用户是否应该完全透明？
- 如何在专业性和易用性之间找到平衡？
- 事实核查失败或部分失败时，如何给用户提供有价值的反馈？

**讨论要点**：
- 用户对"AI 团队"概念的接受程度
- 复杂流程的可视化设计原则
- 错误状态和异常情况的用户引导

### 3. 产品价值定位 💡

**问题集**：
- 事实核查功能的独立价值是否足以支撑用户付费？
- 如何向用户证明我们比简单的"搜索验证"更有价值？
- 是否需要在 MVP 中体现"团队协作"的独特性？

**讨论要点**：
- 目标用户群体的精准定义
- 与现有竞品的差异化策略
- 免费试用和付费转化的节点设计

### 4. 成本与收益平衡 💰

**问题集**：
- $2/任务的成本控制目标是否现实？
- 如何在成本控制和准确性之间找到最优平衡？
- 规模化后的成本结构如何优化？

**讨论要点**：
- API 调用成本的精确预估和监控
- 缓存策略的优化空间
- 用户付费意愿和定价策略

### 5. 团队协作与资源配置 👥

**问题集**：
- 6 周的开发周期是否过于紧张？
- 前后端开发如何有效协作和联调？
- 如何确保代码质量在快速迭代中不被牺牲？

**讨论要点**：
- 人力资源分配和技能匹配
- 开发流程和质量保证机制
- 项目风险的提前识别和应对

---

## 下一步行动

### 立即行动项（本周内）

1. **技术预研验证**
   - [ ] 搭建 LangGraph 最小可行原型
   - [ ] 测试 OpenAI/Anthropic API 调用和成本
   - [ ] 验证 SEC API 和免费财经数据源可用性

2. **团队准备**
   - [ ] 确认开发团队成员和技能分工
   - [ ] 制定详细的开发规范和代码标准
   - [ ] 准备开发环境和工具链

3. **产品设计细化**
   - [ ] 完善用户界面原型设计
   - [ ] 制定详细的用户测试计划
   - [ ] 确定核心功能的优先级排序

### 关键决策点（需要团队讨论）

- **Go/No-Go 决策**: 基于技术预研结果，确定是否启动 6 周开发计划
- **资源分配决策**: 确认团队成员分工和时间投入
- **产品范围决策**: 最终确定 MVP 的功能边界和验收标准

**准备好开始这个激动人心的技术挑战了吗？让我们用事实核查功能为 V3.0 架构奠定坚实的第一块基石！** 🚀