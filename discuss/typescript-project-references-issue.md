# TypeScript 项目引用问题求助

## 问题描述

在实施插件化架构过程中，遇到了 TypeScript 项目引用的编译问题。具体表现为：

- `libs/shared-types` 可以正常编译
- `libs/api-client` 无法找到 `@yai-investor-insight/shared-types` 模块，编译失败

## 错误信息

```
libs/api-client/src/index.ts:9:8 - error TS2307: Cannot find module '@yai-investor-insight/shared-types' or its corresponding type declarations.

9 } from '@yai-investor-insight/shared-types';
       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
```

## 当前配置

### 1. 根目录 tsconfig.base.json
```json
{
  "compileOnSave": false,
  "compilerOptions": {
    "rootDir": ".",
    "sourceMap": true,
    "declaration": false,
    "moduleResolution": "node",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "importHelpers": true,
    "target": "es2015",
    "module": "esnext",
    "lib": ["es2020", "dom"],
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "baseUrl": ".",
    "paths": {
      "@yai-investor-insight/api-client": ["libs/api-client/src/index.ts"],
      "@yai-investor-insight/shared-types": ["libs/shared-types/src/index.ts"],
      "@yai/demo-feature-fe": ["libs/demo-feature-fe/src/index.ts"],
      "@yai/shared-fe-kit": ["libs/shared-fe-kit/src/index.ts"]
    }
  },
  "exclude": ["node_modules", "tmp"]
}
```

### 2. libs/shared-types/tsconfig.json
```json
{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "composite": true,
    "incremental": true,
    "tsBuildInfoFile": "./dist/.tsbuildinfo"
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.spec.ts",
    "**/*.test.ts"
  ]
}
```

### 3. libs/api-client/tsconfig.json
```json
{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.spec.ts",
    "**/*.test.ts"
  ]
}
```

### 4. 根目录 tsconfig.json (新增)
```json
{
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    "composite": true
  },
  "include": [],
  "references": [
    {
      "path": "./libs/shared-types"
    },
    {
      "path": "./libs/api-client"
    },
    {
      "path": "./libs/shared-fe-kit"
    },
    {
      "path": "./libs/demo-feature-fe"
    }
  ]
}
```

## 已尝试的解决方案

1. **添加项目引用**: 在 `api-client/tsconfig.json` 中添加了 `references` 配置
2. **创建根级 tsconfig.json**: 添加了协调所有项目的根配置
3. **使用相对路径导入**: 尝试 `../shared-types/src/index` 导入方式
4. **修改模块系统**: 曾尝试改为 CommonJS (已撤销)

## 环境信息

- **项目**: Nx Monorepo
- **构建工具**: `@nx/js:tsc`
- **TypeScript**: 通过 Nx 管理
- **模块系统**: ES Modules
- **包管理器**: pnpm

## 构建命令

```bash
# shared-types 构建成功
pnpm nx build shared-types

# api-client 构建失败
pnpm nx build api-client
```

## 项目结构

```
yai-investor-insight/
├── tsconfig.base.json
├── tsconfig.json
├── libs/
│   ├── shared-types/
│   │   ├── src/index.ts
│   │   ├── tsconfig.json
│   │   └── dist/  (构建成功)
│   └── api-client/
│       ├── src/index.ts  (导入 shared-types)
│       ├── tsconfig.json
│       └── project.json  (dependsOn: shared-types:build)
```

## 疑问

1. 在 Nx Monorepo 环境下，TypeScript 项目引用的最佳实践是什么？
2. 路径映射和项目引用之间是否存在冲突？
3. 是否需要特殊的 Nx 配置来支持 TypeScript 复合项目？
4. `@nx/js:tsc` 构建器是否完全支持 TypeScript 项目引用特性？

## 期望结果

`api-client` 能够正确解析和导入 `@yai-investor-insight/shared-types` 中的类型定义，并成功编译。

---

**请技术顾问帮忙诊断这个问题，提供解决方案。谢谢！**