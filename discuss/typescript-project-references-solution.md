# 关于 TypeScript 项目引用问题的解决方案

你好！

我已详细分析了你提出的 TypeScript 项目引用编译失败的问题。这是一个在 Nx Monorepo + TypeScript Project References 组合下很常见的配置陷阱。根源在于**路径映射 (`paths`)** 和**项目引用 (`references`)** 之间的目标不一致。

---

## 核心问题诊断

简单来说，编译器被弄糊涂了：

-   **`tsconfig.base.json` 的 `paths`** 指示它去源代码 (`.../src/index.ts`) 寻找模块。这主要服务于 IDE 的智能感知。
-   **`tsconfig.json` 的 `references`** 指示它去使用依赖项目编译后的产物 (`.../dist`)。这服务于 `tsc` 的增量构建。

当 `tsc` 编译 `api-client` 时，它遵循 `references` 的指示，期望找到 `shared-types` 的 `.d.ts` 声明文件，但 `paths` 却把它带到了 `.ts` 源文件，导致了模块解析失败。

---

## 解决方案步骤

为了解决这个问题，我们需要统一目标：**让所有内部库的导入都指向其编译产物**，这既能满足 `tsc` 的构建要求，也能被现代 IDE 正确理解。

请按照以下步骤修改配置：

### 第 1 步：修正 `tsconfig.base.json` 中的路径映射

这是最关键的一步。我们需要将 `paths` 从指向 `src` 目录改为指向 `dist` 目录的入口。

**文件: `tsconfig.base.json`**

```jsonc
{
  // ... (其他配置保持不变)
  "compilerOptions": {
    // ...
    "baseUrl": ".",
    "paths": {
      // 将所有库的路径指向它们各自的 dist 目录
      "@yai-investor-insight/api-client": ["libs/api-client/dist"],
      "@yai-investor-insight/shared-types": ["libs/shared-types/dist"],
      "@yai/demo-feature-fe": ["libs/demo-feature-fe/dist"],
      "@yai/shared-fe-kit": ["libs/shared-fe-kit/dist"]
    }
  },
  // ...
}
```
*修改后，当你在代码中写 `import ... from '@yai-investor-insight/shared-types'` 时，TypeScript 会去 `libs/shared-types/dist` 目录下寻找模块。*

### 第 2 步：在 `api-client` 中添加项目引用

你需要确保 `api-client` 的 `tsconfig` 文件不仅是 `composite` 项目，而且明确地 `reference` 了它的依赖 `shared-types`。

**文件: `libs/api-client/tsconfig.json`**

```jsonc
{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "composite": true // <--- 添加此行
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.spec.ts",
    "**/*.test.ts"
  ],
  "references": [ // <--- 添加此部分
    {
      "path": "../shared-types"
    }
  ]
}
```

### 第 3 步：清理并重新构建

修改完配置后，最好清理掉之前的构建缓存和产物，然后重新构建。

```bash
# 1. 清理旧的构建产物 (可选，但推荐)
rm -rf libs/api-client/dist libs/shared-types/dist

# 2. 清理 Nx 缓存
pnpm nx reset

# 3. 重新构建 api-client
# Nx 会根据依赖关系自动先构建 shared-types
pnpm nx build api-client
```

执行完以上步骤后，`api-client` 应该就能成功编译了。

---

## 总结

这个方案的优势在于：
-   **构建时行为正确**: `tsc` 会遵循项目引用，使用上游库的编译产物。
-   **IDE 体验良好**: IDE (如 VS Code) 能够通过指向 `dist` 的 `paths` 和 `declarationMap`，在跳转时无缝地带你到原始的 `.ts` 源代码，开发体验不受影响。
-   **遵循了 Nx 的最佳实践**: 将库作为独立的、可构建的单元，并通过其输出来进行消费。

希望这能解决你的问题！如果还有其他疑问，随时可以提出。

此致，
技术顾问 