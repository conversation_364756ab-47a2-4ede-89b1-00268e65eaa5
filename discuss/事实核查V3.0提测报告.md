# 事实核查功能 V3.0 提测报告

## 版本信息
- **版本号**: V3.0
- **提测日期**: 2025-07-23
- **开发完成度**: 85%
- **提测环境**: Development

---

## 功能概述

事实核查V3.0是一个基于AI的投资研究文本验证系统，能够自动提取文本中的可验证声明并进行事实核查。

### 核心特性
- **智能声明提取**: 使用OpenRouter Claude 4自动识别财务数据、公司声明、市场预测等
- **多Agent验证架构**: 基于LangGraph的多Agent协作验证系统
- **实时进度跟踪**: 通过AGUI CustomEvent提供实时状态更新
- **优雅降级机制**: 在没有API密钥时使用fallback方法保证基础功能
- **完整前端界面**: React组件支持文本输入、进度查看、结果展示

---

## 系统架构

### 后端架构 (FastAPI + LangGraph)
```
事实核查工作流
├── 声明提取Agent (ClaimExtractorAgent)
├── 财务验证Agent (FinancialVerifierAgent) 
├── 冲突检查节点
└── 报告生成节点
```

### 前端架构 (Next.js 15 + Zustand)
```
页面组件
├── 文本输入区域 (TextInputArea)
├── 处理状态显示 (ProcessingStatus)
├── 结果展示 (ResultsDisplay)
└── 辩论查看器 (DebateViewer)
```

---

## 测试环境配置

### 服务启动
```bash
# 后端服务 (端口8000)
cd apps/api-server
source .venv/bin/activate
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# 前端服务 (端口4200)  
cd yai-investor-insight
pnpm nx dev web-app
```

### 环境变量 (可选)
```bash
# 完整功能需要 (推荐测试)
OPENROUTER_API_KEY=your_key_here

# 无API密钥时使用fallback模式
# 系统仍可正常工作，使用正则表达式提取
```

---

## 测试范围与用例

### 1. 后端API测试

#### 1.1 健康检查
```bash
GET http://localhost:8000/api/v1/health/
期望: {"status":"healthy","version":"1.0.0"}
```

#### 1.2 事实核查API测试
```bash
# 测试端点 (内置测试文本)
POST http://localhost:8000/api/v1/fact-check/test

# 自定义文本测试
POST http://localhost:8000/api/v1/fact-check/start
Content-Type: application/json
{
  "text": "苹果公司2024年Q4营收达到946亿美元，同比增长6%。",
  "options": {
    "enable_financial_verification": true,
    "enable_corporate_verification": true,
    "enable_news_verification": true,
    "deep_research_mode": false
  }
}
```

### 2. 前端功能测试

#### 2.1 页面访问
- 访问 `http://localhost:4200/fact-check`
- 检查页面正常加载，无控制台错误

#### 2.2 用户界面测试
- **文本输入**: 输入测试文本，检查字符计数和成本估算
- **配置选项**: 切换验证选项开关
- **启动按钮**: 点击开始事实核查，检查状态变化
- **进度显示**: 查看Agent工作进度和状态更新
- **结果查看**: 展开声明卡片查看详细信息

### 3. 集成测试

#### 3.1 完整工作流测试
1. 前端输入文本 → 后端接收请求
2. 声明提取 → 财务验证 → 冲突检查 → 报告生成
3. 实时事件通信 → 前端状态更新
4. 最终结果展示

#### 3.2 异常场景测试
- **网络断开**: 检查错误处理和用户提示
- **API超时**: 验证超时处理机制
- **无效输入**: 测试输入验证和错误反馈
- **无API密钥**: 验证fallback机制工作正常

---

## 测试用例

### 测试用例1: 财务数据验证
**输入文本**:
```
苹果公司2024年第四季度营收达到946亿美元，同比增长6%。公司净利润为234亿美元，每股收益为1.52美元。
```

**期望结果**:
- 识别3个财务声明
- 财务数据被正确分类为`financial_data`类型
- 有API密钥时: 进行AI验证
- 无API密钥时: 标记为`unverified`状态

### 测试用例2: 混合内容验证
**输入文本**:
```
特斯拉宣布将在2025年推出新款Model S，预计起售价为8万美元。公司CEO埃隆·马斯克表示，新车型将配备更先进的自动驾驶功能。分析师预测特斯拉股价将上涨20%。
```

**期望结果**:
- 识别产品声明、财务预测等不同类型声明
- 正确分类为`corporate_statement`、`financial_data`、`market_prediction`
- 提供相应的置信度评分

### 测试用例3: 错误处理
**输入文本**: 空字符串或超长文本

**期望结果**:
- 显示适当的错误提示
- 不会导致系统崩溃
- 用户界面保持响应

---

## 已知问题与限制

### 当前限制
1. **数据源集成**: 暂未接入真实财务数据API，使用模拟数据
2. **辩论功能**: 冲突检测逻辑较简单，辩论Agent未完全实现
3. **历史记录**: 任务历史和状态查询功能待开发
4. **取消机制**: 任务取消功能未完全实现

### 技术债务
1. **性能优化**: LLM调用可以增加缓存机制
2. **错误处理**: 部分边界场景的错误处理可以更完善
3. **日志完善**: 可以增加更详细的业务日志

---

## 测试重点

### 优先级P0 (阻塞性)
- [ ] 后端服务正常启动
- [ ] 前端页面正常访问
- [ ] 基本的事实核查流程能够完成
- [ ] API接口返回正确的数据格式

### 优先级P1 (重要)
- [ ] 不同类型文本的声明提取准确性
- [ ] 用户界面交互流畅性
- [ ] 错误场景的处理和提示
- [ ] Fallback机制工作正常

### 优先级P2 (一般)
- [ ] 界面美观度和用户体验
- [ ] 性能表现和响应时间
- [ ] 边界场景处理

---

## 测试数据

### 标准测试文本
1. **财务数据密集型**:
   ```
   微软公司发布2024年Q4财报，营收达到649亿美元，同比增长15%。云服务业务Azure营收增长29%，达到259亿美元。公司宣布季度股息每股0.75美元。
   ```

2. **公司声明类型**:
   ```
   谷歌宣布将在2025年初推出新一代Pixel手机，搭载自研Tensor G4芯片。公司计划投资100亿美元用于AI基础设施建设，预计将创造5000个新工作岗位。
   ```

3. **混合复杂文本**:
   ```
   据彭博社报道，亚马逊正考虑收购某家AI初创公司，交易金额可能达到50亿美元。该交易预计将在今年年底完成。同时，亚马逊AWS部门Q3营收增长12%，达到234亿美元。分析师维持对亚马逊股票的"买入"评级，目标价位180美元。
   ```

---

## 提测清单

### 开发自测完成项
- [x] 单元测试通过
- [x] 集成测试通过  
- [x] 代码review完成
- [x] 文档更新完成
- [x] 环境部署验证
- [x] 基本功能测试
- [x] 异常场景测试
- [x] 性能基准测试

### QA团队测试项
- [ ] 功能测试
- [ ] 界面测试
- [ ] 兼容性测试
- [ ] 性能测试
- [ ] 安全测试
- [ ] 可用性测试

---

## 联系信息

**开发负责人**: AI Assistant  
**测试环境**: Development  
**提测分支**: master  
**文档位置**: `/discuss/事实核查V3.0提测报告.md`

---

## 附录

### API文档
详细的API接口文档请参考: `http://localhost:8000/docs`

### 技术架构图
```mermaid
graph TD
    A[用户输入文本] --> B[前端React界面]
    B --> C[FastAPI后端]
    C --> D[LangGraph工作流]
    D --> E[声明提取Agent]
    D --> F[财务验证Agent]
    D --> G[冲突检查]
    D --> H[报告生成]
    H --> I[AGUI事件通知]
    I --> B
    B --> J[结果展示]
```

### 关键代码位置
- **后端工作流**: `apps/api-server/src/domain/fact_check/workflow.py`
- **前端主组件**: `apps/web-app/src/components/fact-check/FactCheckMain.tsx`
- **状态管理**: `apps/web-app/src/store/factCheckStore.ts`
- **API端点**: `apps/api-server/src/api/v1/endpoints/fact_check.py`