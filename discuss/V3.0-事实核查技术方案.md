# V3.0 架构下的事实核查功能技术方案

> **文档类型**: 核心技术方案  
> **实施阶段**: 对应“实施策略与路线图”中的 **Phase 0**  
> **核心目标**: 使用 V3.0 的多 Agent 协作架构，构建一个业界领先、可扩展、可进化的事实核查引擎。

---

## 1. 核心设计理念

我们将事实核查任务，从一个简单的“输入-验证-输出”线性流程，重新定义为一个由专业 Agent 团队协作完成的 **动态研究项目**。即便只是核查一段文本，我们的系统也会像一个严谨的研究团队一样运作，确保核查过程的深度、广度和可靠性。

**核心原则**:
- **专业分工**: 每个 Agent 只做自己最擅长的事。
- **交叉验证**: 通过不同路径、不同来源的信息进行比对。
- **冲突解决**: 具备识别和处理信息矛盾的能力。
- **架构复用**: 为未来完整的投研功能奠定坚实基础。

---

## 2. 事实核查 Agent 团队架构

我们将组建一个专门用于事实核查的“AI 团队”。

```mermaid
graph TD
    subgraph "用户交互层"
        UI[用户输入待核查文本]
    end

    subgraph "编排与规划层"
        O[🎯 项目总监<br/>Orchestrator]
    end

    subgraph "核心任务执行层"
        CE[📝 声明提取 Agent<br/>Claim Extractor]
        VS[👥 验证专家团队]
        DM[🔄 辩论主持人<br/>Debate Moderator]
        SA[📄 报告生成 Agent<br/>Synthesis Agent]
    end
    
    subgraph "验证专家团队"
        FV[💰 财务数据验证员<br/>Financial Verifier]
        CV[🏢 公司声明验证员<br/>Corporate Verifier]
        NV[🌐 新闻与市场传闻验证员<br/>News Verifier]
    end

    subgraph "数据与工具层"
        subgraph "结构化数据源"
            SEC[SEC EDGAR API]
            FMP[Financial APIs<br/>AlphaVantage, FMP]
        end
        subgraph "非结构化数据源"
            NEWS[News APIs<br/>Google, etc.]
            WEB[Web Search Engine]
        end
    end

    UI --> O
    O --> CE
    CE --> O
    
    O --> VS
    VS --> O
    
    O -- "发现信息冲突" --> DM
    DM -- "组织辩论" --> VS
    VS -- "反馈修正意见" --> DM
    DM -- "形成共识" --> O

    O --> SA
    SA --> Report[✅ 最终核查报告]

    FV --> SEC
    FV --> FMP
    CV --> WEB
    NV --> NEWS
    NV --> WEB
```

---

## 3. Agent 角色与职责定义

#### a. 项目总监 (Orchestrator)
- **职责**: 整个事实核查流程的总指挥。
- **工作流**:
    1.  接收用户输入的文本。
    2.  启动 **声明提取 Agent**，将非结构化文本分解为一系列可核查的独立声明。
    3.  分析每个声明的类型（财务数据、公司行为、市场传闻等），制定验证计划。
    4.  将不同声明 **分配** 给对应的 **验证专家** 并行处理。
    5.  收集所有专家的验证结果。如果发现 **信息冲突**（如不同来源对同一事件说法不一），则启动 **辩论主持人**。
    6.  在所有声明都处理完毕后，启动 **报告生成 Agent** 汇总结果。
- **核心技术**: 基于 LangGraph 的状态机，管理任务流转和状态。

#### b. 声明提取 Agent (Claim Extractor)
- **职责**: 文本预处理与信息结构化。
- **工作流**:
    1.  接收原始文本。
    2.  使用 NLP 技术和 LLM，识别并提取出所有包含具体事实、可被验证的断言（Claims）。
    3.  为每个声明打上初步的分类标签（如 `financial_data`, `product_launch`, `market_rumor`）。
- **示例**:
    -   *输入*: "据报道，苹果公司在上一季度的营收达到了900亿美元，并计划在秋季发布新款 iPhone。"
    -   *输出 (结构化声明)*:
        1.  `{ "claim": "苹果公司上一季度营收为900亿美元", "type": "financial_data" }`
        2.  `{ "claim": "苹果公司计划在秋季发布新款iPhone", "type": "corporate_statement" }`

#### c. 验证专家团队 (Verification Specialists)
这是一个由多个各司其职的 Agent 组成的团队。

-   **财务数据验证员 (Financial Verifier)**:
    -   **专长**: 核实所有与财务相关的数字。
    -   **工具**: 对接 SEC EDGAR 数据库 API、Alpha Vantage、Financial Modeling Prep 等专业财经数据接口。
    -   **输出**: `{"status": "Verified", "source": "SEC 10-Q Filing", "value": "90.1B USD"}` 或 `{"status": "Contradicted", "source": "SEC 10-Q Filing", "value": "83B USD"}`。

-   **公司声明验证员 (Corporate Verifier)**:
    -   **专长**: 核实公司的官方行为、产品发布、战略规划等。
    -   **工具**: 优先搜索公司官网的投资者关系页面、官方新闻稿、提交给监管机构的文件。
    -   **输出**: `{"status": "Verified", "source": "Apple Newsroom Press Release, 2025-07-22"}`。

-   **新闻与市场传闻验证员 (News Verifier)**:
    -   **专长**: 核实来自媒体报道、社交网络的信息，并评估其可信度。
    -   **工具**: 对接 Google News 等新闻聚合 API，进行多信源交叉验证。
    -   **输出**: `{"status": "Substantiated", "sources": ["Reuters", "Bloomberg"], "confidence": "High"}` 或 `{"status": "Unverified_Rumor", "confidence": "Low"}`。

#### d. 辩论主持人 (Debate Moderator)
- **职责**: 解决专家之间的信息冲突，是确保报告质量的关键。
- **工作流**:
    1.  当 **项目总监** 发现两个或以上专家对同一声明给出了矛盾的验证结果时被激活。
    2.  组织一场“辩论”，让持有不同意见的专家陈述各自的证据和理由。
    3.  引导它们评估对方信源的可靠性、时效性。
    4.  最终形成一个综合性的结论，例如：“信息冲突，官方财报显示为 X，但多家媒体报道为 Y，建议谨慎采纳。”
- **核心技术**: 使用一个高级的 LLM，通过精心设计的 Prompt 来引导多轮对话，最终达成共识或清晰地呈现分歧。

#### e. 报告生成 Agent (Synthesis Agent)
- **职责**: 将零散的验证结果，整合成一份清晰、易读的最终报告。
- **工作流**:
    1.  接收所有已处理完毕的声明及其验证结果。
    2.  按照原始文本的顺序，对每个声明进行标注和解释。
    3.  高亮显示信息冲突点、无法验证的声明和重要的证据来源。
    4.  生成最终的核查报告。

---

## 4. 核心工作流程（时序图）

```mermaid
sequenceDiagram
    participant User
    participant Orchestrator as 🎯 总监
    participant Extractor as 📝 提取器
    participant Verifiers as 👥 专家团队
    participant Moderator as 🔄 辩论主持
    participant Synthesizer as 📄 生成器

    User->>Orchestrator: 提交待核查文本
    Orchestrator->>Extractor: 提取声明
    Extractor-->>Orchestrator: 返回结构化声明列表
    
    Orchestrator->>Verifiers: 分配验证任务 (并行)
    
    par 并行验证
        Verifiers->>Verifiers: 各自查询数据源
    end

    Verifiers-->>Orchestrator: 返回验证结果
    
    alt 发现信息冲突
        Orchestrator->>Moderator: 启动辩论
        Moderator->>Verifiers: 质询与讨论
        Verifiers-->>Moderator: 修正观点
        Moderator-->>Orchestrator: 返回共识或分歧摘要
    end

    Orchestrator->>Synthesizer: 提交所有最终结果
    Synthesizer-->>Orchestrator: 生成最终报告
    Orchestrator-->>User: 返回事实核查报告
```

---

## 5. 技术选型与要点

- **Agent 框架**: **LangGraph**。其基于图的状态机模型非常适合管理我们这种复杂的、多 Agent 协作的、可能存在循环和条件分支的工作流。
- **大语言模型 (LLMs)**:
    -   **声明提取/报告生成**: 可使用成本较低、速度较快的模型（如 GPT-3.5-Turbo, Claude 3 Sonnet）。
    -   **专家 Agent/辩论主持**: 必须使用具有强大逻辑推理和工具使用能力的顶级模型（如 GPT-4o, Claude 3 Opus），以确保分析和决策的质量。
- **数据源**: 严格按照 V1.0 方案中的分层策略，优先使用免费、权威的官方数据源（如 SEC），其次是低成本 API，最后是网页爬取。
- **缓存机制**: 使用 **Redis** 对所有外部 API 调用进行缓存，尤其是对那些不经常变化的数据（如历史财报），以大幅降低成本和提高响应速度。

通过此方案，我们不仅能交付一个功能强大的事实核查工具，更为重要的是，我们搭建并验证了 V3.0 的核心——一个可扩展、可进化的多 Agent 智能协作系统。这为我们后续增加更复杂的投研功能铺平了道路。 