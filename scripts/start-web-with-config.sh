#!/bin/bash

# 停止并删除现有容器（如果存在）
docker stop yai-web-test-container 2>/dev/null || true
docker rm yai-web-test-container 2>/dev/null || true

# 使用 ConfigMap 中的环境变量启动容器
docker run -p 3000:3000 --name yai-web-test-container \
  -e NEXT_PUBLIC_API_URL="http://localhost:8080" \
  -e NEXT_PUBLIC_APP_VERSION="1.0.0" \
  -e NEXT_PUBLIC_SERVICE_NAME="yai-investor-insight-web" \
  -e NODE_ENV="production" \
  -e NEXT_PUBLIC_SLS_ENABLED="true" \
  -e NEXT_PUBLIC_SLS_ENDPOINT="https://cn-beijing.log.aliyuncs.com" \
  -e NEXT_PUBLIC_SLS_ACCESS_KEY_ID="LTAI5tPrkKMrPLW1XjbBxwhm" \
  -e NEXT_PUBLIC_SLS_ACCESS_KEY_SECRET="******************************" \
  -e NEXT_PUBLIC_SLS_PROJECT="yai-log-test" \
  -e NEXT_PUBLIC_SLS_LOGSTORE="app-log" \
  -e NEXT_PUBLIC_SLS_REGION="cn-beijing" \
  -e NEXT_PUBLIC_SLS_TOPIC="default" \
  -e KUBERNETES_DEPLOYMENT="false" \
  -e DEPLOYMENT_ENVIRONMENT="local" \
  yai-web-test

echo "Web应用已启动，访问地址: http://localhost:3000"