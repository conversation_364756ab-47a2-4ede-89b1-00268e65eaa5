#!/bin/bash

# AI投资洞察平台 - 查看所有服务日志脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "📋 使用说明:"
    echo "   ./scripts/logs-all.sh [选项]"
    echo ""
    echo "📋 选项:"
    echo "   --tail, -t [行数]    查看所有日志最新行数 (默认30行)"
    echo "   --follow, -f         实时跟踪所有日志"
    echo "   --summary, -s        显示日志摘要信息"
    echo "   --grep, -g [关键词]  在所有日志中搜索关键词"
    echo "   --error, -e          显示所有错误日志"
    echo "   --recent, -r [分钟]  显示最近几分钟的日志 (默认10分钟)"
    echo "   --help               显示此帮助信息"
    echo ""
    echo "📋 示例:"
    echo "   ./scripts/logs-all.sh --tail      # 查看所有日志最新30行"
    echo "   ./scripts/logs-all.sh -f          # 实时跟踪所有日志"
    echo "   ./scripts/logs-all.sh -s          # 显示日志摘要"
    echo "   ./scripts/logs-all.sh -g \"error\" # 在所有日志中搜索错误"
}

# 检查日志文件状态
check_log_files() {
    local backend_exists=false
    local frontend_exists=false
    local dev_services_exists=false
    
    echo ""
    echo "📄 日志文件状态:"
    echo "----------------------------------------"
    
    if [ -f "logs/api-server.log" ]; then
        backend_exists=true
        local size=$(ls -lh logs/api-server.log | awk '{print $5}')
        local lines=$(wc -l < logs/api-server.log)
        echo "   ✅ 后端日志: logs/api-server.log ($size, $lines 行)"
    else
        echo "   ❌ 后端日志: 不存在"
    fi
    
    if [ -f "logs/web-app.log" ]; then
        frontend_exists=true
        local size=$(ls -lh logs/web-app.log | awk '{print $5}')
        local lines=$(wc -l < logs/web-app.log)
        echo "   ✅ 前端日志: logs/web-app.log ($size, $lines 行)"
    else
        echo "   ❌ 前端日志: 不存在"
    fi
    
    if [ -f "logs/dev-services.log" ]; then
        dev_services_exists=true
        local size=$(ls -lh logs/dev-services.log | awk '{print $5}')
        local lines=$(wc -l < logs/dev-services.log)
        echo "   ✅ 开发服务日志: logs/dev-services.log ($size, $lines 行)"
    else
        echo "   ❌ 开发服务日志: 不存在"
    fi
    
    # 返回状态
    if [ "$backend_exists" = true ] || [ "$frontend_exists" = true ] || [ "$dev_services_exists" = true ]; then
        return 0
    else
        return 1
    fi
}

# 显示日志摘要
show_log_summary() {
    log_info "生成日志摘要..."
    
    if ! check_log_files; then
        log_error "未找到任何日志文件"
        echo ""
        echo "建议操作:"
        echo "  • 启动服务: ./scripts/start-all.sh"
        echo "  • 检查服务状态: ./scripts/check-services.sh"
        return 1
    fi
    
    echo ""
    echo "📊 日志统计摘要:"
    echo "========================================"
    
    # 后端日志统计
    if [ -f "logs/api-server.log" ]; then
        echo ""
        echo -e "${PURPLE}🔧 后端服务日志:${NC}"
        local total_lines=$(wc -l < logs/api-server.log)
        local error_count=$(grep -ci "error\|ERROR" logs/api-server.log 2>/dev/null || echo "0")
        local warning_count=$(grep -ci "warning\|WARNING\|warn\|WARN" logs/api-server.log 2>/dev/null || echo "0")
        local info_count=$(grep -ci "info\|INFO" logs/api-server.log 2>/dev/null || echo "0")
        
        echo "   总行数: $total_lines"
        echo "   错误: $error_count 条"
        echo "   警告: $warning_count 条"
        echo "   信息: $info_count 条"
        
        # 最近的错误
        if [ "$error_count" -gt 0 ]; then
            echo "   最近错误:"
            grep -i "error\|ERROR" logs/api-server.log | tail -3 | sed 's/^/     /'
        fi
    fi
    
    # 前端日志统计
    if [ -f "logs/web-app.log" ]; then
        echo ""
        echo -e "${CYAN}🎨 前端服务日志:${NC}"
        local total_lines=$(wc -l < logs/web-app.log)
        local error_count=$(grep -ci "error\|ERROR" logs/web-app.log 2>/dev/null || echo "0")
        local warning_count=$(grep -ci "warning\|WARNING\|warn\|WARN" logs/web-app.log 2>/dev/null || echo "0")
        
        echo "   总行数: $total_lines"
        echo "   错误: $error_count 条"
        echo "   警告: $warning_count 条"
        
        # 编译状态
        if grep -q "compiled successfully" logs/web-app.log; then
            echo "   编译状态: ✅ 成功"
        elif grep -q "Failed to compile" logs/web-app.log; then
            echo "   编译状态: ❌ 失败"
        else
            echo "   编译状态: ❓ 未知"
        fi
        
        # 最近的错误
        if [ "$error_count" -gt 0 ]; then
            echo "   最近错误:"
            grep -i "error\|ERROR" logs/web-app.log | tail -3 | sed 's/^/     /'
        fi
    fi
    
    # 开发服务日志统计
    if [ -f "logs/dev-services.log" ]; then
        echo ""
        echo -e "${YELLOW}🚀 开发服务日志:${NC}"
        local total_lines=$(wc -l < logs/dev-services.log)
        echo "   总行数: $total_lines"
    fi
}

# 实时跟踪所有日志
follow_all_logs() {
    log_info "实时跟踪所有服务日志 (按 Ctrl+C 退出)"
    echo "========================================"
    
    local log_files=()
    
    # 收集存在的日志文件
    if [ -f "logs/api-server.log" ]; then
        log_files+=("logs/api-server.log")
    fi
    
    if [ -f "logs/web-app.log" ]; then
        log_files+=("logs/web-app.log")
    fi
    
    if [ -f "logs/dev-services.log" ]; then
        log_files+=("logs/dev-services.log")
    fi
    
    if [ ${#log_files[@]} -eq 0 ]; then
        log_error "未找到任何日志文件可以跟踪"
        return 1
    fi
    
    # 使用 tail -f 跟踪多个文件
    echo "正在跟踪以下日志文件:"
    for file in "${log_files[@]}"; do
        echo "  • $file"
    done
    echo ""
    
    tail -f "${log_files[@]}"
}

# 查看所有日志最新内容
view_recent_logs() {
    local lines=${1:-30}
    
    log_info "显示所有服务日志最新 $lines 行"
    
    # 后端日志
    if [ -f "logs/api-server.log" ]; then
        echo ""
        echo -e "${PURPLE}🔧 后端服务日志 (最新 $lines 行):${NC}"
        echo "========================================"
        tail -n $lines logs/api-server.log
    fi
    
    # 前端日志
    if [ -f "logs/web-app.log" ]; then
        echo ""
        echo -e "${CYAN}🎨 前端服务日志 (最新 $lines 行):${NC}"
        echo "========================================"
        tail -n $lines logs/web-app.log
    fi
    
    # 开发服务日志
    if [ -f "logs/dev-services.log" ]; then
        echo ""
        echo -e "${YELLOW}🚀 开发服务日志 (最新 $lines 行):${NC}"
        echo "========================================"
        tail -n $lines logs/dev-services.log
    fi
}

# 搜索所有日志
search_all_logs() {
    local keyword="$1"
    
    if [ -z "$keyword" ]; then
        log_error "搜索关键词不能为空"
        return 1
    fi
    
    log_info "在所有日志中搜索: '$keyword'"
    
    local found_any=false
    
    # 搜索后端日志
    if [ -f "logs/api-server.log" ]; then
        echo ""
        echo -e "${PURPLE}🔧 后端服务日志中的匹配结果:${NC}"
        echo "========================================"
        if grep --color=always -i "$keyword" logs/api-server.log; then
            found_any=true
        else
            echo "   未找到匹配结果"
        fi
    fi
    
    # 搜索前端日志
    if [ -f "logs/web-app.log" ]; then
        echo ""
        echo -e "${CYAN}🎨 前端服务日志中的匹配结果:${NC}"
        echo "========================================"
        if grep --color=always -i "$keyword" logs/web-app.log; then
            found_any=true
        else
            echo "   未找到匹配结果"
        fi
    fi
    
    # 搜索开发服务日志
    if [ -f "logs/dev-services.log" ]; then
        echo ""
        echo -e "${YELLOW}🚀 开发服务日志中的匹配结果:${NC}"
        echo "========================================"
        if grep --color=always -i "$keyword" logs/dev-services.log; then
            found_any=true
        else
            echo "   未找到匹配结果"
        fi
    fi
    
    if [ "$found_any" = false ]; then
        echo ""
        log_warning "在所有日志文件中均未找到包含 '$keyword' 的内容"
    fi
}

# 显示所有错误日志
show_all_errors() {
    log_info "显示所有服务的错误日志"
    
    local found_errors=false
    
    # 后端错误
    if [ -f "logs/api-server.log" ]; then
        echo ""
        echo -e "${PURPLE}🔧 后端服务错误日志:${NC}"
        echo "========================================"
        if grep --color=always -i "error\|ERROR" logs/api-server.log; then
            found_errors=true
        else
            echo "   未发现错误日志"
        fi
    fi
    
    # 前端错误
    if [ -f "logs/web-app.log" ]; then
        echo ""
        echo -e "${CYAN}🎨 前端服务错误日志:${NC}"
        echo "========================================"
        if grep --color=always -i "error\|ERROR" logs/web-app.log; then
            found_errors=true
        else
            echo "   未发现错误日志"
        fi
    fi
    
    # 开发服务错误
    if [ -f "logs/dev-services.log" ]; then
        echo ""
        echo -e "${YELLOW}🚀 开发服务错误日志:${NC}"
        echo "========================================"
        if grep --color=always -i "error\|ERROR" logs/dev-services.log; then
            found_errors=true
        else
            echo "   未发现错误日志"
        fi
    fi
    
    if [ "$found_errors" = false ]; then
        echo ""
        log_success "所有服务均未发现错误日志"
    fi
}

# 主日志处理函数
handle_logs() {
    local action="recent"
    local lines=30
    local keyword=""
    local minutes=10
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --tail|-t)
                action="recent"
                if [[ $2 =~ ^[0-9]+$ ]]; then
                    lines=$2
                    shift
                fi
                shift
                ;;
            --follow|-f)
                action="follow"
                shift
                ;;
            --summary|-s)
                action="summary"
                shift
                ;;
            --grep|-g)
                action="search"
                keyword="$2"
                shift 2
                ;;
            --error|-e)
                action="errors"
                shift
                ;;
            --recent|-r)
                action="recent_time"
                if [[ $2 =~ ^[0-9]+$ ]]; then
                    minutes=$2
                    shift
                fi
                shift
                ;;
            --help)
                show_usage
                return 0
                ;;
            *)
                log_error "未知选项: $1"
                show_usage
                return 1
                ;;
        esac
    done
    
    # 执行相应操作
    case $action in
        recent)
            view_recent_logs $lines
            ;;
        follow)
            follow_all_logs
            ;;
        summary)
            show_log_summary
            ;;
        search)
            search_all_logs "$keyword"
            ;;
        errors)
            show_all_errors
            ;;
        recent_time)
            log_info "显示最近 $minutes 分钟的日志"
            # macOS 的 find 命令语法
            find logs -name "*.log" -mtime -${minutes}m -exec echo "=== {} ===" \; -exec tail -20 {} \; 2>/dev/null || {
                log_warning "无法根据时间过滤，显示最新内容"
                view_recent_logs 50
            }
            ;;
    esac
    
    # 显示管理提示
    echo ""
    echo "📋 日志管理命令:"
    echo "   查看后端日志: ./scripts/logs-backend.sh"
    echo "   查看前端日志: ./scripts/logs-frontend.sh"
    echo "   检查服务状态: ./scripts/check-services.sh"
    echo ""
}

# 主函数
main() {
    echo "📋 AI投资洞察平台 - 所有服务日志查看器"
    echo "=================================================="
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] || [ ! -f "nx.json" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 如果没有参数，默认显示最新日志
    if [ $# -eq 0 ]; then
        handle_logs --tail
    else
        handle_logs "$@"
    fi
}

# 运行主函数
main "$@"