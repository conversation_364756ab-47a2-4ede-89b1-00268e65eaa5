#!/bin/bash

# AI投资洞察平台 - 独立停止后端服务脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止后端服务
stop_backend() {
    log_info "停止后端服务..."
    
    local service_stopped=false
    
    # 通过 PID 文件停止服务
    if [ -f logs/api-server.pid ]; then
        local pid=$(cat logs/api-server.pid)
        if kill -0 $pid 2>/dev/null; then
            log_info "停止后端服务进程 (PID: $pid)..."
            
            # 先尝试优雅停止 (SIGTERM)
            kill $pid 2>/dev/null || true
            
            # 等待进程停止
            local max_wait=10
            local wait_count=0
            
            while [ $wait_count -lt $max_wait ]; do
                if ! kill -0 $pid 2>/dev/null; then
                    log_success "后端服务已优雅停止"
                    service_stopped=true
                    break
                fi
                sleep 1
                wait_count=$((wait_count + 1))
                echo -n "."
            done
            
            # 如果优雅停止失败，强制停止 (SIGKILL)
            if [ "$service_stopped" = false ]; then
                echo ""
                log_warning "优雅停止超时，强制停止服务..."
                kill -9 $pid 2>/dev/null || true
                sleep 2
                
                if ! kill -0 $pid 2>/dev/null; then
                    log_success "后端服务已强制停止"
                    service_stopped=true
                else
                    log_error "无法停止后端服务进程"
                fi
            fi
        else
            log_warning "PID 文件中的进程不存在，清理 PID 文件"
        fi
        
        # 清理 PID 文件
        rm logs/api-server.pid
    else
        log_warning "未找到后端服务 PID 文件"
    fi
    
    # 强制清理相关进程
    log_info "清理相关进程..."
    
    # 杀死所有 uvicorn 相关进程
    local uvicorn_pids=$(pgrep -f "uvicorn.*main:app" 2>/dev/null || true)
    if [ -n "$uvicorn_pids" ]; then
        log_info "清理 uvicorn 进程: $uvicorn_pids"
        echo "$uvicorn_pids" | xargs kill 2>/dev/null || true
        sleep 2
        
        # 如果还存在，强制杀死
        uvicorn_pids=$(pgrep -f "uvicorn.*main:app" 2>/dev/null || true)
        if [ -n "$uvicorn_pids" ]; then
            echo "$uvicorn_pids" | xargs kill -9 2>/dev/null || true
        fi
        service_stopped=true
    fi
    
    # 杀死所有 Python FastAPI 相关进程
    local python_pids=$(pgrep -f "python.*main.py" 2>/dev/null || true)
    if [ -n "$python_pids" ]; then
        log_info "清理 Python FastAPI 进程: $python_pids"
        echo "$python_pids" | xargs kill 2>/dev/null || true
        sleep 2
        
        # 如果还存在，强制杀死
        python_pids=$(pgrep -f "python.*main.py" 2>/dev/null || true)
        if [ -n "$python_pids" ]; then
            echo "$python_pids" | xargs kill -9 2>/dev/null || true
        fi
        service_stopped=true
    fi
    
    # 检查端口占用
    if lsof -i :8000 >/dev/null 2>&1; then
        log_warning "端口 8000 仍被占用，尝试清理..."
        local port_pids=$(lsof -ti :8000 2>/dev/null || true)
        if [ -n "$port_pids" ]; then
            echo "$port_pids" | xargs kill 2>/dev/null || true
            sleep 2
            
            # 如果还占用，强制杀死
            port_pids=$(lsof -ti :8000 2>/dev/null || true)
            if [ -n "$port_pids" ]; then
                echo "$port_pids" | xargs kill -9 2>/dev/null || true
            fi
        fi
        service_stopped=true
    fi
    
    # 最终检查
    if lsof -i :8000 >/dev/null 2>&1; then
        log_error "端口 8000 仍被占用，可能需要手动清理"
        lsof -i :8000 | grep LISTEN
        return 1
    else
        if [ "$service_stopped" = true ]; then
            log_success "后端服务已完全停止"
        else
            log_info "未发现运行中的后端服务"
        fi
    fi
}

# 显示状态信息
show_status() {
    echo ""
    echo "📊 后端服务状态:"
    
    # 检查 PID 文件
    if [ -f logs/api-server.pid ]; then
        echo "   PID 文件: 存在"
    else
        echo "   PID 文件: 不存在"
    fi
    
    # 检查端口占用
    if lsof -i :8000 >/dev/null 2>&1; then
        echo "   端口 8000: 被占用"
    else
        echo "   端口 8000: 空闲"
    fi
    
    # 检查相关进程
    local uvicorn_count=$(pgrep -f "uvicorn.*main:app" 2>/dev/null | wc -l)
    echo "   uvicorn 进程: $uvicorn_count 个"
    
    local python_count=$(pgrep -f "python.*main.py" 2>/dev/null | wc -l)
    echo "   Python API 进程: $python_count 个"
    
    echo ""
    echo "📋 管理命令:"
    echo "   启动服务: ./scripts/start-backend.sh"
    echo "   重启服务: ./scripts/restart-backend.sh"
    echo "   查看日志: tail -f logs/api-server.log"
    echo ""
}

# 主函数
main() {
    echo "🛑 AI投资洞察平台 - 停止后端服务"
    echo "=================================="
    
    stop_backend
    show_status
}

# 运行主函数
main "$@"