#!/bin/bash

# AI投资洞察平台 - 重启所有服务脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查脚本是否存在
check_scripts() {
    local required_scripts=(
        "./scripts/stop-all.sh"
        "./scripts/start-all.sh"
    )
    
    for script in "${required_scripts[@]}"; do
        if [ ! -f "$script" ]; then
            log_error "找不到必需的脚本: $script"
            exit 1
        fi
    done
}

# 重启所有服务
restart_all_services() {
    log_info "重启所有服务（前端 + 后端）..."
    
    # 第一步：停止所有现有服务
    log_info "步骤 1/2: 停止所有现有服务"
    echo "========================================"
    ./scripts/stop-all.sh
    
    # 等待完全停止
    log_info "等待所有服务完全停止..."
    sleep 5
    
    # 第二步：启动所有服务
    echo ""
    log_info "步骤 2/2: 启动所有服务"
    echo "========================================"
    
    # 根据参数决定启动模式
    if [ "${1:-}" = "--foreground" ]; then
        log_info "以前台模式重启所有服务..."
        exec ./scripts/start-all.sh --foreground
    else
        log_info "以后台模式重启所有服务..."
        ./scripts/start-all.sh
        
        log_success "所有服务重启完成"
        echo ""
        echo "📋 管理命令:"
        echo "   查看所有日志: ./scripts/logs-all.sh"
        echo "   查看服务状态: ./scripts/check-services.sh"
        echo "   停止所有服务: ./scripts/stop-all.sh"
        echo ""
    fi
}

# 主函数
main() {
    echo "🔄 AI投资洞察平台 - 重启所有服务"
    echo "========================================"
    echo "📝 将重启前端和后端服务"
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] || [ ! -f "nx.json" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    check_scripts
    restart_all_services "$@"
}

# 运行主函数
main "$@"