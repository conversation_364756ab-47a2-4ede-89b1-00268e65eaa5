#!/bin/bash

# AI投资洞察平台 - 完整卸载脚本
# 负责清理所有依赖、构建产物和临时文件

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🗑️  AI投资洞察平台 - 完整卸载${NC}"
echo "=================================="

# 检查是否在项目根目录
if [ ! -f "package.json" ] || [ ! -f "nx.json" ]; then
    echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 确认卸载
echo -e "${YELLOW}⚠️  此操作将删除以下内容:${NC}"
echo "   - 所有 node_modules"
echo "   - Python 虚拟环境 (.venv)"
echo "   - 构建产物 (dist, .next)"
echo "   - 日志文件 (logs)"
echo "   - 缓存文件"
echo ""
read -p "确认继续卸载? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}取消卸载${NC}"
    exit 0
fi

# 1. 停止所有服务
echo -e "${BLUE}🛑 停止所有服务...${NC}"
if [ -f "scripts/stop-all.sh" ]; then
    ./scripts/stop-all.sh || true
fi

# 2. 清理前端依赖
echo -e "${BLUE}🧹 清理前端依赖...${NC}"
rm -rf node_modules
rm -rf .pnpm-store
rm -rf ~/.pnpm-store 2>/dev/null || true
# 清理所有子项目的 node_modules
find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true

# 3. 清理 Python 环境
echo -e "${BLUE}🐍 清理 Python 环境...${NC}"
rm -rf .venv
rm -rf apps/api-server/.venv
rm -rf apps/api-server/__pycache__
find . -name "*.pyc" -delete 2>/dev/null || true
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 4. 清理构建产物
echo -e "${BLUE}🔨 清理构建产物...${NC}"
rm -rf libs/*/dist
rm -rf apps/*/dist
rm -rf apps/*/.next
rm -rf apps/*/.turbo
rm -rf .turbo

# 5. 清理日志文件
echo -e "${BLUE}📝 清理日志文件...${NC}"
rm -rf logs
# 清理所有子项目的 logs 目录
find . -name "logs" -type d -exec rm -rf {} + 2>/dev/null || true
rm -rf *.log

# 6. 清理缓存
echo -e "${BLUE}💾 清理缓存...${NC}"
rm -rf .nx
rm -rf .cache
rm -rf .tmp
rm -rf tmp

# 7. 清理编辑器临时文件
echo -e "${BLUE}📝 清理编辑器临时文件...${NC}"
find . -name ".DS_Store" -delete 2>/dev/null || true
find . -name "Thumbs.db" -delete 2>/dev/null || true
find . -name "*.swp" -delete 2>/dev/null || true
find . -name "*.swo" -delete 2>/dev/null || true

# 8. 清理测试覆盖率文件
echo -e "${BLUE}🧪 清理测试文件...${NC}"
rm -rf coverage
rm -rf .coverage
rm -rf .pytest_cache
rm -rf .nyc_output

# 9. 清理包管理器锁文件（可选）
echo ""
read -p "是否删除锁文件 (pnpm-lock.yaml)? 这将需要重新解析依赖版本 (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}🔒 清理锁文件...${NC}"
    rm -f pnpm-lock.yaml
    rm -f package-lock.json
    rm -f yarn.lock
fi

# 10. 验证清理结果
echo -e "${BLUE}✅ 验证清理结果...${NC}"

REMAINING_FILES=()

if [ -d "node_modules" ]; then
    REMAINING_FILES+=("node_modules")
fi

if [ -d "apps/api-server/.venv" ]; then
    REMAINING_FILES+=("Python 虚拟环境")
fi

if [ -d "logs" ]; then
    REMAINING_FILES+=("日志目录")
fi

if [ ${#REMAINING_FILES[@]} -gt 0 ]; then
    echo -e "${YELLOW}⚠️  以下文件/目录未能完全清理:${NC}"
    for file in "${REMAINING_FILES[@]}"; do
        echo "   - $file"
    done
    echo -e "${YELLOW}请手动检查并删除${NC}"
else
    echo -e "${GREEN}✅ 所有文件已成功清理${NC}"
fi

echo ""
echo -e "${GREEN}🎉 卸载完成！${NC}"
echo "=================================="
echo -e "${BLUE}📍 项目已恢复到初始状态${NC}"
echo ""
echo -e "${BLUE}📍 重新安装:${NC}"
echo "   运行: ./scripts/install.sh"
echo ""
echo -e "${YELLOW}💡 提示: 如果需要完全重新开始，建议重新克隆代码库${NC}"