#!/bin/bash

# 测试 API 服务器的 Docker 构建
set -e

echo "🔧 测试 API 服务器 Docker 构建..."

# 构建 Docker 镜像
echo "📦 构建 Docker 镜像..."
docker build -f deploy/api/Dockerfile -t api-server-test:latest .

echo "✅ Docker 镜像构建成功！"

# 可选：运行容器进行快速测试
echo "🚀 启动容器进行快速测试..."
docker run --rm -d --name api-server-test -p 8000:8000 api-server-test:latest

echo "⏳ 等待服务启动..."
sleep 5

# 检查服务是否正常运行
echo "🔍 检查容器状态..."
docker ps -a --filter name=api-server-test

echo "📋 检查服务日志..."
docker logs api-server-test

# 尝试访问服务
echo "🌐 尝试访问服务..."
if curl -f http://localhost:8000/ > /dev/null 2>&1; then
    echo "✅ 服务启动成功！"
elif curl -f http://localhost:8000/docs > /dev/null 2>&1; then
    echo "✅ 服务启动成功（API 文档可访问）！"
else
    echo "❌ 服务可能未正常启动，但容器已运行。请检查日志。"
fi

# 清理测试容器
echo "🧹 清理测试容器..."
docker stop api-server-test || true
docker rmi api-server-test:latest || true

echo "🎉 测试完成！" 