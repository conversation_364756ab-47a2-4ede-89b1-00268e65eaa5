#!/bin/bash

# AI投资洞察平台 - 停止开发服务脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务
stop_services() {
    log_info "停止开发服务..."

    # 停止并行启动的服务
    if [ -f logs/dev-services.pid ]; then
        DEV_PID=$(cat logs/dev-services.pid)
        if kill -0 $DEV_PID 2>/dev/null; then
            kill $DEV_PID
            log_success "并行服务已停止 (PID: $DEV_PID)"
        else
            log_warning "并行服务进程不存在"
        fi
        rm logs/dev-services.pid
    fi

    # 停止后端服务
    if [ -f logs/api-server.pid ]; then
        API_PID=$(cat logs/api-server.pid)
        if kill -0 $API_PID 2>/dev/null; then
            kill $API_PID
            log_success "后端服务已停止 (PID: $API_PID)"
        else
            log_warning "后端服务进程不存在"
        fi
        rm logs/api-server.pid
    fi

    # 停止前端服务
    if [ -f logs/web-app.pid ]; then
        WEB_PID=$(cat logs/web-app.pid)
        if kill -0 $WEB_PID 2>/dev/null; then
            kill $WEB_PID
            log_success "前端服务已停止 (PID: $WEB_PID)"
        else
            log_warning "前端服务进程不存在"
        fi
        rm logs/web-app.pid
    fi

    # 强制清理所有相关进程
    log_info "清理残留进程..."
    pkill -f "nx run" 2>/dev/null || true
    pkill -f "next dev" 2>/dev/null || true
    pkill -f "uvicorn" 2>/dev/null || true
    pkill -f "python.*main.py" 2>/dev/null || true

    # 检查端口占用
    log_info "检查端口占用..."
    if lsof -i :8000 >/dev/null 2>&1; then
        log_warning "端口 8000 仍被占用"
        lsof -i :8000 | grep LISTEN || true
    fi

    if lsof -i :3000 >/dev/null 2>&1; then
        log_warning "端口 3000 仍被占用"
        lsof -i :3000 | grep LISTEN || true
    fi

    if lsof -i :4200 >/dev/null 2>&1; then
        log_warning "端口 4200 仍被占用"
        lsof -i :4200 | grep LISTEN || true
    fi

    log_success "所有服务已停止"
}

# 主函数
main() {
    echo "⏹️  AI投资洞察平台 - 停止开发服务"
    echo "=================================="
    
    stop_services
    
    echo ""
    echo "✅ 开发服务已完全停止"
}

# 运行主函数
main "$@"
