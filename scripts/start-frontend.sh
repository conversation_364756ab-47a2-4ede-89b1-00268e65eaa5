#!/bin/bash

# AI投资洞察平台 - 仅启动前端开发服务
# 用于测试前端修复，不启动后端服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查前端依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        log_error "pnpm 未安装"
        exit 1
    fi

    log_success "前端依赖检查通过"
}

# 安装前端依赖
install_dependencies() {
    log_info "安装前端依赖..."

    # 安装项目依赖
    log_info "安装项目依赖..."
    pnpm install

    # 构建共享库
    log_info "构建共享库..."
    pnpm run build:libs

    log_success "前端依赖安装完成"
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."

    # 创建日志目录
    mkdir -p logs

    # 启动前端服务
    log_info "启动 Next.js 开发服务器..."
    pnpm nx dev web-app > logs/web-app.log 2>&1 &
    WEB_PID=$!
    
    # 等待服务启动
    log_info "等待前端服务启动..."
    sleep 12

    # 检查服务状态
    WEB_RUNNING=false
    if curl -s http://localhost:4200/ > /dev/null; then
        log_success "前端服务启动成功 (端口: 4200, PID: $WEB_PID)"
        echo $WEB_PID > logs/web-app.pid
        WEB_RUNNING=true
    elif curl -s http://localhost:3000/ > /dev/null; then
        log_success "前端服务启动成功 (端口: 3000, PID: $WEB_PID)"
        echo $WEB_PID > logs/web-app.pid
        WEB_RUNNING=true
    fi

    if [ "$WEB_RUNNING" = false ]; then
        log_error "前端服务启动失败"
        log_info "查看日志: tail -f logs/web-app.log"
        exit 1
    fi

    log_success "前端服务启动完成！"
    echo ""
    echo "🚀 前端服务地址:"
    echo "   Web应用: http://localhost:4200 (或 http://localhost:3000)"
    echo "   事实核查页面: http://localhost:4200/fact-check"
    echo ""
    echo "📋 日志文件:"
    echo "   前端日志: logs/web-app.log"
    echo ""
    echo "💡 注意: 后端服务未启动，AGUI功能将无法使用"
    echo "⏹️  停止服务: ./scripts/stop-dev.sh 或 Ctrl+C"
    echo ""
}

# 清理函数
cleanup() {
    log_info "清理前端进程..."

    # 停止前端进程
    if [ -f logs/web-app.pid ]; then
        kill $(cat logs/web-app.pid) 2>/dev/null || true
        rm logs/web-app.pid
    fi

    # 强制清理相关进程
    pkill -f "nx run web-app" 2>/dev/null || true
    pkill -f "next dev" 2>/dev/null || true
}

# 信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    echo "🎨 AI投资洞察平台 - 前端开发服务"
    echo "=================================="
    echo "📝 仅启动前端，用于测试UI修复"
    echo ""
    
    check_dependencies
    install_dependencies
    start_frontend
    
    # 保持脚本运行
    log_info "前端服务运行中... 按 Ctrl+C 停止"
    while true; do
        sleep 1
    done
}

# 运行主函数
main "$@"