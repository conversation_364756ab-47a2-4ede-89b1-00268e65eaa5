#!/bin/bash

# AI投资洞察平台 - 重启后端服务脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查脚本是否存在
check_scripts() {
    if [ ! -f "./scripts/stop-backend.sh" ]; then
        log_error "找不到 stop-backend.sh 脚本"
        exit 1
    fi
    
    if [ ! -f "./scripts/start-backend.sh" ]; then
        log_error "找不到 start-backend.sh 脚本"
        exit 1
    fi
}

# 重启后端服务
restart_backend() {
    log_info "重启后端服务..."
    
    # 第一步：停止现有服务
    log_info "步骤 1/2: 停止现有后端服务"
    echo "----------------------------------------"
    ./scripts/stop-backend.sh
    
    # 等待完全停止
    log_info "等待服务完全停止..."
    sleep 3
    
    # 第二步：启动服务
    echo ""
    log_info "步骤 2/2: 启动后端服务"
    echo "----------------------------------------"
    
    # 根据参数决定启动模式
    if [ "${1:-}" = "--daemon" ]; then
        ./scripts/start-backend.sh --daemon
        log_success "后端服务已在后台重启完成"
    else
        log_info "以前台模式重启后端服务..."
        exec ./scripts/start-backend.sh
    fi
}

# 主函数
main() {
    echo "🔄 AI投资洞察平台 - 重启后端服务"
    echo "=================================="
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] || [ ! -f "nx.json" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    check_scripts
    restart_backend "$@"
}

# 运行主函数
main "$@"