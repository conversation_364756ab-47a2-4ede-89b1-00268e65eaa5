#!/bin/bash

# AI投资洞察平台 - 完整安装脚本
# 负责项目的完整环境初始化和依赖安装

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 AI投资洞察平台 - 完整安装${NC}"
echo "=================================="

# 检查是否在项目根目录
if [ ! -f "package.json" ] || [ ! -f "nx.json" ]; then
    echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 1. 环境检查
echo -e "${BLUE}🔍 检查系统环境...${NC}"

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js (推荐版本 18+)${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Node.js: $(node --version)${NC}"

# 检查 pnpm
if ! command -v pnpm &> /dev/null; then
    echo -e "${YELLOW}⚠️  pnpm 未安装，正在安装...${NC}"
    npm install -g pnpm
fi
echo -e "${GREEN}✅ pnpm: $(pnpm --version)${NC}"

# 检查 Python
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 未安装，请先安装 Python 3.8+${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Python: $(python3 --version)${NC}"

# 检查 uv
if ! command -v uv &> /dev/null; then
    echo -e "${YELLOW}⚠️  uv 未安装，正在安装...${NC}"
    curl -LsSf https://astral.sh/uv/install.sh | sh
    source $HOME/.cargo/env
fi
echo -e "${GREEN}✅ uv: $(uv --version)${NC}"

# 2. 清理旧环境（如果存在）
echo -e "${BLUE}🧹 清理旧环境...${NC}"
rm -rf node_modules
rm -rf apps/api-server/.venv
rm -rf libs/*/dist
rm -rf apps/*/dist
rm -rf apps/*/.next
rm -rf logs

# 3. 创建必要目录
echo -e "${BLUE}📁 创建必要目录...${NC}"
mkdir -p logs

# 4. 安装前端依赖
echo -e "${BLUE}📦 安装前端依赖...${NC}"
pnpm install

# 5. 构建共享库 (已移至 webapp 内部，跳过此步骤)
echo -e "${BLUE}🔨 跳过共享库构建 (已集成到 webapp 内部)...${NC}"

# 6. 设置 Python 环境
echo -e "${BLUE}🐍 设置 Python 环境...${NC}"
cd apps/api-server

# 创建虚拟环境
uv venv .venv
source .venv/bin/activate

# 安装 Python 依赖
uv pip install -i https://mirrors.aliyun.com/pypi/simple -e .[dev]

cd ../..

# 7. 设置脚本权限
echo -e "${BLUE}🔐 设置脚本权限...${NC}"
chmod +x scripts/*.sh

# 8. 验证安装
echo -e "${BLUE}✅ 验证安装...${NC}"

# 检查前端依赖
if [ ! -d "node_modules" ]; then
    echo -e "${RED}❌ 前端依赖安装失败${NC}"
    exit 1
fi

# 检查共享库 (已移至 webapp 内部)
if [ ! -d "apps/web-app/src/lib/shared-types" ]; then
    echo -e "${RED}❌ 共享库未找到 (应在 apps/web-app/src/lib/ 目录下)${NC}"
    exit 1
fi

# 检查 Python 环境
if [ ! -d "apps/api-server/.venv" ]; then
    echo -e "${RED}❌ Python 环境创建失败${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 安装完成！${NC}"
echo "=================================="
echo -e "${BLUE}📍 下一步操作:${NC}"
echo "   启动开发环境: ./scripts/start-all.sh"
echo "   启动前端: ./scripts/start-frontend.sh"
echo "   启动后端: ./scripts/start-backend.sh"
echo "   查看服务状态: ./scripts/check-services.sh"
echo ""
echo -e "${BLUE}📍 服务地址:${NC}"
echo "   前端: http://localhost:4200 (或 3000)"
echo "   后端: http://localhost:8000"
echo "   API文档: http://localhost:8000/docs"
echo ""
echo -e "${YELLOW}💡 提示: 如需卸载，请运行 ./scripts/uninstall.sh${NC}"