#!/bin/bash

# AI投资洞察平台 - 重启前端服务脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查脚本是否存在
check_scripts() {
    if [ ! -f "./scripts/stop-frontend.sh" ]; then
        log_error "找不到 stop-frontend.sh 脚本"
        exit 1
    fi
    
    if [ ! -f "./scripts/start-frontend.sh" ]; then
        log_error "找不到 start-frontend.sh 脚本"
        exit 1
    fi
}

# 重启前端服务
restart_frontend() {
    log_info "重启前端服务..."
    
    # 第一步：停止现有服务
    log_info "步骤 1/2: 停止现有前端服务"
    echo "----------------------------------------"
    ./scripts/stop-frontend.sh
    
    # 等待完全停止
    log_info "等待服务完全停止..."
    sleep 3
    
    # 第二步：启动服务
    echo ""
    log_info "步骤 2/2: 启动前端服务"
    echo "----------------------------------------"
    
    # 根据参数决定启动模式
    if [ "${1:-}" = "--daemon" ]; then
        # 后台模式启动（不常用，但提供选项）
        log_info "以后台模式重启前端服务..."
        nohup ./scripts/start-frontend.sh > /dev/null 2>&1 &
        sleep 5
        log_success "前端服务已在后台重启完成"
        
        # 显示服务信息
        echo ""
        echo "🚀 前端服务信息:"
        if curl -s http://localhost:4200/ > /dev/null 2>&1; then
            echo "   Web应用: http://localhost:4200"
        elif curl -s http://localhost:3000/ > /dev/null 2>&1; then
            echo "   Web应用: http://localhost:3000"
        else
            log_warning "前端服务可能还在启动中，请稍后检查"
        fi
        echo "   事实核查页面: http://localhost:4200/fact-check (或 :3000)"
        echo ""
        echo "📋 管理命令:"
        echo "   查看日志: tail -f logs/web-app.log"
        echo "   停止服务: ./scripts/stop-frontend.sh"
        echo ""
    else
        log_info "以前台模式重启前端服务..."
        exec ./scripts/start-frontend.sh
    fi
}

# 主函数
main() {
    echo "🔄 AI投资洞察平台 - 重启前端服务"
    echo "=================================="
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] || [ ! -f "nx.json" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    check_scripts
    restart_frontend "$@"
}

# 运行主函数
main "$@"