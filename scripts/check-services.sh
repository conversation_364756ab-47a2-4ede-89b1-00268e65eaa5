#!/bin/bash

# AI投资洞察平台 - 服务状态检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 状态图标
status_running() {
    echo -e "${GREEN}✅ 运行中${NC}"
}

status_stopped() {
    echo -e "${RED}❌ 已停止${NC}"
}

status_unknown() {
    echo -e "${YELLOW}❓ 状态不明${NC}"
}

# 检查后端服务状态
check_backend_status() {
    echo ""
    echo "🔧 后端服务状态"
    echo "----------------------------------------"
    
    local backend_running=false
    local backend_pid=""
    local backend_port_status=""
    
    # 检查 PID 文件
    if [ -f logs/api-server.pid ]; then
        backend_pid=$(cat logs/api-server.pid)
        if kill -0 $backend_pid 2>/dev/null; then
            echo "   PID 文件: 存在 (PID: $backend_pid) $(status_running)"
        else
            echo "   PID 文件: 存在但进程不存在 $(status_unknown)"
        fi
    else
        echo "   PID 文件: 不存在 $(status_stopped)"
    fi
    
    # 检查端口占用
    if lsof -i :8000 >/dev/null 2>&1; then
        backend_port_status="占用"
        echo "   端口 8000: 被占用 $(status_running)"
    else
        backend_port_status="空闲"
        echo "   端口 8000: 空闲 $(status_stopped)"
    fi
    
    # 检查 HTTP 响应
    if curl -s http://localhost:8000/ > /dev/null 2>&1; then
        backend_running=true
        echo "   HTTP 响应: 正常 $(status_running)"
        
        # 尝试获取健康检查
        if curl -s http://localhost:8000/api/v1/health > /dev/null 2>&1; then
            echo "   健康检查: 通过 $(status_running)"
        else
            echo "   健康检查: 失败 $(status_warning)"
        fi
    else
        echo "   HTTP 响应: 无响应 $(status_stopped)"
        echo "   健康检查: 不可用 $(status_stopped)"
    fi
    
    # 检查相关进程
    local uvicorn_count=$(pgrep -f "uvicorn.*main:app" 2>/dev/null | wc -l)
    local python_count=$(pgrep -f "python.*main.py" 2>/dev/null | wc -l)
    
    echo "   uvicorn 进程: $uvicorn_count 个"
    echo "   Python API 进程: $python_count 个"
    
    # 总体状态
    echo ""
    if [ "$backend_running" = true ]; then
        echo "   📊 总体状态: $(status_running)"
        echo "   🌐 访问地址: http://localhost:8000"
        echo "   📋 API 文档: http://localhost:8000/docs"
    else
        echo "   📊 总体状态: $(status_stopped)"
    fi
    
    return $([ "$backend_running" = true ] && echo 0 || echo 1)
}

# 检查前端服务状态
check_frontend_status() {
    echo ""
    echo "🎨 前端服务状态"
    echo "----------------------------------------"
    
    local frontend_running=false
    local frontend_pid=""
    local frontend_port=""
    
    # 检查 PID 文件
    if [ -f logs/web-app.pid ]; then
        frontend_pid=$(cat logs/web-app.pid)
        if kill -0 $frontend_pid 2>/dev/null; then
            echo "   PID 文件: 存在 (PID: $frontend_pid) $(status_running)"
        else
            echo "   PID 文件: 存在但进程不存在 $(status_unknown)"
        fi
    else
        echo "   PID 文件: 不存在 $(status_stopped)"
    fi
    
    # 检查端口占用
    for port in 3000 4200; do
        if lsof -i :$port >/dev/null 2>&1; then
            echo "   端口 $port: 被占用 $(status_running)"
        else
            echo "   端口 $port: 空闲 $(status_stopped)"
        fi
    done
    
    # 检查 HTTP 响应
    if curl -s http://localhost:4200/ > /dev/null 2>&1; then
        frontend_running=true
        frontend_port="4200"
        echo "   HTTP 响应 (4200): 正常 $(status_running)"
    elif curl -s http://localhost:3000/ > /dev/null 2>&1; then
        frontend_running=true
        frontend_port="3000"
        echo "   HTTP 响应 (3000): 正常 $(status_running)"
    else
        echo "   HTTP 响应: 无响应 $(status_stopped)"
    fi
    
    # 检查相关进程
    local nx_count=$(pgrep -f "nx.*web-app" 2>/dev/null | wc -l)
    local next_count=$(pgrep -f "next.*dev" 2>/dev/null | wc -l)
    local node_count=$(pgrep -f "node.*apps/web-app" 2>/dev/null | wc -l)
    
    echo "   Nx 前端进程: $nx_count 个"
    echo "   Next.js 进程: $next_count 个"
    echo "   Node.js 进程: $node_count 个"
    
    # 总体状态
    echo ""
    if [ "$frontend_running" = true ]; then
        echo "   📊 总体状态: $(status_running)"
        echo "   🌐 访问地址: http://localhost:$frontend_port"
        echo "   🔍 事实核查: http://localhost:$frontend_port/fact-check"
    else
        echo "   📊 总体状态: $(status_stopped)"
    fi
    
    return $([ "$frontend_running" = true ] && echo 0 || echo 1)
}

# 检查系统资源
check_system_resources() {
    echo ""
    echo "💻 系统资源状态"
    echo "----------------------------------------"
    
    # CPU 使用率
    local cpu_usage=$(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
    echo "   CPU 使用率: ${cpu_usage}%"
    
    # 内存使用率
    local memory_info=$(top -l 1 | grep "PhysMem")
    echo "   内存状态: $memory_info"
    
    # 磁盘空间
    local disk_usage=$(df -h . | tail -1 | awk '{print $5}')
    echo "   磁盘使用率: $disk_usage"
    
    # 端口占用汇总
    echo ""
    echo "   📡 端口占用情况:"
    for port in 3000 4200 8000; do
        if lsof -i :$port >/dev/null 2>&1; then
            local process_info=$(lsof -i :$port | grep LISTEN | awk '{print $2 " (" $1 ")"}' | head -1)
            echo "      端口 $port: 被占用 - PID $process_info"
        else
            echo "      端口 $port: 空闲"
        fi
    done
}

# 显示管理命令
show_management_commands() {
    echo ""
    echo "📋 服务管理命令"
    echo "========================================"
    echo ""
    echo "🚀 启动服务:"
    echo "   ./scripts/start-all.sh          # 启动所有服务"
    echo "   ./scripts/start-frontend.sh     # 仅启动前端"
    echo "   ./scripts/start-backend.sh      # 仅启动后端"
    echo ""
    echo "🛑 停止服务:"
    echo "   ./scripts/stop-all.sh           # 停止所有服务"
    echo "   ./scripts/stop-frontend.sh      # 仅停止前端"
    echo "   ./scripts/stop-backend.sh       # 仅停止后端"
    echo ""
    echo "🔄 重启服务:"
    echo "   ./scripts/restart-all.sh        # 重启所有服务"
    echo "   ./scripts/restart-frontend.sh   # 仅重启前端"
    echo "   ./scripts/restart-backend.sh    # 仅重启后端"
    echo ""
    echo "📋 日志查看:"
    echo "   ./scripts/logs-all.sh           # 查看所有日志"
    echo "   ./scripts/logs-frontend.sh      # 仅查看前端日志"
    echo "   ./scripts/logs-backend.sh       # 仅查看后端日志"
    echo ""
}

# 主函数
main() {
    echo "📊 AI投资洞察平台 - 服务状态检查"
    echo "========================================"
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] || [ ! -f "nx.json" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查服务状态
    local backend_ok=false
    local frontend_ok=false
    
    if check_backend_status; then
        backend_ok=true
    fi
    
    if check_frontend_status; then
        frontend_ok=true
    fi
    
    # 检查系统资源
    check_system_resources
    
    # 总体状态摘要
    echo ""
    echo "📈 总体状态摘要"
    echo "========================================"
    
    if [ "$backend_ok" = true ] && [ "$frontend_ok" = true ]; then
        echo "   🎉 所有服务运行正常！"
        log_success "系统状态良好"
    elif [ "$backend_ok" = true ] || [ "$frontend_ok" = true ]; then
        echo "   ⚠️  部分服务运行中"
        log_warning "请检查未运行的服务"
    else
        echo "   🚨 所有服务均已停止"
        log_info "使用 ./scripts/start-all.sh 启动服务"
    fi
    
    # 显示管理命令
    show_management_commands
}

# 运行主函数
main "$@"