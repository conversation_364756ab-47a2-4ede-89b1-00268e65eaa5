#!/bin/bash

# AI投资洞察平台 - 独立启动后端服务脚本
# 基于 FastAPI + uvicorn

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查后端依赖
check_dependencies() {
    log_info "检查后端依赖..."
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查是否在项目根目录
    if [ ! -d "apps/api-server" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查 uv
    if ! command -v uv &> /dev/null; then
        log_warning "uv 未安装，将使用 pip 安装 Python 依赖"
    fi

    log_success "后端依赖检查通过"
}

# 检查服务状态
check_service_status() {
    # 检查 PID 文件
    if [ -f logs/api-server.pid ]; then
        local pid=$(cat logs/api-server.pid)
        if kill -0 $pid 2>/dev/null; then
            log_error "后端服务已在运行 (PID: $pid)"
            log_info "如需重启，请先运行: ./scripts/stop-backend.sh"
            exit 1
        else
            log_warning "清理过期的 PID 文件"
            rm logs/api-server.pid
        fi
    fi
    
    # 检查端口占用
    if lsof -i :8000 >/dev/null 2>&1; then
        log_error "端口 8000 已被占用"
        lsof -i :8000 | grep LISTEN
        exit 1
    fi
}

# 设置 Python 环境
setup_python_environment() {
    log_info "设置 Python 环境..."
    
    cd apps/api-server
    
    # 创建虚拟环境（如果不存在）
    if [ ! -d ".venv" ]; then
        log_info "创建 Python 虚拟环境..."
        if command -v uv &> /dev/null; then
            uv venv .venv
        else
            python3 -m venv .venv
        fi
    fi
    
    # 激活虚拟环境
    source .venv/bin/activate
    
    # 检查并安装依赖
    if command -v uv &> /dev/null; then
        log_info "使用 uv 安装 Python 依赖..."
        uv pip install -e .[dev]
    else
        log_info "使用 pip 安装 Python 依赖..."
        pip install -e .[dev]
    fi
    
    cd ../..
    
    log_success "Python 环境设置完成"
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 进入后端目录
    cd apps/api-server
    source .venv/bin/activate
    
    # 启动 FastAPI 服务
    log_info "启动 FastAPI 服务器 (端口: 8000)..."
    python3 -m uvicorn main:app --reload --host 0.0.0.0 --port 8000 > ../../logs/api-server.log 2>&1 &
    local backend_pid=$!
    
    # 返回项目根目录
    cd ../..
    
    # 保存 PID
    echo $backend_pid > logs/api-server.pid
    
    # 等待服务启动
    log_info "等待后端服务启动..."
    local max_attempts=10
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost:8000/ > /dev/null 2>&1; then
            log_success "后端服务启动成功 (PID: $backend_pid)"
            break
        fi
        
        # 检查进程是否还在运行
        if ! kill -0 $backend_pid 2>/dev/null; then
            log_error "后端服务启动失败，进程已退出"
            log_info "查看错误日志: tail -f logs/api-server.log"
            rm logs/api-server.pid 2>/dev/null || true
            exit 1
        fi
        
        # 检查日志中的错误信息
        if [ -f logs/api-server.log ]; then
            # 检查常见的错误模式
            if grep -q "ModuleNotFoundError\|ImportError\|SyntaxError\|AttributeError\|NameError" logs/api-server.log; then
                log_error "后端服务启动失败，发现代码错误"
                echo ""
                log_info "错误详情:"
                tail -10 logs/api-server.log | grep -E "(ModuleNotFoundError|ImportError|SyntaxError|AttributeError|NameError|Error:|Exception:)" || tail -5 logs/api-server.log
                echo ""
                kill $backend_pid 2>/dev/null || true
                rm logs/api-server.pid 2>/dev/null || true
                exit 1
            fi
            
            # 检查端口占用错误
            if grep -q "Address already in use\|port.*already in use" logs/api-server.log; then
                log_error "后端服务启动失败，端口已被占用"
                echo ""
                log_info "端口占用详情:"
                lsof -i :8000 | grep LISTEN || echo "无法获取端口占用信息"
                echo ""
                kill $backend_pid 2>/dev/null || true
                rm logs/api-server.pid 2>/dev/null || true
                exit 1
            fi
            
            # 检查权限错误
            if grep -q "Permission denied\|PermissionError" logs/api-server.log; then
                log_error "后端服务启动失败，权限不足"
                echo ""
                log_info "权限错误详情:"
                tail -5 logs/api-server.log | grep -E "(Permission|权限)" || tail -3 logs/api-server.log
                echo ""
                kill $backend_pid 2>/dev/null || true
                rm logs/api-server.pid 2>/dev/null || true
                exit 1
            fi
        fi
        
        sleep 1
        attempt=$((attempt + 1))
        echo -n "."
    done
    
    if [ $attempt -eq $max_attempts ]; then
        log_error "后端服务启动超时"
        kill $backend_pid 2>/dev/null || true
        rm logs/api-server.pid 2>/dev/null || true
        log_info "查看错误日志: tail -f logs/api-server.log"
        exit 1
    fi
    
    echo ""
    log_success "后端服务启动完成！"
    echo ""
    echo "🚀 后端服务信息:"
    echo "   API 服务: http://localhost:8000"
    echo "   API 文档: http://localhost:8000/docs"
    echo "   健康检查: http://localhost:8000/api/v1/health"
    echo ""
    echo "📋 管理命令:"
    echo "   查看日志: tail -f logs/api-server.log"
    echo "   停止服务: ./scripts/stop-backend.sh"
    echo "   重启服务: ./scripts/restart-backend.sh"
    echo ""
}

# 清理函数
cleanup() {
    if [ -f logs/api-server.pid ]; then
        local pid=$(cat logs/api-server.pid)
        if kill -0 $pid 2>/dev/null; then
            log_info "清理后端进程 (PID: $pid)..."
            kill $pid 2>/dev/null || true
        fi
        rm logs/api-server.pid
    fi
}

# 信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    echo "🔧 AI投资洞察平台 - 后端服务启动"
    echo "=================================="
    
    check_dependencies
    check_service_status
    setup_python_environment
    start_backend
    
    # 保持脚本运行（可选）
    if [ "${1:-}" = "--daemon" ]; then
        log_info "后端服务已在后台运行"
    else
        log_info "后端服务运行中... 按 Ctrl+C 停止"
        while true; do
            # 检查服务是否还在运行
            if [ -f logs/api-server.pid ]; then
                local pid=$(cat logs/api-server.pid)
                if ! kill -0 $pid 2>/dev/null; then
                    log_error "后端服务意外停止"
                    break
                fi
            else
                log_error "PID 文件丢失，服务可能已停止"
                break
            fi
            sleep 5
        done
    fi
}

# 运行主函数
main "$@"