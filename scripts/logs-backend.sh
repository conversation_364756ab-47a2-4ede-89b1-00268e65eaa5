#!/bin/bash

# AI投资洞察平台 - 查看后端日志脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "📋 使用说明:"
    echo "   ./scripts/logs-backend.sh [选项]"
    echo ""
    echo "📋 选项:"
    echo "   --tail, -t [行数]    实时查看最新日志 (默认50行)"
    echo "   --follow, -f         持续跟踪日志输出"
    echo "   --head, -h [行数]    查看日志开头 (默认50行)"
    echo "   --grep, -g [关键词]  搜索包含关键词的日志"
    echo "   --error, -e          仅显示错误日志"
    echo "   --info, -i           仅显示信息日志"
    echo "   --debug, -d          仅显示调试日志"
    echo "   --all, -a            显示所有历史日志"
    echo "   --help               显示此帮助信息"
    echo ""
    echo "📋 示例:"
    echo "   ./scripts/logs-backend.sh --tail      # 查看最新50行日志"
    echo "   ./scripts/logs-backend.sh -f          # 实时跟踪日志"
    echo "   ./scripts/logs-backend.sh -g \"ERROR\" # 搜索错误日志"
}

# 检查日志文件
check_log_file() {
    if [ ! -f "logs/api-server.log" ]; then
        log_warning "后端日志文件不存在: logs/api-server.log"
        echo ""
        echo "可能的原因:"
        echo "  1. 后端服务尚未启动"
        echo "  2. 后端服务启动失败"
        echo "  3. 日志文件路径配置错误"
        echo ""
        echo "建议操作:"
        echo "  • 检查服务状态: ./scripts/check-services.sh"
        echo "  • 启动后端服务: ./scripts/start-backend.sh"
        echo ""
        return 1
    fi
    return 0
}

# 显示日志文件信息
show_log_info() {
    if [ -f "logs/api-server.log" ]; then
        local file_size=$(ls -lh logs/api-server.log | awk '{print $5}')
        local line_count=$(wc -l < logs/api-server.log)
        local last_modified=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" logs/api-server.log)
        
        echo ""
        echo "📄 后端日志文件信息:"
        echo "   文件路径: logs/api-server.log"
        echo "   文件大小: $file_size"
        echo "   行数: $line_count"
        echo "   最后修改: $last_modified"
        echo ""
    fi
}

# 主日志查看函数
view_logs() {
    local action="tail"
    local lines=50
    local keyword=""
    local log_level=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --tail|-t)
                action="tail"
                if [[ $2 =~ ^[0-9]+$ ]]; then
                    lines=$2
                    shift
                fi
                shift
                ;;
            --follow|-f)
                action="follow"
                shift
                ;;
            --head|-h)
                action="head"
                if [[ $2 =~ ^[0-9]+$ ]]; then
                    lines=$2
                    shift
                fi
                shift
                ;;
            --grep|-g)
                action="grep"
                keyword="$2"
                shift 2
                ;;
            --error|-e)
                log_level="ERROR"
                action="grep"
                keyword="ERROR"
                shift
                ;;
            --info|-i)
                log_level="INFO"
                action="grep"
                keyword="INFO"
                shift
                ;;
            --debug|-d)
                log_level="DEBUG"
                action="grep"
                keyword="DEBUG"
                shift
                ;;
            --all|-a)
                action="all"
                shift
                ;;
            --help)
                show_usage
                return 0
                ;;
            *)
                log_error "未知选项: $1"
                show_usage
                return 1
                ;;
        esac
    done
    
    # 检查日志文件
    if ! check_log_file; then
        return 1
    fi
    
    # 显示日志文件信息
    show_log_info
    
    # 执行相应的日志查看操作
    case $action in
        tail)
            log_info "显示后端日志最新 $lines 行 (按 Ctrl+C 退出)"
            echo "========================================"
            tail -n $lines logs/api-server.log
            ;;
        follow)
            log_info "实时跟踪后端日志 (按 Ctrl+C 退出)"
            echo "========================================"
            tail -f logs/api-server.log
            ;;
        head)
            log_info "显示后端日志开头 $lines 行"
            echo "========================================"
            head -n $lines logs/api-server.log
            ;;
        grep)
            if [ -n "$keyword" ]; then
                log_info "搜索包含 '$keyword' 的日志条目"
                echo "========================================"
                grep --color=always -i "$keyword" logs/api-server.log || {
                    log_warning "未找到包含 '$keyword' 的日志条目"
                }
            else
                log_error "搜索关键词不能为空"
                return 1
            fi
            ;;
        all)
            log_info "显示完整的后端日志"
            echo "========================================"
            cat logs/api-server.log
            ;;
    esac
    
    # 显示额外信息
    echo ""
    echo "📋 其他日志操作:"
    echo "   实时跟踪: ./scripts/logs-backend.sh -f"
    echo "   搜索错误: ./scripts/logs-backend.sh -e"
    echo "   查看服务状态: ./scripts/check-services.sh"
    echo ""
}

# 主函数
main() {
    echo "📋 AI投资洞察平台 - 后端日志查看器"
    echo "========================================"
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] || [ ! -f "nx.json" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 如果没有参数，默认显示最新日志
    if [ $# -eq 0 ]; then
        view_logs --tail
    else
        view_logs "$@"
    fi
}

# 运行主函数
main "$@"