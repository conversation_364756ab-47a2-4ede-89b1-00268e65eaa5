{"name": "yai-investor-insight", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nx dev:all", "build": "nx run-many -t build", "build:libs": "nx run-many --target=build --projects=shared-types,api-client --parallel", "start": "nx run-many --target=start --projects=api-server,web-app --parallel", "test": "nx run-many -t test", "lint": "nx run-many -t lint"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.13.0", "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@nx/js": "^21.2.4", "@nx/next": "^21.2.4", "@nx/node": "^21.2.4", "@nx/react": "21.2.4", "@nx/rollup": "21.2.4", "@nx/workspace": "^21.2.4", "@rollup/plugin-url": "^8.0.2", "@svgr/rollup": "^8.1.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/node": "^20.0.0", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "core-js": "^3.36.1", "nx": "^21.2.4", "prettier": "^2.6.2", "rollup": "^4.14.0", "tslib": "^2.3.0", "typescript": "~5.8.2"}, "dependencies": {"clsx": "^2.1.1", "react": "19.0.0", "react-dom": "19.0.0", "tailwind-merge": "^3.3.1"}}