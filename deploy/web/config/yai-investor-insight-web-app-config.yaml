apiVersion: v1
kind: ConfigMap
metadata:
  name: yai-investor-insight-web-app-config
data:
  # 应用基础配置
  NEXT_PUBLIC_API_URL: "http://yai-investor-insight-api-server-service"
  NEXT_PUBLIC_APP_VERSION: "1.0.0"
  NEXT_PUBLIC_SERVICE_NAME: "yai-investor-insight-web"
  NODE_ENV: "production"

  # 日志配置 - loglayer-support v0.7.9
  NEXT_PUBLIC_SLS_ENABLED: "true"
  NEXT_PUBLIC_SLS_ENDPOINT: "https://cn-beijing.log.aliyuncs.com"
  NEXT_PUBLIC_SLS_ACCESS_KEY_ID: "LTAI5tPrkKMrPLW1XjbBxwhm"
  NEXT_PUBLIC_SLS_ACCESS_KEY_SECRET: "******************************"
  NEXT_PUBLIC_SLS_PROJECT: "yai-log-test"
  NEXT_PUBLIC_SLS_LOGSTORE: "app-log"
  NEXT_PUBLIC_SLS_REGION: "cn-beijing"
  NEXT_PUBLIC_SLS_TOPIC: "default"

  # K8s环境标识
  KUBERNETES_DEPLOYMENT: "true"
  DEPLOYMENT_ENVIRONMENT: "k8s"
