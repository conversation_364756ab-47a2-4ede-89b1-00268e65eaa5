apiVersion: apps/v1
kind: Deployment
metadata:
  name: yai-investor-insight-api-server
  namespace: default
  labels:
    app: yai-investor-insight-api-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: yai-investor-insight-api-server
  template:
    metadata:
      labels:
        app: yai-investor-insight-api-server
    spec:
      containers:
      - name: yai-investor-insight-api-server
        image: ${SERVICE_IMAGE}
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: yai-investor-insight-api-server-config
        # 保留原有的 SLS 配置
        - configMapRef:
            name: sls-config
        - secretRef:
            name: sls-secret
        resources:
          requests:
            memory: "100Mi"
            cpu: "50m"
          limits:
            cpu: "300m"
            memory: "1Gi"

---
apiVersion: v1
kind: Service
metadata:
  name: yai-investor-insight-api-server-service
  namespace: default
  labels:
    app: yai-investor-insight-api-server
spec:
  selector:
    app: yai-investor-insight-api-server
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
  type: ClusterIP