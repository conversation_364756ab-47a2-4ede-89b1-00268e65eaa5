# Dockerfile
FROM beiyikeji-registry.cn-beijing.cr.aliyuncs.com/common/python-base-image:3.12-slim-tools

# 使用构建参数来指定应用程序的路径
ARG APP_PATH=apps/api-server

WORKDIR /app

# 复制依赖文件
# 注意：这里只复制依赖文件，以便利用 Docker 的层缓存
COPY ${APP_PATH}/pyproject.toml ${APP_PATH}/poetry.lock* /app/

# 使用 uv 安装依赖，配置阿里云镜像源
# --system 表示安装到全局环境中，这在 Docker 容器中是推荐的做法
# 在 /app 目录下运行，uv 会自动找到 pyproject.toml
RUN uv pip install --system . --index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com

# 复制应用代码
# 注意：这里只复制应用代码，不覆盖已经复制的 pyproject.toml
COPY ${APP_PATH}/ /app/

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]