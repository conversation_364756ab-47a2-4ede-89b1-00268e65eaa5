# Dockerfile
FROM beiyikeji-registry.cn-beijing.cr.aliyuncs.com/common/python-base-image:3.12-slim-tools

# 使用构建参数来指定应用程序的路径
ARG APP_PATH=apps/api-server

# 设置工作目录为项目根目录，保持与本地开发环境一致的相对路径结构
WORKDIR /app

# 复制整个项目结构，保持相对路径的一致性
COPY libs/ /app/libs/
COPY ${APP_PATH}/ /app/apps/api-server/

# 设置环境变量使用系统 Python，这在 Docker 容器中是推荐的做法
ENV UV_SYSTEM_PYTHON=1

# 首先安装 shared-bs-llm 库
RUN cd /app/libs/shared-bs-llm && \
    uv pip install -e . --index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com

# 临时修改 pyproject.toml 中的相对路径为绝对路径，解决 uv 构建时的路径解析问题
RUN cd /app/apps/api-server && \
    cp pyproject.toml pyproject.toml.backup && \
    sed -i 's|path = "../../libs/shared-bs-llm"|path = "/app/libs/shared-bs-llm"|g' pyproject.toml && \
    sed -i 's|path = "../../libs/demo-feature-bs"|path = "/app/libs/demo-feature-bs"|g' pyproject.toml

# 然后安装 api-server 及其依赖
RUN cd /app/apps/api-server && \
    uv pip install -e . --index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com

# 恢复原始的 pyproject.toml（可选，因为容器中不需要开发时的相对路径）
# RUN cd /app/apps/api-server && mv pyproject.toml.backup pyproject.toml

# 暴露端口
EXPOSE 8000

# 设置工作目录为应用目录
WORKDIR /app/apps/api-server

# 启动命令 - 使用 uvicorn 启动 FastAPI 应用
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]