{
  "folders": [
    {
      "name": "🏠 Root",
      "path": "."
    },
    {
      "name": "🚀 API Server",
      "path": "./apps/api-server"
    },
    {
      "name": "🌐 Web App", 
      "path": "./apps/web-app"
    },
    {
      "name": "📦 Shared Frontend API Client",
      "path": "./libs/api-client"
    },
    {
      "name": "📦 Shared Frontend Types",
      "path": "./libs/shared-types"
    },
    {
      "name": "📦 Shared Frontend Kit",
      "path": "./libs/shared-fe-kit"
    },
    {
      "name": "📦 Shared Backend Core",
      "path": "./libs/shared-bs-core"
    },
    {
      "name": "📦 Shared Backend LLM",
      "path": "./libs/shared-bs-llm"
    },
    {
      "name": "📦 Demo Feature Frontend",
      "path": "./libs/demo-feature-fe"
    },
    {
      "name": "📦 Demo Feature Backend",
      "path": "./libs/demo-feature-bs"
    }
  ],
  "settings": {
    // Python 配置
    "python.defaultInterpreterPath": "./apps/api-server/.venv/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.analysis.autoImportCompletions": true,
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.extraPaths": [
      "./apps/api-server/src"
    ],
    
    // 搜索排除 - 提升搜索速度
    "search.exclude": {
      "**/node_modules": true,
      "**/.venv": true,
      "**/.nx": true,
      "**/dist": true,
      "**/.next": true,
      "**/__pycache__": true,
      "**/.ruff_cache": true,
      "**/pnpm-lock.yaml": true
    },
    
    // 文件监视排除 - 减少 CPU 占用
    "files.watcherExclude": {
      "**/.venv/**": true,
      "**/node_modules/**": true,
      "**/.nx/**": true,
      "**/dist/**": true,
      "**/.next/**": true,
      "**/__pycache__/**": true,
      "**/.ruff_cache/**": true
    },
    "nxConsole.generateAiAgentRules": true
  },
  "extensions": {
    "recommendations": [
      "ms-python.python",
      "ms-python.debugpy",
      "ms-vscode.vscode-typescript-next",
      "bradlc.vscode-tailwindcss",
      "nrwl.angular-console"
    ]
  }
}