# AI投资洞察平台 🚀

> AI驱动的投资研究平台，提供事件驱动的智能分析与人机协作工作流

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/your-repo/actions)
[![Version](https://img.shields.io/badge/version-v2.0-orange.svg)](docs/PROJECT_MASTER_PLAN.md)

---

## 🎯 项目概览

AI投资洞察平台是一个现代化的投资研究工具，通过AI分析引擎为投资者提供实时、准确的市场洞察。平台采用事件驱动架构，结合人机协作工作流，实现30秒内完成投资分析。

### 核心特性
- 🤖 **AI驱动分析**: 基于LangGraph的智能投资分析引擎
- ⚡ **实时反馈**: SSE实时通信，30秒内完成分析
- 🔄 **人机协作**: AI分析 + 人工验证的混合工作流  
- 📊 **数据可视化**: 股票图表、财务分析图表展示
- 🏗️ **企业级**: 99.9%可用性，支持1000+任务/分钟

---

## 🚀 快速开始

### 前置要求
- Node.js 18+ 
- Python 3.11+
- PostgreSQL 15+
- pnpm 9+

### 安装与运行

```bash
# 1. 克隆项目
git clone <repository-url>
cd yai-investor-insight

# 2. 安装依赖
pnpm install

# 3. 启动开发服务器
pnpm nx dev web-app      # 前端 → http://localhost:4200
pnpm nx dev api-server   # 后端 → http://localhost:8000

# 4. 同时启动前后端
pnpm nx dev:all
```

### Docker 快速部署
```bash
# 构建并启动所有服务
pnpm nx docker:build
pnpm nx docker:up

# 查看日志
pnpm nx docker:logs
```

---

## 🏗️ 项目架构

### Monorepo 结构
```
yai-investor-insight/
├── apps/
│   ├── web-app/           # Next.js 15 + TypeScript 前端
│   └── api-server/        # FastAPI + Python 后端
├── libs/
│   ├── shared-types/      # 共享 TypeScript 类型
│   ├── api-client/        # API 客户端库
│   ├── ui-components/     # 共享 UI 组件  
│   └── utils/             # 工具函数库
├── tools/
│   ├── docker/           # Docker 配置文件
│   └── scripts/          # 构建和部署脚本
└── docs/                 # 项目文档
```

### 技术栈
| 层级 | 技术 | 版本 |
|------|------|------|
| 🎨 前端 | Next.js + TypeScript | 15.x + 5.x |
| ⚙️ 后端 | FastAPI + Python | 3.11+ |
| 🗄️ 数据库 | PostgreSQL + TortoiseORM | 15+ |
| 🔧 构建 | Nx Monorepo | 21.x |
| 📦 容器化 | Docker + Docker Compose | Latest |

---

## 🛠️ 开发命令

### 项目管理
```bash
# 查看所有项目
pnpm nx show projects

# 查看项目依赖图  
pnpm nx graph

# 运行特定项目
pnpm nx <target> <project-name>
```

### 构建与测试
```bash
# 构建所有项目
pnpm nx run-many -t build

# 运行所有测试
pnpm nx run-many -t test

# 代码检查和格式化
pnpm nx run-many -t lint
pnpm nx run-many -t type-check
```

### 增量构建 (推荐)
```bash
# 只构建受影响的项目
pnpm nx affected -t build

# 只测试受影响的项目  
pnpm nx affected -t test

# 基于特定分支比较
pnpm nx affected -t build --base=main
```

---

## 📊 项目状态

### ✅ 已完成
- [x] **Nx Monorepo 迁移** - 统一项目管理和构建系统
- [x] **技术架构设计** - PostgreSQL统一数据/任务队列/通知
- [x] **CTO技术审核** - 核心技术方案已获批准
- [x] **Docker集成** - 完整的容器化配置

### 📅 开发路线图
| 阶段 | 时间 | 状态 | 描述 |
|------|------|------|------|
| Phase 1 | Week 1-2 | 🚧 **进行中** | 核心功能开发 |
| Phase 2 | Weeks 3-5 | ⏳ 等待 | AI分析引擎 |
| Phase 3 | Weeks 5-7 | ⏳ 等待 | 企业级特性 |
| Phase 4 | Weeks 8-10 | ⏳ 等待 | 高级功能 |
| Phase 5 | Weeks 11-12 | ⏳ 等待 | 高级功能 |
| Phase 6 | Week 13 | ⏳ 等待 | 生产准备 |

---

## 📚 文档资源

### 核心文档
- 📋 **[项目主计划](docs/PROJECT_MASTER_PLAN.md)** - 完整项目方案与路线图
- 🎨 **[产品设计文档](docs/产品设计文档.md)** - 产品需求与用户体验设计
- ⚙️ **[技术实现方案](docs/技术实现方案.md)** - 详细技术架构与实现

### 开发文档
- 🔧 **[开发环境配置](CLAUDE.md)** - Monorepo开发提醒
- 🐳 **[Docker部署指南](tools/docker/)** - 容器化部署说明

---

## 📞 联系方式

- **项目负责人**: [CTO]
- **技术问题**: 请创建 GitHub Issue
- **文档问题**: 查看 [docs/](docs/) 目录

---

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

*最后更新: 2025-07-21*