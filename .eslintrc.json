{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "scope:shared", "onlyDependOnLibsWithTags": ["scope:shared"]}, {"sourceTag": "type:fe", "onlyDependOnLibsWithTags": ["type:fe", "scope:shared"]}, {"sourceTag": "type:bs", "onlyDependOnLibsWithTags": ["type:bs", "scope:shared"]}, {"sourceTag": "type:app", "onlyDependOnLibsWithTags": ["*"]}, {"sourceTag": "scope:demo-feature", "onlyDependOnLibsWithTags": ["scope:shared", "scope:demo-feature"]}, {"sourceTag": "scope:user-auth", "onlyDependOnLibsWithTags": ["scope:shared", "scope:user-auth"]}, {"sourceTag": "scope:market-data", "onlyDependOnLibsWithTags": ["scope:shared", "scope:market-data"]}, {"sourceTag": "scope:portfolio-management", "onlyDependOnLibsWithTags": ["scope:shared", "scope:portfolio-management", "scope:market-data"]}, {"sourceTag": "scope:news-analysis", "onlyDependOnLibsWithTags": ["scope:shared", "scope:news-analysis", "scope:market-data"]}, {"sourceTag": "scope:company-financials", "onlyDependOnLibsWithTags": ["scope:shared", "scope:company-financials", "scope:market-data"]}]}]}}]}