{"version": "2.0.0", "tasks": [{"label": "start-all-services", "dependsOrder": "parallel", "dependsOn": ["start-api-server", "start-web-app"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}}, {"label": "start-api-server", "type": "shell", "command": "pnpm", "args": ["nx", "dev", "api-server"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "isBackground": true}, {"label": "start-web-app", "type": "shell", "command": "pnpm", "args": ["nx", "dev", "web-app"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "isBackground": true}, {"label": "install-all-deps", "type": "shell", "command": "pnpm", "args": ["install"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}}, {"label": "build-all", "type": "shell", "command": "pnpm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}}, {"label": "test-all", "type": "shell", "command": "pnpm", "args": ["run", "test"], "options": {"cwd": "${workspaceFolder}"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}}]}