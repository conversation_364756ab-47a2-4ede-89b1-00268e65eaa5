{"version": "0.2.0", "configurations": [{"name": "🐍 Python API Server", "type": "debugpy", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"], "cwd": "${workspaceFolder}/apps/api-server", "env": {"PYTHONPATH": "${workspaceFolder}/apps/api-server/src", "LOG_LEVEL": "DEBUG", "ENVIRONMENT": "development"}, "console": "integratedTerminal", "justMyCode": false, "python": "${workspaceFolder}/apps/api-server/.venv/bin/python"}, {"name": "⚡ Next.js Web App", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/apps/web-app", "runtimeExecutable": "pnpm", "runtimeArgs": ["dev"], "console": "integratedTerminal", "env": {"NODE_OPTIONS": "--inspect=9229"}, "port": 9229, "restart": true, "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/apps/web-app/**", "!**/node_modules/**"], "skipFiles": ["<node_internals>/**", "**/node_modules/**"]}, {"name": "🔗 Attach to Python", "type": "debugpy", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}/apps/api-server/src", "remoteRoot": "/app/src"}], "justMyCode": false}, {"name": "🔗 Attach to Next.js", "type": "node", "request": "attach", "port": 9229, "localRoot": "${workspaceFolder}/apps/web-app", "remoteRoot": "/app"}], "compounds": [{"name": "🔥 Full Stack Debug", "configurations": ["🐍 Python API Server", "⚡ Next.js Web App"], "stopAll": true, "presentation": {"hidden": false, "group": "Full Stack", "order": 1}}, {"name": "🔗 Attach to All Services", "configurations": ["🔗 Attach to Python", "🔗 Attach to Next.js"], "stopAll": true, "presentation": {"hidden": false, "group": "Full Stack", "order": 2}}]}