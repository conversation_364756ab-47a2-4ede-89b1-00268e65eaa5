{"permissions": {"allow": ["Bash(npm view:*)", "Bash(pip search:*)", "Bash(pip index versions:*)", "Bash(npm install:*)", "Bash(pnpm init:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "Bash(pnpm create:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(cp:*)", "Bash(ls:*)", "Bash(pnpm add:*)", "Bash(pnpm list:*)", "Bash(find:*)", "WebFetch(domain:pypi.org)", "<PERSON><PERSON>(pip show:*)", "Bash(packages/py-investor-insight/.venv/bin/pip install -r packages/py-investor-insight/requirements.txt)", "Bash(packages/py-investor-insight/.venv/bin/pip install yai-nexus-agentkit[all]==0.2.6 fastapi uvicorn[standard] python-dotenv pydantic openai anthropic langchain langchain-openai langchain-anthropic)", "Bash(packages/py-investor-insight/.venv/bin/pip install 'yai-nexus-agentkit[all]==0.2.6' fastapi 'uvicorn[standard]' python-dotenv pydantic openai anthropic langchain langchain-openai langchain-anthropic)", "Bash(packages/py-investor-insight/.venv/bin/pip install yfinance requests beautifulsoup4 lxml)", "Bash(.venv/bin/python:*)", "Bash(packages/py-investor-insight/.venv/bin/pip list)", "Bash(packages/py-investor-insight/.venv/bin/pip install yfinance pandas)", "Bash(packages/py-investor-insight/.venv/bin/python:*)", "Bash(.venv/bin/pip:*)", "Bash(packages/py-investor-insight/.venv/bin/pip install --no-cache-dir yfinance langchain-core)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(gtimeout:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker compose:*)", "Bash(npx nx:*)", "<PERSON><PERSON>(nx show:*)", "Bash(nx graph:*)", "Bash(nx run-many:*)", "Bash(pnpm nx show:*)", "Bash(pnpm nx run-many:*)", "Bash(pnpm nx lint:*)", "Bash(pnpm nx build:*)", "Bash(pnpm nx graph:*)", "Bash(pnpm nx:*)", "<PERSON><PERSON>(cat:*)", "Bash(brew install:*)", "Bash(brew services start:*)", "Bash(export:*)", "<PERSON><PERSON>(createdb:*)", "Bash(psql:*)", "Bash(pnpm install:*)", "Bash(pnpm build:*)", "<PERSON><PERSON>(true)", "mcp__puppeteer__puppeteer_navigate", "Bash(pnpm dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(npx:*)", "Bash(node:*)", "Bash(npm run build:*)", "mcp__filesystem__list_directory", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "mcp__filesystem__directory_tree", "mcp__filesystem__read_multiple_files", "mcp__filesystem__read_file", "mcp__sequential-thinking__sequentialthinking", "Bash(tree:*)", "mcp__filesystem__create_directory", "mcp__filesystem__write_file", "mcp__filesystem__edit_file", "mcp__filesystem__move_file", "<PERSON><PERSON>(uvicorn:*)", "Bash(curl -X POST \"http://localhost:8000/api/v1/fact-check/test\" -H \"Content-Type: application/json\")", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/dev.sh:*)", "Bash(./scripts/start-dev.sh:*)", "Bash(./scripts/stop-dev.sh:*)", "<PERSON><PERSON>(pkill:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_close", "Bash(./scripts/start-frontend.sh:*)", "Bash(grep:*)", "mcp__playwright__browser_type", "mcp__playwright__browser_click", "Bash(./scripts/check-services.sh:*)", "Bash(./scripts/stop-all.sh:*)", "Bash(./scripts/start-backend.sh:*)", "Bash(git fetch:*)", "Bash(git merge:*)", "WebFetch(domain:github.com)", "Bash(git checkout:*)", "Bash(pnpm store prune:*)", "Bash(uv add:*)", "<PERSON><PERSON>(uv run:*)", "Bash(./scripts/restart-all.sh:*)"], "deny": []}}