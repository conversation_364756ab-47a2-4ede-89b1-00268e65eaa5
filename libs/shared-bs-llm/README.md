# Python Shared Libraries

这是 YAI Investor Insight 项目的 Python 共享库，包含了可在多个 Python 应用间复用的工具和模块。

## 📁 项目结构

```
libs/shared-bs-llm/
├── src/shared_bs_llm/          # 源代码
│   ├── llm/                   # LLM 相关工具
│   │   ├── client_utils.py    # LLM 客户端工具
│   │   ├── client_enum.py     # LLM 客户端枚举
│   │   ├── model_config.py    # LLM 配置模型
│   │   └── snow_flake.py      # Snowflake ID 生成器
│   └── utils/                 # 通用工具
│       └── common.py          # 通用函数
├── tests/                     # 测试文件
├── examples/                  # 使用示例
├── pyproject.toml            # Python 包配置
├── project.json              # Nx 项目配置
└── README.md                 # 本文件
```

## 🚀 快速开始

### 安装

在项目根目录下运行：

```bash
# 构建 shared-bs-llm 库
pnpm nx build shared-bs-llm

# 或者直接安装到开发环境
cd libs/shared-bs-llm
pip install -e .
```

### 基本使用

```python
# 导入 LLM 相关模块
from shared_bs_llm.llm import LLMClientUtils, LLMClientEnum, LLMNacosConfig, Snowflake

# 导入工具函数
from shared_bs_llm.utils import setup_logger

# 创建 Snowflake ID 生成器
sf = Snowflake(worker_id=1, datacenter_id=1)
id = sf.generate()

# 获取 LLM 配置
config = LLMNacosConfig.get_instance()
available_llms = config.get_available_llms()

# 创建 LLM 实例
if available_llms:
    llm = LLMClientUtils.create_llm(
        model_type=LLMClientEnum.ZHIPU_GLM_4,
        streaming=False,
        temperature=0.7
    )
```

## 🔧 配置

### 环境变量

支持以下 LLM 提供商的配置：

```bash
# OpenAI
export OPENAI_API_KEY="your-openai-key"
export OPENAI_BASE_URL="https://api.openai.com/v1"  # 可选

# 智谱 AI
export ZHIPU_API_KEY="your-zhipu-key"
export ZHIPU_BASE_URL="https://open.bigmodel.cn/api/paas/v4/"  # 可选

# 通义千问
export QWEN_API_KEY="your-qwen-key"
export QWEN_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"  # 可选

# 豆包
export DOUBAO_API_KEY="your-doubao-key"
export DOUBAO_BASE_URL="https://ark.cn-beijing.volces.com/api/v3/"  # 可选

# DeepSeek
export DEEPSEEK_API_KEY="your-deepseek-key"
export DEEPSEEK_BASE_URL="https://api.deepseek.com/v1"  # 可选

# OpenRouter
export OPENROUTER_API_KEY="your-openrouter-key"
export OPENROUTER_BASE_URL="https://openrouter.ai/api/v1"  # 可选

# Anthropic
export ANTHROPIC_API_KEY="your-anthropic-key"
export ANTHROPIC_BASE_URL="https://api.anthropic.com"  # 可选

# Perplexity
export PERPLEXITY_API_KEY="your-perplexity-key"
export PERPLEXITY_BASE_URL="https://api.perplexity.ai"  # 可选

# Langfuse (可选)
export LANGFUSE_PUBLIC_KEY="your-langfuse-public-key"
export LANGFUSE_SECRET_KEY="your-langfuse-secret-key"
export LANGFUSE_HOST="your-langfuse-host"
```

## 📚 API 文档

### LLM 模块

#### LLMClientUtils

主要的 LLM 客户端工具类：

- `create_llm(model_type, streaming=False, temperature=0.7, ...)` - 创建 LLM 实例
- `async_create_llm(...)` - 异步创建 LLM 实例

#### LLMClientEnum

支持的 LLM 模型枚举：

- `ZHIPU_GLM_4` - 智谱 AI GLM-4
- `QWEN_32B` - 通义千问 32B
- `DOUBAO_1_5_PRO_256K` - 豆包 1.5 Pro
- `DEEPSEEK_EP` - DeepSeek
- `CLAUDE_SONNET_4` - Claude Sonnet 4
- 等等...

#### LLMNacosConfig

配置管理类：

- `get_instance()` - 获取单例实例
- `get_config(llm_type)` - 获取指定 LLM 配置
- `get_available_llms()` - 获取可用 LLM 列表
- `get_primary_llm()` - 获取主要 LLM 配置

#### Snowflake

Snowflake ID 生成器：

- `__init__(worker_id, datacenter_id, sequence=0)` - 初始化
- `generate()` - 生成唯一 ID

### 工具模块

#### 通用工具

- `setup_logger(name, level=logging.INFO)` - 设置日志记录器
- `safe_get(obj, key, default=None)` - 安全获取对象属性

## 🧪 测试

运行测试：

```bash
# 运行所有测试
pnpm nx test shared-bs-llm

# 或者直接使用 pytest
cd libs/shared-bs-llm
PYTHONPATH=src pytest tests/ -v
```

## 📖 示例

查看 `examples/basic_usage.py` 了解完整的使用示例：

```bash
cd libs/shared-bs-llm
python examples/basic_usage.py
```

## 🔄 迁移说明

这个库是从 `apps/api-server/src/api/demo/` 迁移而来的，包含以下文件：

- `llm_client_utils.py` → `src/shared_bs_llm/llm/client_utils.py`
- `llm_client_enum.py` → `src/shared_bs_llm/llm/client_enum.py`
- `llm_model.py` → `src/shared_bs_llm/llm/model_config.py`
- `snow_flake.py` → `src/shared_bs_llm/llm/snow_flake.py`

### 导入路径变更

**迁移前：**
```python
from src.api.demo.llm_client_utils import LLMClientUtils
from src.api.demo.llm_client_enum import LLMClientEnum
from src.api.demo.llm_model import LLMNacosConfig
```

**迁移后：**
```python
from shared_bs_llm.llm import LLMClientUtils, LLMClientEnum, LLMNacosConfig
```

## 🏗️ 开发

### 添加新功能

1. 在 `src/shared_bs_llm/` 下添加新模块
2. 更新 `__init__.py` 文件导出新功能
3. 添加相应的测试文件
4. 更新文档

### 发布新版本

1. 更新 `pyproject.toml` 中的版本号
2. 运行测试确保所有功能正常
3. 构建并发布

## 📄 许可证

本项目遵循 YAI Investor Insight 项目的许可证。