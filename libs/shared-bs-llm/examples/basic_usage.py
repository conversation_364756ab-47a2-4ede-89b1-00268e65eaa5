#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python Shared Libraries 基本使用示例

这个示例展示了如何使用迁移后的 Python Shared Libraries。
"""

import os
import sys
from pathlib import Path

# 添加 shared-bs-llm 到路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from shared_bs_llm.llm import LLMClientUtils, LLMClientEnum, LLMNacosConfig, Snowflake
from shared_bs_llm.utils import setup_logger


def main():
    """主函数"""
    print("🚀 Python Shared Libraries 使用示例")
    print("=" * 50)
    
    # 1. 测试 Snowflake ID 生成器
    print("\n1. 测试 Snowflake ID 生成器")
    sf = Snowflake(worker_id=1, datacenter_id=1)
    for i in range(3):
        snowflake_id = sf.generate()
        print(f"   生成的 ID {i+1}: {snowflake_id}")
    
    # 2. 测试 LLM 枚举
    print("\n2. 测试 LLM 枚举")
    print(f"   ZHIPU_GLM_4 模型名: {LLMClientEnum.ZHIPU_GLM_4.model_name}")
    print(f"   ZHIPU_GLM_4 LLM类型: {LLMClientEnum.ZHIPU_GLM_4.llm}")
    print(f"   QWEN_32B 描述: {LLMClientEnum.QWEN_32B.description}")
    
    # 3. 测试配置管理
    print("\n3. 测试配置管理")
    config = LLMNacosConfig.get_instance()
    available_llms = config.get_available_llms()
    print(f"   可用的 LLM: {available_llms}")
    
    if available_llms:
        print("   配置详情:")
        for llm_type in available_llms[:3]:  # 只显示前3个
            llm_config = config.get_config(llm_type)
            if llm_config:
                print(f"     {llm_type}: {llm_config.api_base}")
    else:
        print("   ⚠️  没有配置任何 LLM API 密钥")
        print("   💡 提示: 设置环境变量如 OPENAI_API_KEY, ZHIPU_API_KEY 等")
    
    # 4. 测试工具函数
    print("\n4. 测试工具函数")
    logger = setup_logger("example")
    logger.info("这是一个测试日志消息")
    
    # 5. 如果有配置，尝试创建 LLM 实例
    if available_llms:
        print("\n5. 测试 LLM 实例创建")
        try:
            # 使用第一个可用的 LLM 类型
            test_enum = None
            for enum_item in LLMClientEnum:
                if enum_item.llm in available_llms:
                    test_enum = enum_item
                    break
            
            if test_enum:
                print(f"   尝试创建 {test_enum.description} 实例...")
                llm = LLMClientUtils.create_llm(
                    model_type=test_enum,
                    streaming=False,
                    temperature=0.7
                )
                print(f"   ✅ 成功创建 LLM 实例: {type(llm).__name__}")
            else:
                print("   ⚠️  没有找到匹配的 LLM 枚举")
                
        except Exception as e:
            print(f"   ❌ 创建 LLM 实例失败: {str(e)}")
    
    print("\n🎉 示例运行完成！")
    print("\n📚 更多信息:")
    print("   - 查看 libs/shared-bs-llm/src/shared_bs_llm/ 了解所有可用模块")
    print("   - 查看 libs/shared-bs-llm/tests/ 了解测试用例")
    print("   - 在其他 Python 项目中使用: from shared_bs_llm.llm import ...")


if __name__ == "__main__":
    main()