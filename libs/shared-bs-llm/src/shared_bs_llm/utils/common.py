#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Common utility functions

This module contains common utility functions that can be used
across different parts of the application.
"""

from typing import Any, Optional
import logging


def setup_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """
    Setup a logger with the given name and level
    
    Args:
        name: Logger name
        level: Logging level
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger


def safe_get(obj: Any, key: str, default: Any = None) -> Any:
    """
    Safely get a value from an object
    
    Args:
        obj: Object to get value from
        key: Key to get
        default: Default value if key not found
        
    Returns:
        Value or default
    """
    try:
        if hasattr(obj, key):
            return getattr(obj, key)
        elif hasattr(obj, '__getitem__'):
            return obj[key]
        else:
            return default
    except (KeyError, AttributeError, TypeError):
        return default