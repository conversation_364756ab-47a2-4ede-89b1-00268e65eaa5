#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM Model Configuration

Configuration classes for various LLM providers and Langfuse integration.
Migrated from apps/api-server/src/api/demo/llm_model.py

@author: <PERSON>
@date: 2025/4/14
@version: 0.0.1
"""

import os
from typing import Optional, Dict
from pydantic import Field, BaseModel
from dotenv import load_dotenv
from pydantic.fields import PrivateAttr

# 加载环境变量
load_dotenv()


class LangfuseConfig(BaseModel):
    """Langfuse配置类"""
    public_key: str = Field(default="", description="public_key")
    secret_key: str = Field(default="", description="secret_key")
    url: str = Field(default="", description="url")


class LLMConfig(BaseModel):
    """LLM配置类"""
    api_key: str = Field(default="", description="api_key")
    api_base: str = Field(default="", description="api_base")


class LLMNacosConfig(BaseModel):
    """基于环境变量的LLM配置类"""
    
    # 类级别单例实例
    _instance = None
    
    # LLM配置映射 - 使用 PrivateAttr
    _llm_configs: Dict[str, Optional[LLMConfig]] = PrivateAttr(default_factory=dict)
    
    # LLM默认API基础URL映射
    _default_api_bases = {
        "zhipu": "https://open.bigmodel.cn/api/paas/v4/",
        "doubao": "https://ark.cn-beijing.volces.com/api/v3/",
        "qwen": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "openai": "https://api.openai.com/v1",
        "deepseek": "https://api.deepseek.com/v1",
        "openrouter": "https://openrouter.ai/api/v1",
        "anthropic": "https://api.anthropic.com",
        "claude": "https://openrouter.ai/api/v1",  # Claude通过OpenRouter
        "perplexity": "https://api.perplexity.ai"
    }
    
    def __init__(self, **data):
        super().__init__(**data)
        self._load_from_env()
    
    @classmethod
    def get_instance(cls) -> 'LLMNacosConfig':
        """获取单例实例"""
        if not hasattr(cls, '_singleton_instance'):
            cls._singleton_instance = cls()
        return cls._singleton_instance
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 遍历所有支持的LLM类型
        for llm_type in self._default_api_bases.keys():
            # 构造环境变量名（大写）
            api_key_env = f"{llm_type.upper()}_API_KEY"
            api_base_env = f"{llm_type.upper()}_BASE_URL"
            
            # 获取API密钥
            api_key = os.getenv(api_key_env)
            if api_key:
                # 获取API基础URL，如果没有设置则使用默认值
                api_base = os.getenv(api_base_env, self._default_api_bases[llm_type])
                
                # 创建配置
                self._llm_configs[llm_type] = LLMConfig(
                    api_key=api_key,
                    api_base=api_base
                )
            else:
                self._llm_configs[llm_type] = None
    
    def get_config(self, llm_type: str) -> Optional[LLMConfig]:
        """获取指定LLM类型的配置"""
        return self._llm_configs.get(llm_type)
    
    # 为了兼容性，保留原有的属性访问方式
    @property
    def zhipu(self) -> Optional[LLMConfig]:
        return self.get_config("zhipu")
    
    @property
    def doubao(self) -> Optional[LLMConfig]:
        return self.get_config("doubao")
    
    @property
    def qwen(self) -> Optional[LLMConfig]:
        return self.get_config("qwen")
    
    @property
    def openai(self) -> Optional[LLMConfig]:
        return self.get_config("openai")
    
    @property
    def deepseek(self) -> Optional[LLMConfig]:
        return self.get_config("deepseek")
    
    @property
    def openrouter(self) -> Optional[LLMConfig]:
        return self.get_config("openrouter")
    
    @property
    def anthropic(self) -> Optional[LLMConfig]:
        return self.get_config("anthropic")
    
    @property
    def claude(self) -> Optional[LLMConfig]:
        # Claude优先使用OpenRouter，如果没有则使用Anthropic
        return self.get_config("openrouter") or self.get_config("anthropic")
    
    @property
    def perplexity(self) -> Optional[LLMConfig]:
        return self.get_config("perplexity")
    
    def get_available_llms(self) -> list[str]:
        """获取可用的LLM列表"""
        return [llm_type for llm_type, config in self._llm_configs.items() if config is not None]
    
    def get_primary_llm(self) -> Optional[LLMConfig]:
        """获取主要的LLM配置（优先级：OpenRouter > OpenAI > 其他）"""
        priority_order = ["openrouter", "openai", "zhipu", "qwen", "doubao", "deepseek", "anthropic"]
        
        for llm_type in priority_order:
            config = self.get_config(llm_type)
            if config is not None:
                return config
        
        return None
    
    def add_llm_type(self, llm_type: str, default_api_base: str):
        """动态添加新的LLM类型支持"""
        self._default_api_bases[llm_type] = default_api_base
        
        # 立即检查环境变量
        api_key_env = f"{llm_type.upper()}_API_KEY"
        api_base_env = f"{llm_type.upper()}_BASE_URL"
        
        api_key = os.getenv(api_key_env)
        if api_key:
            api_base = os.getenv(api_base_env, default_api_base)
            self._llm_configs[llm_type] = LLMConfig(
                api_key=api_key,
                api_base=api_base
            )
        else:
            self._llm_configs[llm_type] = None