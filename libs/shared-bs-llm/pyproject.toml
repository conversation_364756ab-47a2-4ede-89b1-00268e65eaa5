[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "shared-bs-llm"
version = "0.1.0"
description = "Python shared libraries for YAI Investor Insight"
requires-python = ">=3.11,<3.13"
dependencies = [
    "langchain-openai>=0.1.0",
    "langfuse>=2.0.0",
    "pydantic>=2.0.0"
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-asyncio"
]

[tool.hatch.build.targets.wheel]
packages = ["src/shared_bs_llm"]