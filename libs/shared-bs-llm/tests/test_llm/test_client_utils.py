#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM Client Utils 测试
"""

import pytest
import os
from unittest.mock import patch, MagicMock

from shared_bs_llm.llm import LLMClientUtils, LLMClientEnum, LLMNacosConfig, Snowflake


class TestSnowflake:
    """Snowflake ID生成器测试"""
    
    def test_snowflake_init(self):
        """测试Snowflake初始化"""
        sf = Snowflake(worker_id=1, datacenter_id=1)
        assert sf.worker_id == 1
        assert sf.datacenter_id == 1
        assert sf.sequence == 0
    
    def test_snowflake_generate(self):
        """测试ID生成"""
        sf = Snowflake(worker_id=0, datacenter_id=0)
        id1 = sf.generate()
        id2 = sf.generate()
        
        assert len(id1) == 16
        assert len(id2) == 16
        assert id1 != id2
    
    def test_snowflake_invalid_worker_id(self):
        """测试无效的worker_id"""
        with pytest.raises(ValueError):
            Snowflake(worker_id=32, datacenter_id=0)
    
    def test_snowflake_invalid_datacenter_id(self):
        """测试无效的datacenter_id"""
        with pytest.raises(ValueError):
            Snowflake(worker_id=0, datacenter_id=32)


class TestLLMClientEnum:
    """LLM客户端枚举测试"""
    
    def test_enum_values(self):
        """测试枚举值"""
        assert LLMClientEnum.ZHIPU_GLM_4.model_name == "glm-4"
        assert LLMClientEnum.ZHIPU_GLM_4.llm == "zhipu"
        assert "智谱AI" in LLMClientEnum.ZHIPU_GLM_4.description
    
    def test_get_model_name(self):
        """测试获取模型名称"""
        model_name = LLMClientEnum.get_model_name(LLMClientEnum.QWEN_32B)
        assert model_name == "qwen2.5-32b-instruct"
    
    def test_get_model_config_key(self):
        """测试获取模型配置键"""
        config_key = LLMClientEnum.get_model_config_key(LLMClientEnum.DOUBAO_1_5_PRO_256K)
        assert config_key == "doubao"


class TestLLMNacosConfig:
    """LLM配置测试"""
    
    @patch.dict(os.environ, {
        'ZHIPU_API_KEY': 'test_zhipu_key',
        'QWEN_API_KEY': 'test_qwen_key',
        'OPENAI_API_KEY': 'test_openai_key'
    })
    def test_config_loading(self):
        """测试配置加载"""
        # 重置单例
        LLMNacosConfig._instance = None
        
        config = LLMNacosConfig.get_instance()
        
        assert config.zhipu is not None
        assert config.zhipu.api_key == 'test_zhipu_key'
        assert config.qwen is not None
        assert config.qwen.api_key == 'test_qwen_key'
        assert config.openai is not None
        assert config.openai.api_key == 'test_openai_key'
    
    @patch.dict(os.environ, {
        'OPENROUTER_API_KEY': 'test_openrouter_key',
        'ZHIPU_API_KEY': 'test_zhipu_key'
    })
    def test_get_primary_llm(self):
        """测试获取主要LLM"""
        # 重置单例
        LLMNacosConfig._instance = None
        
        config = LLMNacosConfig.get_instance()
        primary = config.get_primary_llm()
        
        # OpenRouter应该是优先级最高的
        assert primary is not None
        assert primary.api_key == 'test_openrouter_key'
    
    @patch.dict(os.environ, {
        'ZHIPU_API_KEY': 'test_zhipu_key',
        'QWEN_API_KEY': 'test_qwen_key'
    })
    def test_get_available_llms(self):
        """测试获取可用LLM列表"""
        # 重置单例
        LLMNacosConfig._instance = None
        
        config = LLMNacosConfig.get_instance()
        available = config.get_available_llms()
        
        assert 'zhipu' in available
        assert 'qwen' in available
        assert len(available) >= 2


class TestLLMClientUtils:
    """LLM客户端工具测试"""
    
    @patch.dict(os.environ, {
        'ZHIPU_API_KEY': 'test_zhipu_key'
    })
    @patch('shared_bs_llm.llm.client_utils.ChatOpenAI')
    def test_create_llm(self, mock_chat_openai):
        """测试创建LLM实例"""
        # 重置单例和缓存
        LLMNacosConfig._instance = None
        from shared_bs_llm.llm.client_utils import _llm_cache
        _llm_cache.clear()
        
        mock_llm = MagicMock()
        mock_chat_openai.return_value = mock_llm
        
        llm = LLMClientUtils.create_llm(
            model_type=LLMClientEnum.ZHIPU_GLM_4,
            streaming=False,
            temperature=0.7
        )
        
        assert llm is not None
        mock_chat_openai.assert_called_once()
    
    @patch.dict(os.environ, {})
    def test_create_llm_no_config(self):
        """测试无配置时创建LLM"""
        # 重置单例
        LLMNacosConfig._instance = None
        
        with pytest.raises(ValueError, match="No LLM configuration found"):
            LLMClientUtils.create_llm(
                model_type=LLMClientEnum.ZHIPU_GLM_4
            )


if __name__ == "__main__":
    pytest.main([__file__])