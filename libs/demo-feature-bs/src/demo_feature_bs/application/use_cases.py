"""应用用例"""

from typing import List
from ..domain.entities import MarketData
from ..domain.repositories import MarketDataRepository


class GetMarketDataUseCase:
    """获取市场数据用例"""
    
    def __init__(self, repository: MarketDataRepository):
        self._repository = repository
    
    async def execute(self) -> List[MarketData]:
        """执行获取市场数据用例"""
        return await self._repository.get_all_market_data()


class GetMarketDataBySymbolUseCase:
    """根据股票代码获取市场数据用例"""
    
    def __init__(self, repository: MarketDataRepository):
        self._repository = repository
    
    async def execute(self, symbol: str) -> MarketData:
        """执行根据股票代码获取市场数据用例"""
        return await self._repository.get_market_data_by_symbol(symbol)