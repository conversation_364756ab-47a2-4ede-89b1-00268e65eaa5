"""模拟数据仓储实现"""

from datetime import datetime
from typing import List
from ..domain.entities import MarketData
from ..domain.repositories import MarketDataRepository


class MockMarketDataRepository(MarketDataRepository):
    """模拟市场数据仓储实现"""
    
    def __init__(self):
        self._mock_data = [
            MarketData(
                symbol="AAPL",
                name="Apple Inc.",
                price=182.31,
                change=2.45,
                changePercent=1.36,
                volume=65432100,
                market_cap=2850000000000,
                updated_at=datetime.now()
            ),
            MarketData(
                symbol="MSFT",
                name="Microsoft Corp.",
                price=378.85,
                change=-1.23,
                changePercent=-0.32,
                volume=45321000,
                market_cap=2800000000000,
                updated_at=datetime.now()
            ),
            MarketData(
                symbol="GOOGL",
                name="Alphabet Inc.",
                price=2849.12,
                change=15.67,
                changePercent=0.55,
                volume=32145000,
                market_cap=1800000000000,
                updated_at=datetime.now()
            ),
            MarketData(
                symbol="TSLA",
                name="Tesla Inc.",
                price=243.84,
                change=-5.21,
                changePercent=-2.09,
                volume=87654300,
                market_cap=775000000000,
                updated_at=datetime.now()
            ),
        ]
    
    async def get_all_market_data(self) -> List[MarketData]:
        """获取所有市场数据"""
        return self._mock_data.copy()
    
    async def get_market_data_by_symbol(self, symbol: str) -> MarketData:
        """根据股票代码获取市场数据"""
        for data in self._mock_data:
            if data.symbol == symbol:
                return data
        raise ValueError(f"Market data not found for symbol: {symbol}")