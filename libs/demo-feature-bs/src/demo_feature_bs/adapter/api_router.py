"""API路由适配器"""

from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
from ..application.use_cases import GetMarketDataUseCase, GetMarketDataBySymbolUseCase
from ..infrastructure.mock_repository import MockMarketDataRepository


# 创建路由器
router = APIRouter(prefix="/api/plugins/demo-feature", tags=["demo-feature"])

# 创建依赖
repository = MockMarketDataRepository()
get_market_data_use_case = GetMarketDataUseCase(repository)
get_market_data_by_symbol_use_case = GetMarketDataBySymbolUseCase(repository)


@router.get("/market-data", response_model=List[Dict[str, Any]])
async def get_market_data():
    """获取市场数据"""
    try:
        market_data = await get_market_data_use_case.execute()
        return [data.dict() for data in market_data]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/market-data/{symbol}", response_model=Dict[str, Any])
async def get_market_data_by_symbol(symbol: str):
    """根据股票代码获取市场数据"""
    try:
        market_data = await get_market_data_by_symbol_use_case.execute(symbol)
        return market_data.dict()
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))