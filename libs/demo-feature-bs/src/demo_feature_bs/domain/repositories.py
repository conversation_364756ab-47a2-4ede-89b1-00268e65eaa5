"""领域仓储接口"""

from abc import ABC, abstractmethod
from typing import List
from .entities import MarketData


class MarketDataRepository(ABC):
    """市场数据仓储接口"""
    
    @abstractmethod
    async def get_all_market_data(self) -> List[MarketData]:
        """获取所有市场数据"""
        pass
    
    @abstractmethod
    async def get_market_data_by_symbol(self, symbol: str) -> MarketData:
        """根据股票代码获取市场数据"""
        pass