// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// 任务优先级枚举
export enum TaskPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 分析类型枚举
export enum AnalysisType {
  STOCK_ANALYSIS = 'stock_analysis',
  MARKET_TREND = 'market_trend',
  FINANCIAL_REPORT = 'financial_report',
  NEWS_SENTIMENT = 'news_sentiment'
}

// 基础任务接口
export interface BaseTask {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
  worker_id?: string;
  retry_count: number;
  max_retries: number;
  last_heartbeat?: string;
}

// 分析任务接口
export interface AnalysisTask extends BaseTask {
  analysis_type: AnalysisType;
  input_data: Record<string, unknown>;
  result?: AnalysisResult;
  ai_tools_used?: string[];
  processing_steps?: ProcessingStep[];
}

// 分析结果接口
export interface AnalysisResult {
  summary: string;
  details: Record<string, unknown>;
  confidence_score: number;
  recommendations?: string[];
  charts_data?: ChartData[];
  created_at: string;
}

// 处理步骤接口
export interface ProcessingStep {
  step_name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  started_at?: string;
  completed_at?: string;
  result?: unknown;
  error_message?: string;
}

// 图表数据接口
export interface ChartData {
  type: 'line' | 'bar' | 'pie' | 'candlestick';
  title: string;
  data: unknown[];
  config?: Record<string, unknown>;
}

// API 响应接口
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

// 分页接口
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// SSE 事件接口
export interface SSEEvent {
  type: 'task_update' | 'task_created' | 'task_completed' | 'task_failed' | 'heartbeat';
  data: unknown;
  timestamp: string;
}

// 用户接口
export interface User {
  id: string;
  email: string;
  name?: string;
  created_at: string;
  updated_at: string;
}

// 股票信息接口
export interface StockInfo {
  symbol: string;
  name: string;
  price: number;
  change: number;
  change_percent: number;
  volume: number;
  market_cap?: number;
  updated_at: string;
}

// 新闻信息接口
export interface NewsItem {
  id: string;
  title: string;
  content: string;
  source: string;
  url: string;
  published_at: string;
  sentiment_score?: number;
  relevance_score?: number;
}

// 财务数据接口
export interface FinancialData {
  symbol: string;
  period: string;
  revenue: number;
  profit: number;
  eps: number;
  pe_ratio?: number;
  debt_to_equity?: number;
  roe?: number;
  reported_at: string;
}

