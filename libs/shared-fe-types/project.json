{"name": "shared-fe-types", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared-fe-types/src", "projectType": "library", "tags": ["type:lib", "scope:shared"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "libs/shared-fe-types/dist", "main": "libs/shared-fe-types/src/index.ts", "tsConfig": "libs/shared-fe-types/tsconfig.json", "assets": []}}, "type-check": {"executor": "@nx/js:tsc", "options": {"tsConfig": "libs/shared-fe-types/tsconfig.json", "noEmit": true}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/shared-fe-types/**/*.ts"]}}}}