{"name": "@yai-investor-insight/api-client", "version": "1.0.0", "description": "API client library for YAI Investor Insight platform", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "keywords": ["typescript", "api-client", "fetch", "investor-insight"], "author": "YAI Team", "license": "MIT", "dependencies": {"@yai-investor-insight/shared-types": "workspace:*", "tslib": "^2.6.0"}, "devDependencies": {"typescript": "^5.0.0"}, "publishConfig": {"access": "restricted"}}