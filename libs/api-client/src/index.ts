import {
  AnalysisTask,
  ApiResponse,
  PaginatedResponse,
  SSEEvent,
  TaskStatus,
  AnalysisType,
  TaskPriority
} from '@yai-investor-insight/shared-types';

// API 客户端配置
export interface ApiClientConfig {
  baseUrl: string;
  timeout?: number;
  headers?: Record<string, string>;
}

// API 客户端类
export class ApiClient {
  private baseUrl: string;
  private timeout: number;
  private headers: Record<string, string>;

  constructor(config: ApiClientConfig) {
    this.baseUrl = config.baseUrl.replace(/\/$/, '');
    this.timeout = config.timeout || 30000;
    this.headers = {
      'Content-Type': 'application/json',
      ...config.headers
    };
  }

  // 通用请求方法
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.headers,
          ...options.headers
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  // GET 请求
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  // POST 请求
  async post<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  // PUT 请求
  async put<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  // DELETE 请求
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // 任务相关 API
  async getTasks(params?: {
    status?: TaskStatus;
    page?: number;
    page_size?: number;
  }): Promise<ApiResponse<PaginatedResponse<AnalysisTask>>> {
    const searchParams = new URLSearchParams();
    if (params?.status) searchParams.set('status', params.status);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.page_size) searchParams.set('page_size', params.page_size.toString());
    
    const query = searchParams.toString();
    return this.get(`/api/tasks${query ? `?${query}` : ''}`);
  }

  async getTask(taskId: string): Promise<ApiResponse<AnalysisTask>> {
    return this.get(`/api/tasks/${taskId}`);
  }

  async createTask(task: {
    title: string;
    description?: string;
    analysis_type: AnalysisType;
    input_data: Record<string, unknown>;
    priority?: TaskPriority;
  }): Promise<ApiResponse<AnalysisTask>> {
    return this.post('/api/tasks', task);
  }

  async updateTask(taskId: string, updates: Partial<AnalysisTask>): Promise<ApiResponse<AnalysisTask>> {
    return this.put(`/api/tasks/${taskId}`, updates);
  }

  async deleteTask(taskId: string): Promise<ApiResponse<void>> {
    return this.delete(`/api/tasks/${taskId}`);
  }

  // 文件上传
  async uploadFile(file: File): Promise<ApiResponse<{ file_id: string; url: string }>> {
    const formData = new FormData();
    formData.append('file', file);

    // 创建不包含 Content-Type 的 headers，让浏览器自动设置
    const uploadHeaders = { ...this.headers };
    delete uploadHeaders['Content-Type'];

    const response = await fetch(`${this.baseUrl}/api/upload`, {
      method: 'POST',
      body: formData,
      headers: uploadHeaders
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }

    return response.json();
  }
}

// SSE 客户端类
export class SSEClient {
  private eventSource: EventSource | null = null;
  private listeners: Map<string, ((event: SSEEvent) => void)[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor(private url: string) {}

  // 连接 SSE
  connect(): void {
    if (this.eventSource) {
      this.disconnect();
    }

    this.eventSource = new EventSource(this.url);

    this.eventSource.onopen = () => {
      console.log('SSE connected');
      this.reconnectAttempts = 0;
    };

    this.eventSource.onmessage = (event) => {
      try {
        const sseEvent: SSEEvent = JSON.parse(event.data);
        this.emit(sseEvent.type, sseEvent);
      } catch (error) {
        console.error('Failed to parse SSE event:', error);
      }
    };

    this.eventSource.onerror = () => {
      console.error('SSE connection error');
      this.handleReconnect();
    };
  }

  // 断开连接
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }

  // 添加事件监听器
  on(eventType: string, listener: (event: SSEEvent) => void): void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType)!.push(listener);
  }

  // 移除事件监听器
  off(eventType: string, listener: (event: SSEEvent) => void): void {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  // 触发事件
  private emit(eventType: string, event: SSEEvent): void {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      listeners.forEach(listener => listener(event));
    }
  }

  // 处理重连
  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }
}

// 默认 API 客户端实例
export const createApiClient = (config: ApiClientConfig) => new ApiClient(config);
export const createSSEClient = (url: string) => new SSEClient(url);
