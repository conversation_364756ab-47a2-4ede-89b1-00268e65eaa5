{"name": "api-client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/api-client/src", "projectType": "library", "tags": ["type:lib", "scope:shared"], "targets": {"type-check": {"executor": "@nx/js:tsc", "options": {"tsConfig": "libs/api-client/tsconfig.json", "noEmit": true}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/api-client/**/*.ts"]}}}}