{"name": "demo-feature-fe", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/demo-feature-fe/src", "projectType": "library", "tags": ["scope:demo-feature", "type:fe"], "// targets": "to see all targets run: nx show project demo-feature-fe --web", "targets": {"type-check": {"executor": "@nx/js:tsc", "options": {"tsConfig": "libs/demo-feature-fe/tsconfig.json", "noEmit": true}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/demo-feature-fe/**/*.{ts,tsx}"]}}}}