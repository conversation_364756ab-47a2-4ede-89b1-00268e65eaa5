'use server';

import { MarketData } from '../types';

/**
 * Server Actions for market data
 * 在服务器端执行API调用
 */

const API_BASE_URL = process.env.API_URL || 'http://localhost:8000';

export async function getMarketDataAction(): Promise<MarketData[]> {
  try {
    console.log('Fetching market data from:', `${API_BASE_URL}/api/plugins/demo-feature/market-data`);
    
    const response = await fetch(`${API_BASE_URL}/api/plugins/demo-feature/market-data`, {
      cache: 'no-store', // 确保每次都获取最新数据
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    // 验证数据格式
    if (!Array.isArray(data)) {
      console.error('Invalid data format:', data);
      throw new Error('Invalid market data format');
    }

    return data as MarketData[];
  } catch (error) {
    console.error('Failed to fetch market data:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch market data');
  }
}

export async function getMarketDataBySymbolAction(symbol: string): Promise<MarketData> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/plugins/demo-feature/market-data/${symbol}`, {
      cache: 'no-store',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data as MarketData;
  } catch (error) {
    console.error(`Failed to fetch market data for ${symbol}:`, error);
    throw new Error(error instanceof Error ? error.message : `Failed to fetch data for ${symbol}`);
  }
}