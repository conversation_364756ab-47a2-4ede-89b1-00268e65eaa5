import { revalidatePath } from 'next/cache';
import { MarketOverview } from '../components/market-overview';

interface DemoPageProps {
  className?: string;
}

async function refreshDataAction() {
  'use server';
  revalidatePath('/demo-feature');
}

export function DemoPage({ className }: DemoPageProps) {

  return (
    <div className={`container mx-auto px-4 py-8 ${className || ''}`}>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          演示功能页面
        </h1>
        <p className="text-gray-600">
          这是一个演示插件式架构的试点功能模块，展示了前后端分离和组件复用的能力。
        </p>
      </div>

      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">实时市场数据</h2>
        <form action={refreshDataAction}>
          <button 
            type="submit"
            className="px-4 py-2 text-sm border rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            刷新数据
          </button>
        </form>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <MarketOverview />
        
        <div className="space-y-4">
          <div className="p-6 bg-blue-50 rounded-lg">
            <h3 className="text-lg font-medium text-blue-900 mb-2">
              架构特点
            </h3>
            <ul className="text-blue-800 space-y-1 text-sm">
              <li>• 使用共享 UI 组件库 (shared-fe-kit)</li>
              <li>• 异步数据获取与状态管理</li>
              <li>• 响应式布局设计</li>
              <li>• TypeScript 类型安全</li>
              <li>• Next.js 原生编译，无需预构建</li>
            </ul>
          </div>
          
          <div className="p-6 bg-green-50 rounded-lg">
            <h3 className="text-lg font-medium text-green-900 mb-2">
              模块化优势
            </h3>
            <ul className="text-green-800 space-y-1 text-sm">
              <li>• 独立开发和测试</li>
              <li>• 清晰的依赖边界</li>
              <li>• 可复用的组件设计</li>
              <li>• 易于维护和扩展</li>
              <li>• 支持热重载开发</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};