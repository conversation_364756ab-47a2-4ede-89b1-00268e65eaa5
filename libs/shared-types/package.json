{"name": "@yai-investor-insight/shared-types", "version": "1.0.0", "description": "Shared TypeScript types for YAI Investor Insight platform", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "keywords": ["typescript", "types", "shared", "investor-insight"], "author": "YAI Team", "license": "MIT", "dependencies": {"tslib": "^2.6.0"}, "devDependencies": {"typescript": "^5.0.0"}, "publishConfig": {"access": "restricted"}}