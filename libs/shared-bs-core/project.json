{"name": "shared-bs-core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/shared-bs-core/src", "tags": ["scope:shared", "type:bs"], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "cd libs/shared-bs-core && python -m build", "cwd": "{workspaceRoot}"}}, "test": {"executor": "nx:run-commands", "options": {"command": "cd libs/shared-bs-core && python -m pytest", "cwd": "{workspaceRoot}"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "cd libs/shared-bs-core && ruff check .", "cwd": "{workspaceRoot}"}}, "format": {"executor": "nx:run-commands", "options": {"command": "cd libs/shared-bs-core && ruff format .", "cwd": "{workspaceRoot}"}}, "type-check": {"executor": "nx:run-commands", "options": {"command": "cd libs/shared-bs-core && mypy .", "cwd": "{workspaceRoot}"}}}}