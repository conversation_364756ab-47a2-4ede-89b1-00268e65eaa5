"""Dependency injection container for service management."""

import inspect
from typing import Any, Dict, TypeVar, Type, Callable, Union
from functools import wraps

T = TypeVar('T')


class Container:
    """Simple dependency injection container."""
    
    def __init__(self) -> None:
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable[[], Any]] = {}
    
    def register(self, interface: Type[T], implementation: Union[T, Callable[[], T]]) -> None:
        """Register a service or factory in the container."""
        key = self._get_service_key(interface)
        
        if callable(implementation) and not inspect.isclass(implementation):
            # It's a factory function
            self._factories[key] = implementation
        else:
            # It's an instance or class
            self._services[key] = implementation
    
    def get(self, interface: Type[T]) -> T:
        """Get a service from the container."""
        key = self._get_service_key(interface)
        
        # Check if we have a cached instance
        if key in self._services:
            service = self._services[key]
            if inspect.isclass(service):
                # Instantiate the class
                instance = service()
                self._services[key] = instance
                return instance
            return service
        
        # Check if we have a factory
        if key in self._factories:
            instance = self._factories[key]()
            self._services[key] = instance
            return instance
        
        raise ValueError(f"Service {interface.__name__} not registered")
    
    def clear(self) -> None:
        """Clear all registered services."""
        self._services.clear()
        self._factories.clear()
    
    @staticmethod
    def _get_service_key(interface: Type[Any]) -> str:
        """Get a unique key for the service interface."""
        return f"{interface.__module__}.{interface.__name__}"


# Global container instance
_container = Container()


def inject(interface: Type[T]) -> T:
    """Inject a service dependency."""
    return _container.get(interface)


def register_service(interface: Type[T], implementation: Union[T, Callable[[], T]]) -> None:
    """Register a service in the global container."""
    _container.register(interface, implementation)


def dependency(interface: Type[T]) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator to inject dependencies into function parameters."""
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            if interface.__name__.lower() not in kwargs:
                kwargs[interface.__name__.lower()] = inject(interface)
            return func(*args, **kwargs)
        return wrapper
    return decorator