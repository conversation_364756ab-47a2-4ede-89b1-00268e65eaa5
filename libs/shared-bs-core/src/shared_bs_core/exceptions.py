"""Common exception classes for API services."""

from typing import Dict, Union, Optional


class BaseAPIException(Exception):
    """Base exception class for all API errors."""
    
    def __init__(
        self, 
        message: str, 
        status_code: int = 500,
        details: Optional[Dict[str, Union[str, int]]] = None
    ) -> None:
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(BaseAPIException):
    """Exception raised for validation errors."""
    
    def __init__(
        self, 
        message: str = "Validation failed", 
        details: Optional[Dict[str, Union[str, int]]] = None
    ) -> None:
        super().__init__(message, status_code=400, details=details)


class NotFoundError(BaseAPIException):
    """Exception raised when a resource is not found."""
    
    def __init__(
        self, 
        message: str = "Resource not found", 
        details: Optional[Dict[str, Union[str, int]]] = None
    ) -> None:
        super().__init__(message, status_code=404, details=details)


class UnauthorizedError(BaseAPIException):
    """Exception raised for unauthorized access."""
    
    def __init__(
        self, 
        message: str = "Unauthorized access", 
        details: Optional[Dict[str, Union[str, int]]] = None
    ) -> None:
        super().__init__(message, status_code=401, details=details)


class ForbiddenError(BaseAPIException):
    """Exception raised for forbidden access."""
    
    def __init__(
        self, 
        message: str = "Access forbidden", 
        details: Optional[Dict[str, Union[str, int]]] = None
    ) -> None:
        super().__init__(message, status_code=403, details=details)