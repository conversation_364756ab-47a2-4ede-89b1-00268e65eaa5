"""Database configuration and session management."""

import os
from typing import As<PERSON><PERSON>enerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from pydantic import BaseSettings


class DatabaseConfig(BaseSettings):
    """Database configuration settings."""
    
    database_url: str = os.getenv(
        "DATABASE_URL", 
        "postgresql+asyncpg://user:password@localhost:5432/yai_investor_insight"
    )
    echo_sql: bool = os.getenv("DATABASE_ECHO", "false").lower() == "true"
    pool_size: int = int(os.getenv("DATABASE_POOL_SIZE", "10"))
    max_overflow: int = int(os.getenv("DATABASE_MAX_OVERFLOW", "20"))


class Base(DeclarativeBase):
    """Base class for all database models."""
    pass


# Global database configuration
_db_config = DatabaseConfig()
_engine = create_async_engine(
    _db_config.database_url,
    echo=_db_config.echo_sql,
    pool_size=_db_config.pool_size,
    max_overflow=_db_config.max_overflow,
)
_session_factory = async_sessionmaker(
    _engine, 
    class_=AsyncSession,
    expire_on_commit=False
)


async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """Get an async database session."""
    async with _session_factory() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()