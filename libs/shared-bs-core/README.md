# Shared Backend Core (shared-bs-core)

后端核心工具库，为所有后端服务模块提供基础设施支持。

## 功能模块

### 数据库管理 (`database.py`)
- 数据库连接配置
- 异步会话管理
- SQLAlchemy Base 模型类

### 异常处理 (`exceptions.py`)
- 统一的 API 异常基类
- 常用的 HTTP 状态码异常类
- 结构化错误信息

### 依赖注入 (`container.py`)
- 轻量级的依赖注入容器
- 服务注册和获取
- 装饰器式依赖注入

### 日志系统 (`logger.py`)
- 统一的日志配置
- 文件和控制台双输出
- 日志轮转和备份

## 使用示例

```python
from shared_bs_core import (
    get_database_session,
    BaseAPIException,
    inject,
    register_service,
    get_logger
)

# 数据库会话
async with get_database_session() as db:
    # 数据库操作
    pass

# 异常处理
raise BaseAPIException("Something went wrong", status_code=400)

# 依赖注入
register_service(MyService, MyServiceImpl())
service = inject(MyService)

# 日志记录
logger = get_logger(__name__)
logger.info("Application started")
```